**~~项目背景：
本项目是通过价格监控的方式 ，监控合约BTCUSDT的价格变化，然后触发唤醒Agent交易员团队进行实时交易、仓库管理、风险应对,同时Agent团队主要是替代交易员人为盯盘，这样可以更有效的发现交易机会 ，更快、更精准的进行交易
注意：通过[price_monitor.py](src/price_monitor.py) 进行监控，此代码目前测试阶段，暂时未完成，里面的触发是推送企业微信消息，后续会进行完善进行唤醒交易团队
Agent交易员团队成员：
交易员
仓位管理员
风险控制
订单执行员
市场分析师
交易团队责任
1、被唤醒的交易员团队需要进行多周期K线技术面分析、消息面分析，寻找交易机会进行交易（每笔交易需要保存到数据库中）、仓位管理、风险应对等等，来保证账户资金的安全
2、查询前一日的复盘策略报告，对后续交易进行关键性指导 ，复盘通过查询近期的交易记录，有一个问题：是否需要将每一笔交易的过程全部保存下来，比如每一个Agent的输出输出，为复盘团队提供数据支撑 
3、Agent交易团队在模拟仓进行交易
4、每笔交易按照1-10U金额进行交易，根据情况选择交易的金额大小 限制10U是保证资金安全

Agent复盘团队：
Agent通过定时任务的方式进行当日交易情况的复盘，生成复盘结果，保存到数据库中~~**










          
用户询问如何启动这个项目，我需要查看项目的启动方式和配置文件，了解具体的启动步骤。
        
            
toolName: view_files
            
status: success
          
            
filePath: d:\JavaSoftware\GitProject\xua_agent\src\gold_agents\main.py
d:\JavaSoftware\GitProject\xua_agent\src\price_monitor.py
d:\JavaSoftware\GitProject\xua_agent\pyproject.toml
          
查看完整的main.py文件和环境配置文件，了解项目的完整启动流程。
        
            
toolName: view_files
            
status: success
          
            
filePath: d:\JavaSoftware\GitProject\xua_agent\src\gold_agents\main.py
d:\JavaSoftware\GitProject\xua_agent\.env_test
          
查看项目的依赖文件和README，了解完整的启动要求。
        
            
toolName: view_files
            
status: success
          
            
filePath: d:\JavaSoftware\GitProject\xua_agent\README.md
d:\JavaSoftware\GitProject\xua_agent\uv.lock
          
# 🚀 项目启动指南

这个项目包含两个主要组件：**价格监控系统**和**Agent交易团队**。以下是详细的启动步骤：

## 📋 前置要求

### 1. 环境要求
- Python 3.10 - 3.12
- MySQL数据库
- 网络连接（用于API调用）

### 2. 依赖安装
项目使用 `uv` 作为包管理器：

```bash
# 安装依赖
uv sync

# 或者使用pip安装
pip install -e .
```

### 3. 环境配置

#### 创建环境变量文件
复制 `.env_test` 为 `.env` 并配置：

```bash
cp .env_test .env
```

#### 配置环境变量
编辑 `.env` 文件，设置以下关键配置：

```env
# AI模型配置（选择其中一种）
# 选项1：使用阿里云DashScope
OPENAI_API_KEY="your-dashscope-api-key"
OPENAI_API_BASE="https://dashscope.aliyuncs.com/compatible-mode/v1"
OPENAI_MODEL_NAME="qwen-plus"

# 选项2：使用本地Ollama
MODEL="ollama/qwen2.5:3b"
API_BASE="http://localhost:11434"
OPENAI_API_KEY=""

# 选项3：使用OpenAI
OPENAI_API_KEY="your-openai-api-key"
OPENAI_MODEL_NAME="gpt-4"

# 数据库配置
DB_HOST="localhost"
DB_PORT="3306"
DB_USER="root"
DB_PASSWORD="your-password"
DB_NAME="trading_db"

# Bitget API配置（用于实际交易）
BITGET_API_KEY="your-bitget-api-key"
BITGET_SECRET_KEY="your-bitget-secret"
BITGET_PASSPHRASE="your-passphrase"

# 企业微信配置（用于通知）
WECOM_WEBHOOK_URL="your-wecom-webhook"
```

### 4. 数据库设置

创建MySQL数据库和必要的表：

```sql
CREATE DATABASE trading_db;
USE trading_db;

-- 创建策略表
CREATE TABLE strategies (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(255) NOT NULL,
    type VARCHAR(100) NOT NULL,
    content TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    expiry_time TIMESTAMP,
    INDEX idx_type_expiry (type, expiry_time)
);

-- 创建交易记录表
CREATE TABLE trades (
    id INT AUTO_INCREMENT PRIMARY KEY,
    symbol VARCHAR(20) NOT NULL,
    side VARCHAR(10) NOT NULL,
    amount DECIMAL(18,8),
    price DECIMAL(18,8),
    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 🎯 启动方式

### 方式1：启动价格监控系统（推荐先启动）

价格监控系统会实时监控BTCUSDT价格变化，当检测到交易机会时推送通知：

```bash
# 进入项目目录
cd d:\JavaSoftware\GitProject\xua_agent

# 启动价格监控
python src/price_monitor.py
```

**价格监控功能：**
- 实时监控BTCUSDT价格
- 智能触发机制（价格波动、趋势变化）
- 企业微信消息推送
- 智能冷却机制防止重复推送

### 方式2：启动Agent交易团队

```bash
# 方法1：使用项目脚本
uv run gold_agents

# 方法2：直接运行
python -m src.gold_agents.main

# 方法3：使用crewai命令
crewai run
```

**Agent团队包含：**
- 🧠 **市场分析师**：多维度市场分析
- 💼 **交易员**：制定交易决策
- 📊 **仓位管理员**：优化资金分配
- 🛡️ **风险控制员**：实时风险监控
- ⚡ **订单执行员**：精确执行交易

### 方式3：其他运行模式

```bash
# 训练模式
uv run train <iterations> <filename>

# 重放模式
uv run replay <task_id>

# 测试模式
uv run test <iterations> <model_name>
```

## 🔧 配置说明

### 交易配置
- **交易金额**：每笔1-10U（确保资金安全）
- **交易模式**：模拟仓交易
- **风险控制**：多层风险控制机制
- **数据存储**：所有交易过程保存到数据库

### 监控配置
- **监控标的**：BTCUSDT合约
- **触发条件**：价格波动、趋势变化
- **通知方式**：企业微信推送
- **冷却机制**：防止重复推送

## 📊 运行状态检查

### 检查价格监控
```bash
# 查看日志
tail -f logs/price_monitor.log
```

### 检查Agent团队
```bash
# 查看输出文件
ls output/
cat output/10_market_analysis_task.md
```

### 检查数据库
```sql
-- 查看策略
SELECT * FROM strategies ORDER BY created_at DESC LIMIT 5;

-- 查看交易记录
SELECT * FROM trades ORDER BY timestamp DESC LIMIT 10;
```

## ⚠️ 注意事项

1. **首次启动**：建议先启动价格监控系统，确保基础设施正常
2. **API配置**：确保所有API密钥正确配置
3. **网络连接**：需要稳定的网络连接访问交易所API
4. **资金安全**：当前为模拟交易，实盘交易需要额外配置
5. **日志监控**：定期检查日志文件，确保系统正常运行

## 🚨 故障排除

### 常见问题
1. **数据库连接失败**：检查数据库配置和网络连接
2. **API调用失败**：检查API密钥和网络连接
3. **模型调用失败**：检查AI模型配置和API额度
4. **WebSocket连接失败**：检查网络连接和防火墙设置

### 日志位置
- 价格监控日志：`logs/price_monitor.log`
- Agent输出：`output/` 目录
- 系统日志：控制台输出

现在你可以根据需要选择启动价格监控系统或Agent交易团队了！建议先启动价格监控系统进行测试。
        



