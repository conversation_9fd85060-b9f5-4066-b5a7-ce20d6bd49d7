#!/usr/bin/env python3
"""启动交易统计仪表板"""

import sys
import os
import webbrowser
import time
from threading import Timer

# 添加src目录到Python路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def open_browser():
    """延迟打开浏览器"""
    webbrowser.open('http://localhost:6229')

if __name__ == '__main__':
    print("=" * 50)
    print("交易统计仪表板启动中...")
    print("=" * 50)
    
    try:
        # 延迟2秒后自动打开浏览器
        Timer(2.0, open_browser).start()
        
        # 导入并启动Flask应用
        from trading_dashboard_server import app
        
        print("✓ 服务器启动成功")
        print("✓ 访问地址: http://localhost:6229")
        print("✓ 浏览器将自动打开...")
        print("✓ 按 Ctrl+C 停止服务器")
        print("=" * 50)
        
        app.run(host='0.0.0.0', port=6229, debug=False)
        
    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保已安装所需依赖:")
        print("pip install flask flask-cors sqlalchemy")
    except Exception as e:
        print(f"❌ 启动失败: {e}")
    finally:
        print("\n服务器已停止")