from pybitget.stream import BitgetWsClient, SubscribeReq, handel_error
from pybitget import logger
import json
api_key = "bg_44025411ed4ba2c0032ac4e2dff2b18e"
api_secret = "d30fa4f8e6d80462659ed7e525d489020413a4e9d11cd16a3c8f6ceeccd87500"
api_passphrase = "1137285095"
# 订单     {"action":"update","arg":{"instType":"sp","channel":"candle1m","instId":"BTCUSDT"},"data":[["1750407420000","106349","106349.01","106310","106310","9.2854276984"]],"ts":1750407443455}
# 订单     {"action":"snapshot","arg":{"instType":"USDT-FUTURES","channel":"orders","instId":"default"},"data":[{"accBaseVolume":"0.01","cTime":"1695718781129","clientOId":"1","feeDetail":[{"feeCoin":"USDT","fee":"-0.162003"}],"fillFee":"-0.162003","fillFeeCoin":"USDT","fillNotionalUsd":"270.005","fillPrice":"27000.5","baseVolume":"0.01","stpMode":"cancel_taker","fillTime":"1695718781146","force":"gtc","instId":"BTCUSDT","leverage":"20","marginCoin":"USDT","marginMode":"crossed","notionalUsd":"270","orderId":"1","orderType":"market","pnl":"0","posMode":"hedge_mode","posSide":"long","price":"0","priceAvg":"27000.5","reduceOnly":"no","side":"buy","size":"0.01","enterPointSource":"WEB","status":"filled","tradeScope":"T","tradeId":"1111111111","tradeSide":"open","presetStopSurplusPrice":"21.4","totalProfits":"11221.45","presetStopLossPrice":"21.5","cancelReason":"normal_cancel","uTime":"1695718781146"}],"ts":1695718781206}
def on_message(message):
    if isinstance(message, str):
        message = json.loads(message)
        channel=message.get("arg").get("channel")
        if channel=="candle1m":
            logger.info(f"k_line：{message}")
        elif channel=="orders":
            logger.info(f"order：{message}")







if __name__ == '__main__':
    data = """ {"action":"snapshot","arg":{"instType":"USDT-FUTURES","channel":"orders","instId":"default"},"data":[{"accBaseVolume":"0.01","cTime":"1695718781129","clientOId":"1","feeDetail":[{"feeCoin":"USDT","fee":"-0.162003"}],"fillFee":"-0.162003","fillFeeCoin":"USDT","fillNotionalUsd":"270.005","fillPrice":"27000.5","baseVolume":"0.01","stpMode":"cancel_taker","fillTime":"1695718781146","force":"gtc","instId":"BTCUSDT","leverage":"20","marginCoin":"USDT","marginMode":"crossed","notionalUsd":"270","orderId":"1","orderType":"market","pnl":"0","posMode":"hedge_mode","posSide":"long","price":"0","priceAvg":"27000.5","reduceOnly":"no","side":"buy","size":"0.01","enterPointSource":"WEB","status":"filled","tradeScope":"T","tradeId":"1111111111","tradeSide":"open","presetStopSurplusPrice":"21.4","totalProfits":"11221.45","presetStopLossPrice":"21.5","cancelReason":"normal_cancel","uTime":"1695718781146"}],"ts":1695718781206} """
    # data = """ {"action":"update","arg":{"instType":"sp","channel":"candle1m","instId":"BTCUSDT"},"data":[["1750407420000","106349","106349.01","106310","106310","9.2854276984"]],"ts":1750407443455} """


    # Auth subscribe
    client = BitgetWsClient(api_key=api_key,
                            api_secret=api_secret,
                            passphrase=api_passphrase,
                            verbose=True) \
        .error_listener(handel_error) \
        .build()

    channels = [SubscribeReq("sp", "candle1m", "BTCUSDT"),SubscribeReq("SUSDT-FUTURES", "orders", "default")]
    client.subscribe(channels, on_message)



