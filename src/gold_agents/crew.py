from crewai import Agent, Crew, Process, Task, LLM
from crewai.project import CrewBase, agent, crew, task
# 导入实际使用的工具
from tools import (
    BitgetAccountTool,
    BitgetMarketTool,
    BitgetOrderTool,
    BitgetTradeTool,
    Jin10NewsTool,
    Kline15minKlineDataTool,
    Kline1hKlineDataTool,
    Kline4hKlineDataTool,
    Kline1dKlineDataTool,
    Jin10NewsSpeculativeSentimentReportTool,OrderHistoryDataTool,KlineKlineDataTool
)
# 导入事件监听器
from listeners.database_logger_listener import DatabaseLoggerListener

# 创建全局事件监听器实例
db_logger = DatabaseLoggerListener()


@CrewBase
class GoldAgents():
    """现货黄金多智能体策略分析系统"""
    agents_config = 'config/agents.yaml'
    tasks_config = 'config/tasks.yaml'

    llm = LLM(
        model="gemini/gemini-2.0-flash",  # gpt-4o, gemini-2.0-flash, anthropic/claude...
        api_key="AIzaSyAYY759q0BzkAni7dyXZnbDOBuIPE1lztc",
        temperature=0.7
    )
    # llm_2_5_flash = LLM(
    #     model="gemini/gemini-2.5-flash",  # gpt-4o, gemini-2.0-flash, anthropic/claude...
    #     api_key="AIzaSyAcQ_OHje9ZKX_eOvi-f6QXOFkciKi2Y9E",
    #     temperature=0.7
    # )
    # llm_2_5_flash_key2 = LLM(
    #     model="gemini/gemini-2.5-flash",  # gpt-4o, gemini-2.0-flash, anthropic/claude...
    #     api_key="AIzaSyDF5nGU8kDwXIdN8dq6kJj9MoXLIsB7AMQ",
    #     temperature=0.7
    # )
    
    def get_event_logger(self):
        """获取事件监听器实例"""
        return db_logger
    
    def close_logger(self):
        """关闭事件监听器会话"""
        db_logger.close_session()


    @agent
    def smart_trader(self) -> Agent:
        return Agent(
            config=self.agents_config['smart_trader'],
            tools=[
                BitgetAccountTool(),
                BitgetMarketTool(),
                BitgetTradeTool(),
                BitgetOrderTool()
            ],
            verbose=True,
            allow_delegation=False,
            llm=self.llm,
        )


    @agent
    def risk_manager(self) -> Agent:
        return Agent(
            config=self.agents_config['risk_manager'],
            tools=[
                BitgetAccountTool(),
                BitgetOrderTool(),
                BitgetTradeTool(),
                OrderHistoryDataTool()
            ],
            verbose=True,
            allow_delegation=False,
            llm=self.llm,
        )


    @agent
    def market_analyst(self) -> Agent:
        tools = [
            OrderHistoryDataTool(),
            BitgetMarketTool(),
            KlineKlineDataTool(),
            Jin10NewsTool(),
            Jin10NewsSpeculativeSentimentReportTool(),
        ]

        return Agent(
            config=self.agents_config['market_analyst'],
            tools=tools,
            verbose=True,
            allow_delegation=False,
            llm=self.llm,
        )

    # Define Tasks using the updated YAML config keys



    @task
    def market_analysis_task(self) -> Task:
        return Task(
            config=self.tasks_config['market_analysis_task'],
            agent=self.market_analyst(),
            output_file='output/0_market_analysis_task.md',
            async_execution=False
        )
    @task
    def risk_management_task(self) -> Task:
        return Task(
            config=self.tasks_config['risk_management_task'],
            agent=self.risk_manager(),
            output_file='output/1_risk_management_task.md',
            context=[ self.market_analysis_task()],
            async_execution=False
        )
    @task
    def smart_trading_task(self) -> Task:
        return Task(
            config=self.tasks_config['smart_trading_task'],
            agent=self.smart_trader(),
            context=[self.risk_management_task(), self.market_analysis_task()],
            output_file='output/3_smart_trading_task.md',
            async_execution=False
        )


    @crew
    def crew(self) -> Crew:
        """创建虚拟货币多智能体策略分析系统
        
        注意：事件监听器已自动激活，执行信息将记录到logs目录下的文件中
        """
        return Crew(
            agents=self.agents,
            tasks=self.tasks,
            process=Process.sequential,
            verbose=True,
            max_rpm=15
        )

