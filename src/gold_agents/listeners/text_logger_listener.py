from crewai.utilities.events import (
    CrewKickoffStartedEvent,
    CrewKickoffCompletedEvent,
    AgentExecutionStartedEvent,
    AgentExecutionCompletedEvent,
    ToolUsageStartedEvent,
    ToolUsageFinishedEvent,
    ToolUsageErrorEvent,
    TaskStartedEvent,
    TaskCompletedEvent
)
from crewai.utilities.events.base_event_listener import BaseEventListener
from datetime import datetime
import json
import os

class TextLoggerListener(BaseEventListener):
    """将CrewAI执行信息记录到本地文本文件的事件监听器"""
    
    def __init__(self, log_dir="logs"):
        super().__init__()
        self.log_dir = log_dir
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 确保日志目录存在
        os.makedirs(log_dir, exist_ok=True)
        
        # 创建会话日志文件
        self.log_file = os.path.join(log_dir, f"crew_execution_{self.session_id}.log")
        
        # 写入会话开始信息
        self._write_log("SESSION_START", {
            "session_id": self.session_id,
            "timestamp": datetime.now().isoformat(),
            "message": "CrewAI执行会话开始"
        })
        
    def _write_log(self, event_type, data):
        """写入日志到文件"""
        try:
            timestamp = datetime.now().isoformat()
            log_entry = {
                "timestamp": timestamp,
                "event_type": event_type,
                "data": data
            }
            
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(json.dumps(log_entry, ensure_ascii=False, indent=2) + "\n" + "="*80 + "\n")
                
            print(f"[LOG] {event_type}: {timestamp}")
            
        except Exception as e:
            print(f"写入日志失败: {str(e)}")
            
    def setup_listeners(self, crewai_event_bus):
        """设置事件监听器"""
        
        @crewai_event_bus.on(CrewKickoffStartedEvent)
        def on_crew_started(source, event):
            self._write_log("CREW_STARTED", {
                "crew_name": getattr(event, 'crew_name', 'Unknown'),
                "inputs": str(getattr(event, 'inputs', {})),
                "message": "Crew开始执行"
            })
            
        @crewai_event_bus.on(CrewKickoffCompletedEvent)
        def on_crew_completed(source, event):
            self._write_log("CREW_COMPLETED", {
                "crew_name": getattr(event, 'crew_name', 'Unknown'),
                "output": str(getattr(event, 'output', '')),
                "success": True,
                "message": "Crew执行完成"
            })
            
        @crewai_event_bus.on(TaskStartedEvent)
        def on_task_started(source, event):
            task_data = {
                "task_description": getattr(event, 'task_description', ''),
                "agent_role": getattr(event, 'agent_role', ''),
                "message": "任务开始执行"
            }
            
            # 尝试获取更多任务信息
            if hasattr(event, 'task'):
                task = event.task
                task_data.update({
                    "task_description": getattr(task, 'description', ''),
                    "expected_output": getattr(task, 'expected_output', ''),
                    "tools": [str(tool) for tool in getattr(task, 'tools', [])]
                })
                
            self._write_log("TASK_STARTED", task_data)
            
        @crewai_event_bus.on(TaskCompletedEvent)
        def on_task_completed(source, event):
            task_data = {
                "task_description": getattr(event, 'task_description', ''),
                "agent_role": getattr(event, 'agent_role', ''),
                "output": str(getattr(event, 'output', '')),
                "success": getattr(event, 'success', True),
                "message": "任务执行完成"
            }
            
            if hasattr(event, 'error'):
                task_data["error"] = str(event.error)
                
            self._write_log("TASK_COMPLETED", task_data)
            
        @crewai_event_bus.on(AgentExecutionStartedEvent)
        def on_agent_started(source, event):
            agent_data = {
                "message": "Agent开始执行"
            }
            
            # 尝试获取Agent信息
            if hasattr(event, 'agent'):
                agent = event.agent
                agent_data.update({
                    "agent_role": getattr(agent, 'role', ''),
                    "agent_goal": getattr(agent, 'goal', ''),
                    "agent_backstory": getattr(agent, 'backstory', ''),
                    "tools": [str(tool) for tool in getattr(agent, 'tools', [])]
                })
            
            # 获取任务信息
            if hasattr(event, 'task'):
                agent_data["task_description"] = getattr(event.task, 'description', '')
                
            self._write_log("AGENT_STARTED", agent_data)
            
        @crewai_event_bus.on(AgentExecutionCompletedEvent)
        def on_agent_completed(source, event):
            agent_data = {
                "output": str(getattr(event, 'output', '')),
                "success": getattr(event, 'success', True),
                "message": "Agent执行完成"
            }
            
            # 获取Agent信息
            if hasattr(event, 'agent'):
                agent = event.agent
                agent_data["agent_role"] = getattr(agent, 'role', '')
                
            if hasattr(event, 'error'):
                agent_data["error"] = str(event.error)
                
            self._write_log("AGENT_COMPLETED", agent_data)
            
        @crewai_event_bus.on(ToolUsageStartedEvent)
        def on_tool_started(source, event):
            tool_data = {
                "tool_name": getattr(event, 'tool_name', ''),
                "inputs": str(getattr(event, 'inputs', {})),
                "arguments": str(getattr(event, 'arguments', {})),
                "message": "工具开始执行"
            }
            
            # 尝试获取Agent信息
            if hasattr(event, 'agent'):
                tool_data["agent_role"] = getattr(event.agent, 'role', '')
            elif hasattr(event, 'agent_role'):
                tool_data["agent_role"] = event.agent_role
                
            self._write_log("TOOL_STARTED", tool_data)
            
        @crewai_event_bus.on(ToolUsageErrorEvent)
        def on_tool_error(source, event):
            tool_data = {
                "tool_name": getattr(event, 'tool_name', ''),
                "error": str(getattr(event, 'error', '')),
                "message": "工具执行错误"
            }
            
            if hasattr(event, 'agent'):
                tool_data["agent_role"] = getattr(event.agent, 'role', '')
            elif hasattr(event, 'agent_role'):
                tool_data["agent_role"] = event.agent_role
                
            self._write_log("TOOL_ERROR", tool_data)
            
        @crewai_event_bus.on(ToolUsageFinishedEvent)
        def on_tool_finished(source, event):
            tool_data = {
                "tool_name": getattr(event, 'tool_name', ''),
                "output": str(getattr(event, 'output', '')),
                "success": True,
                "message": "工具执行完成"
            }
            
            if hasattr(event, 'agent'):
                tool_data["agent_role"] = getattr(event.agent, 'role', '')
            elif hasattr(event, 'agent_role'):
                tool_data["agent_role"] = event.agent_role
                
            self._write_log("TOOL_FINISHED", tool_data)
            
    def close_session(self):
        """关闭会话，写入结束信息"""
        self._write_log("SESSION_END", {
            "session_id": self.session_id,
            "timestamp": datetime.now().isoformat(),
            "message": "CrewAI执行会话结束"
        })
        print(f"日志已保存到: {self.log_file}")