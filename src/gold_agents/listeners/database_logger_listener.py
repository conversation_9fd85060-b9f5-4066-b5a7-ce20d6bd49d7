from crewai.utilities.events import (
    CrewKickoffStartedEvent,
    CrewKickoffCompletedEvent,
    AgentExecutionStartedEvent,
    AgentExecutionCompletedEvent,
    ToolUsageStartedEvent,
    ToolUsageFinishedEvent,
    ToolUsageErrorEvent,
    TaskStartedEvent,
    TaskCompletedEvent
)
from crewai.utilities.events.base_event_listener import BaseEventListener
from datetime import datetime
import json
from sqlalchemy import create_engine, Column, Integer, String, DateTime, Text, Boolean, JSON, Index
from sqlalchemy.orm import sessionmaker, scoped_session, declarative_base
from sqlalchemy.exc import SQLAlchemyError
import yaml
import os

# 创建SQLAlchemy基类
Base = declarative_base()

class CrewAIExecutionLog(Base):
    """CrewAI执行日志模型"""
    __tablename__ = 'crewai_execution_logs'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    session_id = Column(String(50), nullable=False, index=True)
    batch_id = Column(String(50), nullable=False, index=True)
    timestamp = Column(DateTime, nullable=False, index=True)
    event_type = Column(String(50), nullable=False, index=True)
    event_data = Column(JSON)
    agent_role = Column(String(255))
    task_description = Column(Text)
    tool_name = Column(String(255))
    success = Column(Boolean)
    error_message = Column(Text)
    created_at = Column(DateTime, default=datetime.now)

class DatabaseLoggerListener(BaseEventListener):
    """将CrewAI执行信息记录到MySQL数据库的事件监听器"""
    
    def __init__(self, config_path="config/trader_config.yaml"):
        super().__init__()
        # 生成唯一的会话ID和批次ID
        self.session_id = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.batch_id = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:20]  # 包含微秒确保唯一性
        self.engine = None
        self.Session = None
        
        # 加载数据库配置
        self.db_config = self._load_db_config(config_path)
        
        # 初始化数据库连接和表
        self._init_database()
        
        # 记录会话开始
        self._write_log("SESSION_START", {
            "session_id": self.session_id,
            "timestamp": datetime.now().isoformat(),
            "message": "CrewAI执行会话开始"
        })
        
    def _load_db_config(self, config_path):
        """加载数据库配置"""
        try:
            # 获取配置文件的绝对路径
            if not os.path.isabs(config_path):
                # 假设配置文件在gold_agents目录下
                base_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
                config_path = os.path.join(base_dir, config_path)
            
            with open(config_path, 'r', encoding='utf-8') as file:
                config = yaml.safe_load(file)
                return config['database']
        except Exception as e:
            print(f"加载数据库配置失败: {str(e)}")
            # 返回默认配置
            return {
                "host": "*************",
                "port": "6221",
                "user": "Mysql5.7",
                "password": "a78d04a8027589c3",
                "name": "Mysql5.7"
            }
    
    def _init_database(self):
        """初始化数据库连接和创建表"""
        try:
            # 创建数据库连接字符串
            db_url = f"mysql+pymysql://{self.db_config['user']}:{self.db_config['password']}@{self.db_config['host']}:{self.db_config['port']}/{self.db_config['name']}?charset=utf8mb4"
            
            # 创建引擎
            self.engine = create_engine(
                db_url,
                echo=False,  # 设置为True可以看到SQL语句
                pool_pre_ping=True,  # 连接池预检查
                pool_recycle=3600,   # 连接回收时间
            )
            
            # 创建会话工厂
            self.Session = scoped_session(sessionmaker(bind=self.engine))
            
            # 创建表
            Base.metadata.create_all(self.engine)
            
            print(f"成功连接到MySQL数据库: {self.db_config['host']}")
            print("数据库表创建成功")
            
        except SQLAlchemyError as e:
            print(f"数据库连接失败: {str(e)}")
            self.engine = None
            self.Session = None
    

    
    def _write_log(self, event_type, data):
        """写入日志到数据库"""
        if not self.engine or not self.Session:
            print("数据库连接不可用，跳过日志记录")
            return
            
        session = None
        try:
            session = self.Session()
            timestamp = datetime.now()
            
            # 提取常用字段
            agent_role = data.get('agent_role', '') or None
            task_description = data.get('task_description', '') or None
            tool_name = data.get('tool_name', '') or None
            success = data.get('success', None)
            error_message = data.get('error', '') or None
            
            # 创建日志记录
            log_entry = CrewAIExecutionLog(
                session_id=self.session_id,
                batch_id=self.batch_id,
                timestamp=timestamp,
                event_type=event_type,
                event_data=data,
                agent_role=agent_role,
                task_description=task_description,
                tool_name=tool_name,
                success=success,
                error_message=error_message
            )
            
            session.add(log_entry)
            session.commit()
            
            print(f"[DB_LOG] {event_type}: {timestamp.isoformat()}")
            
        except SQLAlchemyError as e:
            print(f"写入数据库日志失败: {str(e)}")
            if session:
                session.rollback()
        finally:
            if session:
                session.close()
    
    def setup_listeners(self, crewai_event_bus):
        """设置事件监听器"""
        
        @crewai_event_bus.on(CrewKickoffStartedEvent)
        def on_crew_started(source, event):
            self._write_log("CREW_STARTED", {
                "crew_name": getattr(event, 'crew_name', 'Unknown'),
                "inputs": str(getattr(event, 'inputs', {})),
                "message": "Crew开始执行"
            })
            
        @crewai_event_bus.on(CrewKickoffCompletedEvent)
        def on_crew_completed(source, event):
            self._write_log("CREW_COMPLETED", {
                "crew_name": getattr(event, 'crew_name', 'Unknown'),
                "output": str(getattr(event, 'output', '')),
                "success": True,
                "message": "Crew执行完成"
            })
            
        @crewai_event_bus.on(TaskStartedEvent)
        def on_task_started(source, event):
            task_data = {
                "task_description": getattr(event, 'task_description', ''),
                "agent_role": getattr(event, 'agent_role', ''),
                "message": "任务开始执行"
            }
            
            # 尝试获取更多任务信息
            if hasattr(event, 'task'):
                task = event.task
                task_data.update({
                    "task_description": getattr(task, 'description', ''),
                    "expected_output": getattr(task, 'expected_output', ''),
                    "tools": [str(tool) for tool in getattr(task, 'tools', [])]
                })
                
            self._write_log("TASK_STARTED", task_data)
            
        @crewai_event_bus.on(TaskCompletedEvent)
        def on_task_completed(source, event):
            task_data = {
                "task_description": getattr(event, 'task_description', ''),
                "agent_role": getattr(event, 'agent_role', ''),
                "output": str(getattr(event, 'output', '')),
                "success": getattr(event, 'success', True),
                "message": "任务执行完成"
            }
            
            if hasattr(event, 'error'):
                task_data["error"] = str(event.error)
                
            self._write_log("TASK_COMPLETED", task_data)
            
        @crewai_event_bus.on(AgentExecutionStartedEvent)
        def on_agent_started(source, event):
            agent_data = {
                "message": "Agent开始执行"
            }
            
            # 尝试获取Agent信息
            if hasattr(event, 'agent'):
                agent = event.agent
                agent_data.update({
                    "agent_role": getattr(agent, 'role', ''),
                    "agent_goal": getattr(agent, 'goal', ''),
                    "agent_backstory": getattr(agent, 'backstory', ''),
                    "tools": [str(tool) for tool in getattr(agent, 'tools', [])]
                })
            
            # 获取任务信息
            if hasattr(event, 'task'):
                agent_data["task_description"] = getattr(event.task, 'description', '')
                
            self._write_log("AGENT_STARTED", agent_data)
            
        @crewai_event_bus.on(AgentExecutionCompletedEvent)
        def on_agent_completed(source, event):
            agent_data = {
                "output": str(getattr(event, 'output', '')),
                "success": getattr(event, 'success', True),
                "message": "Agent执行完成"
            }
            
            # 获取Agent信息
            if hasattr(event, 'agent'):
                agent = event.agent
                agent_data["agent_role"] = getattr(agent, 'role', '')
                
            if hasattr(event, 'error'):
                agent_data["error"] = str(event.error)
                
            self._write_log("AGENT_COMPLETED", agent_data)
            
        @crewai_event_bus.on(ToolUsageStartedEvent)
        def on_tool_started(source, event):
            tool_data = {
                "tool_name": getattr(event, 'tool_name', ''),
                "inputs": str(getattr(event, 'inputs', {})),
                "arguments": str(getattr(event, 'arguments', {})),
                "message": "工具开始执行"
            }
            
            # 尝试获取Agent信息
            if hasattr(event, 'agent'):
                tool_data["agent_role"] = getattr(event.agent, 'role', '')
            elif hasattr(event, 'agent_role'):
                tool_data["agent_role"] = event.agent_role
                
            self._write_log("TOOL_STARTED", tool_data)
            
        @crewai_event_bus.on(ToolUsageErrorEvent)
        def on_tool_error(source, event):
            tool_data = {
                "tool_name": getattr(event, 'tool_name', ''),
                "error": str(getattr(event, 'error', '')),
                "message": "工具执行错误"
            }
            
            if hasattr(event, 'agent'):
                tool_data["agent_role"] = getattr(event.agent, 'role', '')
            elif hasattr(event, 'agent_role'):
                tool_data["agent_role"] = event.agent_role
                
            self._write_log("TOOL_ERROR", tool_data)
            
        @crewai_event_bus.on(ToolUsageFinishedEvent)
        def on_tool_finished(source, event):
            tool_data = {
                "tool_name": getattr(event, 'tool_name', ''),
                "output": str(getattr(event, 'output', '')),
                "success": True,
                "message": "工具执行完成"
            }
            
            if hasattr(event, 'agent'):
                tool_data["agent_role"] = getattr(event.agent, 'role', '')
            elif hasattr(event, 'agent_role'):
                tool_data["agent_role"] = event.agent_role
                
            self._write_log("TOOL_FINISHED", tool_data)
    
    def close_session(self):
        """关闭数据库连接并记录会话结束"""
        try:
            # 记录会话结束
            self._write_log("session_ended", {
                "session_id": self.session_id,
                "end_time": datetime.now().isoformat(),
                "message": "CrewAI执行会话结束"
            })
            
            # 关闭数据库连接
            if self.Session:
                self.Session.remove()  # 清理scoped_session
            if self.engine:
                self.engine.dispose()  # 关闭连接池
                print(f"数据库连接已关闭，会话ID: {self.session_id}")
                
        except SQLAlchemyError as e:
            print(f"关闭数据库连接时出错: {str(e)}")
    
    def get_session_logs(self, session_id=None):
        """获取指定会话的日志记录"""
        if not self.engine or not self.Session:
            print("数据库连接不可用")
            return []
            
        session = None
        try:
            session = self.Session()
            
            if session_id is None:
                session_id = self.session_id
                
            # 使用SQLAlchemy ORM查询
            logs = session.query(CrewAIExecutionLog).filter(
                CrewAIExecutionLog.session_id == session_id
            ).order_by(CrewAIExecutionLog.timestamp.asc()).all()
            
            # 转换为字典格式
            result = []
            for log in logs:
                result.append({
                    'id': log.id,
                    'session_id': log.session_id,
                    'batch_id': log.batch_id,
                    'timestamp': log.timestamp,
                    'event_type': log.event_type,
                    'event_data': log.event_data,
                    'agent_role': log.agent_role,
                    'task_description': log.task_description,
                    'tool_name': log.tool_name,
                    'success': log.success,
                    'error_message': log.error_message,
                    'created_at': log.created_at
                })
            
            return result
            
        except SQLAlchemyError as e:
            print(f"查询日志失败: {str(e)}")
            return []
        finally:
            if session:
                session.close()