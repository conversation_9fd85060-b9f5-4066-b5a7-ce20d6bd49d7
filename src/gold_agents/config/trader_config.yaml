# 环境配置
environment: "test"  # test 或 prod

# Bitget API配置
api:
  api_key: "bg_44025411ed4ba2c0032ac4e2dff2b18e"
  api_secret: "d30fa4f8e6d80462659ed7e525d489020413a4e9d11cd16a3c8f6ceeccd87500"
  passphrase: "1137285095"

# 价格监控配置
price_monitor:
  short_term:
    window: 60  # 1分钟
    threshold: 0.005  # 0.5%
  medium_term:
    window: 300  # 5分钟
    threshold: 0.01  # 1%
  long_term:
    window: 900  # 15分钟
    threshold: 0.02  # 2%
  volatility:
    max_volatility: 0.05  # 最大波动率
    high_volatility_threshold: 0.02  # 高波动率阈值
    volatility_multiplier: 1.5  # 高波动时的阈值乘数

# 风险控制参数配置
risk_control:
  # 仓位控制
  max_position_size: 1.0      # 最大仓位（BTC）
  max_account_risk: 0.1        # 最大账户风险比例（10%）
  max_position_value_usd: 100000 # 最大持仓价值（USDT）
  max_leverage: 5             # 最大杠杆倍数
  min_margin_ratio: 0.5       # 最小保证金率
  max_daily_loss: 0.05        # 最大日亏损比例（5%）
  max_single_loss: 0.02       # 最大单笔亏损比例（2%）
  
  # 价格控制
  max_price_deviation: 0.01   # 最大价格偏离度（1%）
  
  # 止盈止损控制
  default_take_profit_ratio: 0.02  # 默认止盈比例（2%）
  default_stop_loss_ratio: 0.01    # 默认止损比例（1%）
  
  # 仓位调整系数
  position_adjustment:
    high_volatility: 0.5      # 高波动时仓位系数
    strong_signal: 1.2        # 强信号时仓位系数
    medium_signal: 1.0        # 中等信号时仓位系数
    low_margin: 0.5           # 低保证金率时仓位系数
    near_daily_limit: 0.3     # 接近日亏损限制时仓位系数 

# 数据库配置
database:
  host: "*************"
  port: "6221"
  user: "Mysql5.7"
  password: "a78d04a8027589c3"
  name: "Mysql5.7"

# 交易对配置
trading_pairs:
  BTC:
    test_symbol: "SBTCSUSDT_SUMCBL"
    prod_symbol: "BTCUSDT_UMCBL"
    price_scale: 1
    leverage: 125
    volume_scale: 3
  ETH:
    test_symbol: "SETHSUSDT_SUMCBL"
    prod_symbol: "ETHUSDT_UMCBL"
    price_scale: 2
    leverage: 100
    volume_scale: 3 