smart_trading_task:
  description: '作为智能交易员，基于市场分析师的信号进行综合分析，包括风险评估、仓位管理，并直接执行优化的交易策略。**核心要求**：1）严格控制交易频次，每日不超过20次交易，优先质量而非数量；2）强制使用BitgetRiskTool进行风险评估，绝不跳过风险检查；3）实施严格的成本效益控制，单日手续费不超过30U；4）只对高质量信号（confidence≥0.75）进行交易。整合了交易决策、风险控制、仓位管理和订单执行的全流程，重点关注盈利质量和成本控制。'
  expected_output: '必须严格按照JSON格式输出结果，包含role、market_analysis、risk_assessment、position_management、trading_decision、execution_status和timestamp字段。market_analysis必须包含signal_quality、confidence_score和risk_reward_ratio字段。risk_assessment必须包含account_risk、position_risk和max_drawdown_risk字段。position_management必须包含current_position、target_position、available_margin和margin_usage字段。trading_decision必须包含action、symbol、order_type、size、entry_price、stop_loss、take_profit和reason字段。execution_status必须包含order_id、status、execution_price和slippage字段。不得包含任何解释或分析过程，只返回JSON格式的结果。'
  agent: smart_trader
  async_execution: false


market_analysis_task:
  description: '全面分析虚拟货币市场，识别**高质量**交易机会，并生成结构化的交易机会报告。**核心要求**：1）严格信号过滤，只推荐confidence≥0.75且风险收益比≥2.0的信号；2）进行成本效益分析，确保预期手续费占盈利比例不超过20%；3）至少3个技术指标同向确认才产生强信号；4）优先质量而非数量，减少低质量信号输出。需要调用市场数据工具获取实时K线、深度和行情数据，结合新闻情绪，并进行技术指标分析，最终产出包含交易方向、价格区间、止损止盈、仓位建议、信号强度、置信度和成本效益分析等信息的JSON报告。'
  expected_output: '必须严格按照JSON格式输出结果，包含role、analysis_summary、trading_opportunity和timestamp字段。trading_opportunity必须包含symbol、direction、entry_price_range、stop_loss_price、take_profit_price、position_size_suggestion、signal_strength、confidence_score、analysis_details（包含technical_indicators、news_sentiment、volatility_assessment）和reasoning字段。所有价格和数量必须是精确的数值。不得包含任何解释或分析过程，只返回JSON格式的结果。'
  agent: market_analyst
  async_execution: false


