# 项目背景：
Agents团队，结合指标工具 分析合约BTCUSDT K线（15min 、1H 、4H 、1D ），得出入场机会，交易员根据分析师的策略，进行入场交易 ，交易使用Bitget 封装的tools进行交易
# 角色
市场分析师
分析K线数据，寻找入场机会，
比如输出策略：
监控名称:高低点分型BTC-30分钟
周期做空
监控时间:2025-07-27 17:00:09
监控目标:117301.0-116947.0
监控提醒:118127.0-118846.0
监控止损:118893.0
有效期:4h-9h

风险控制：
结合分析师策略以及仓库情况进行风险控制 ，并且预估该策略的风险以及准确率
交易员：根据分析师以及策略师的分析情况进行智能交易
# 工具
BitgetAccountTool:获取账户信息
BitgetMarketTool：获取市市场最新信息以及交易深度
BitgetOrderTool：Bitget订单的工具，进行挂单、仓位管理
BitgetTradeTool：Bitget平台执行合约交易的工具
Jin10NewsTool：可以获取金十数据网站最新的金融新闻数据
Jin10NewsSpeculativeSentimentReportTool：金十数据-比特币持仓报告"
OrderHistoryDataTool：1、获取交易团队成交的订单历史统计数据，统计维度包括每天订单数量、盈亏情况、胜率、盈亏比、总利润、手续费等关键交易指标，用于分析交易表现和优化策略决策。 2、获取前一日根据交易记录的复盘总结信息，优化策略决策
KlineKlineDataTool：BTC 15min、1h、4h、1d K线数据以及指标数据