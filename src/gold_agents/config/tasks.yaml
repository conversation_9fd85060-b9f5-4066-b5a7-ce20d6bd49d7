market_analysis_task:
  description: |
    **使命：智能市场情报与盈利机会识别**
    
    对BTCUSDT进行智能多时间框架技术分析，识别高概率交易机会。
    你的分析应提供可操作的情报，直接促进盈利的交易决策，实现利润最大化。
    
    **分析要求：**
    1. **多时间框架综合分析**（1D、4H、1H为主，15min、30min为辅）
       - **主要交易框架**：1D主趋势 + 4H中期趋势 + 1H执行框架
       - **辅助参考框架**：30min趋势过渡 + 15min精确时机
       - 跨时间框架的关键支撑/阻力位汇合分析，确保不错过任何重要汇合点
       - 至少2个主要时间框架同向确认才能交易，提高成功率
       - **短周期数据使用规则（智能优化）：**
         * **30min周期**：作为1H和4H之间的过渡分析，减少噪声，提高信号质量
         * **15min周期**：仅用于以下特定场景
           - 极端市场波动（单日>8%，从15%降低）时的精确入场时机
           - 强反转信号（强度>75%，从80%降低）的确认
           - 异动检测和未来异动风险评估
           - 止损点位的精确执行时机
           - **24/7机会捕获**：全天候监控短周期异动和套利机会
           - **应急响应**：价格跳跃>0.8%或成交量异常>1.8倍时的快速分析
           - **严禁用于独立信号生成和主要交易决策**
    
    2. **增强技术信号生成**
       - **多重指标汇合**：MACD背离、动态RSI、布林带挤压/扩张
       - **趋势强度分析**：多重移动平均线系统确认
       - **异动检测系统**：实时异动等级评估(normal/moderate/high/extreme)
       - **未来异动预测**：基于短周期数据的异动风险评估
       - **成交量深度分析**：异常成交量+价格确认+流动性评估
       - **关键位汇合分析**：多时间框架支撑阻力位重合度评估
    
    3. **市场结构映射**
       - 入场/出场的关键价格水平
       - 突破/跌破区域
       - 风险回报优化区域
       - 市场状态分类（趋势/区间/过渡）
    
    4. **战略机会评估** - 多层级信号系统（优化版）
       - 信号质量分类：ALPHA(≥3:1，从4:1降低)、BETA(≥2:1，从2.5:1降低)、GAMMA(≥1.8:1，从2:1降低)，降低门槛以捕获更多机会
       - 风险回报要求：ALPHA≥3:1，BETA≥2:1，GAMMA≥1.8:1，平衡盈利与机会捕获
       - 概率评估和信心水平：ALPHA≥70%（从80%降低），BETA≥60%（从70%降低），GAMMA≥50%（从60%降低）
       - 交易执行的最佳时间框架，优先选择最优时机、实现复利最大化
       - 替代场景规划，建立多层次机会捕获网络，24/7持续监控
       - **紧急机会识别**：反转信号强度>75%时强制执行，不受常规限制
    
    **交易约束要求：**
    - 根据波动性在允许范围内适度调整指标参数
    - 基于多时间框架客观权衡信号强度
    - 使用标准化汇合验证标准
    - **移除交易次数限制，以机会质量和盈利为最高目标，主动捕获套利机会**
    
    使用所有可用工具：KlineDataTool进行技术分析，Jin10NewsTool获取市场情绪，
    OrderHistoryDataTool获取表现背景。

  expected_output: |
    在以下JSON结构中提供全面的市场情报：
    
    ```json
    {
      "market_regime": "trending_bullish|trending_bearish|ranging|transitional",
      "primary_opportunity": {
        "direction": "long|short|neutral",
        "signal_quality": "ALPHA|BETA|GAMMA",
        "confidence_level": 0.0-1.0,
        "timeframe_focus": "1H|4H|1D",
        "entry_zone": {
          "optimal": price_level,
          "range": [min_price, max_price]
        },
        "targets": [target1, target2, target3],
        "stop_loss": price_level,
        "risk_reward_ratio": ratio
      },
      "technical_analysis": {
        "trend_analysis": {
          "1D": "bullish|bearish|neutral",
          "4H": "bullish|bearish|neutral", 
          "1H": "bullish|bearish|neutral"
        },
        "key_levels": {
          "major_resistance": [levels],
          "major_support": [levels], 
          "immediate_resistance": level,
          "immediate_support": level,
          "support_strength": strength_score,
          "resistance_strength": strength_score,
          "support_effectiveness": effectiveness_ratio,
          "resistance_effectiveness": effectiveness_ratio
        },
        "indicators": {
          "macd_signal": "bullish|bearish|neutral",
          "rsi_status": "overbought|oversold|neutral",
          "trend_signal": "bullish|bearish|neutral",
          "volume_confirmation": true|false,
          "signal_quality": "ALPHA|BETA|GAMMA",
          "atr_value": number
        }
      },
      "market_context": {
        "volatility_regime": "high|medium|low",
        "news_sentiment": "positive|negative|neutral",
        "market_structure": "breakout|breakdown|consolidation|trend_continuation",
        "support_resistance_quality": "high|medium|low",
        "volume_profile_signal": "bullish|bearish|neutral",
        "institutional_flow_direction": "buying|selling|neutral",
        "trading_session": "asian|european|american|overlap",
        "correlation_analysis": {
          "btc_spx_correlation": correlation_value,
          "btc_dxy_correlation": correlation_value,
          "btc_gold_correlation": correlation_value
        },
        "futures_basis": "contango|backwardation|neutral",
        "onchain_flow": "inflow|outflow|neutral",
        "liquidity_depth": "high|medium|low",
        "extreme_market_detection": {
          "black_swan_alert": true|false,
          "daily_volatility_pct": percentage,
          "volume_anomaly_detected": true|false,
          "reversal_signal_strength": 0-100,
          "dual_position_risk_alert": true|false
        },
        "anomaly_detection": {
          "current_anomaly_level": "normal|moderate|high|extreme",
          "future_anomaly_risk": 0-3,
          "anomaly_score": 0-10,
          "volatility_spike_detected": true|false,
          "volume_spike_detected": true|false,
          "bb_squeeze_detected": true|false,
          "bb_expansion_detected": true|false,
          "divergence_signals": {
            "macd_divergence": true|false,
            "rsi_divergence": true|false,
            "price_momentum_divergence": true|false
          },
          "short_timeframe_alerts": {
            "15min_anomaly_risk": 0-3,
            "30min_trend_shift": true|false,
            "precision_entry_signal": true|false
          }
        },
        "multi_timeframe_confluence": {
          "1D_4H_1H_alignment": "strong_bullish|bullish|neutral|bearish|strong_bearish",
          "30min_15min_confirmation": "confirming|neutral|conflicting",
          "timeframe_strength_score": 0-10,
          "cross_timeframe_support_resistance": [price_levels]
        },
        "bias_check": {
          "confirmation_bias_alert": true|false,
          "anchoring_bias_alert": true|false,
          "emotion_filter_active": true|false
        }
      },
      "strategic_recommendations": {
        "position_sizing_guidance": "aggressive|moderate|conservative",
        "trade_duration": "scalp|intraday|swing|position",
        "risk_management_notes": "specific guidance",
        "contingency_plan": "alternative scenarios"
      },
      "analysis_timestamp": "ISO_timestamp",
      "analyst_confidence": "high|medium|low"
    }
    ```

  agent: market_analyst
  async_execution: false

risk_management_task:
  description: |
    **使命：智能风险优化与利润最大化**
    
    评估市场分析师的建议和当前投资组合状态，以优化风险调整后收益。
    你的角色是通过智能风险管理最大化盈利能力，在保证利润的前提下实现利润最大化。
    
    **风险评估要求：**
    1. **机会风险评估**
       - 评估信号质量和市场条件
       - 确定最佳仓位规模
       - 计算风险回报优化
       - 评估投资组合热度和敞口
    
    2. **动态风险配置**
       - 根据近期表现调整风险
       - 根据信念度调整仓位规模
       - 管理投资组合相关性和多样化
       - 实施条件性风险参数
    
    3. **利润保护策略**
       - 设计止损和止盈水平
       - 创建利润分级和跟踪策略
       - 规划整个交易生命周期的仓位管理
       - 制定应急风险降低措施
    
    4. **表现优化**
       - 分析历史表现模式
       - 根据市场状态调整风险参数
       - 优化跨机会的资本配置
       - 平衡激进增长与资本保护
    
    **风险管理约束：**
    - **禁止覆盖硬性风险参数**（但允许在成功表现下适度提升）
    - 在既定范围内调整仓位规模和止损水平
    - 基于客观数据修正风险评估
    - 连续亏损时必须强制执行降低风险措施
    - 拥有最终交易否决权以保护资本
    - **优先考虑盈利机会，适度提升风险容忍度以获取更高回报**
    
    使用BitgetAccountTool获取投资组合状态，OrderHistoryDataTool进行表现分析。

  expected_output: |
    以JSON格式提供全面的风险管理指导：
    
    ```json
    {
      "risk_assessment": {
        "opportunity_quality": "exceptional|high|medium|low",
        "market_risk_level": "low|medium|high|extreme",
        "portfolio_heat": percentage,
        "recommended_risk_per_trade": "ALPHA信号最高5%（从3%提高），BETA信号最高3.5%（从2.5%提高），GAMMA信号最高2%（从1.5%提高），在保护资本的同时最大化盈利机会",
        "max_portfolio_exposure": percentage,
        "var_95_confidence": dollar_amount,
        "max_drawdown_estimate": percentage,
        "correlation_risk_score": 0.0-1.0,
        "liquidity_risk_adjustment": percentage,
        "sharpe_ratio_target": ratio,
        "stress_test_results": {
          "black_swan_loss_20pct": dollar_amount,
          "black_swan_loss_30pct": dollar_amount,
          "liquidity_crunch_impact": percentage,
          "correlation_breakdown_risk": percentage
        }
      },
      "position_sizing": {
        "recommended_size": dollar_amount_or_percentage,
        "sizing_rationale": "conviction_based|volatility_adjusted|performance_based",
        "scaling_strategy": "single_entry|layered_entry|pyramid_building",
        "maximum_position_limit": dollar_amount_or_percentage
      },
      "risk_parameters": {
        "stop_loss_strategy": {
          "type": "technical|atr_based|percentage|dynamic",
          "initial_stop": price_level,
          "trailing_stop": true|false,
          "stop_adjustment_rules": "specific_rules"
        },
        "take_profit_strategy": {
          "targets": [
            {"level": price, "percentage": "50%首个目标强制平仓"},
            {"level": price, "percentage": "30%第二目标"},
            {"level": price, "percentage": "20%最终目标"}
          ],
          "profit_trailing": true|false,
          "extension_criteria": "conditions_for_target_extension"
        }
      },
      "portfolio_management": {
        "current_exposure": percentage,
        "correlation_risk": "low|medium|high",
        "diversification_status": "adequate|needs_improvement|over_diversified",
        "capital_allocation_advice": "increase|maintain|reduce"
      },
      "performance_context": {
        "recent_win_rate": percentage,
        "profit_factor": ratio,
        "current_drawdown": percentage,
        "performance_trend": "improving|stable|declining",
        "risk_adjustment_needed": true|false
      },
      "contingency_planning": {
        "adverse_scenario_plan": "specific_actions",
        "profit_protection_triggers": [trigger_conditions],
        "emergency_exit_criteria": "conditions",
        "risk_reduction_steps": ["step1", "step2", "step3"]
      },
      "recommendations": {
        "trade_approval": "approved_only_ALPHA|rejected_BETA_GAMMA",
        "risk_modifications": "suggested_changes",
        "monitoring_requirements": "what_to_watch",
        "review_schedule": "when_to_reassess"
      },
      "extreme_market_protocols": {
        "black_swan_response": "immediate_action_required",
        "dual_position_elimination": "force_close_weaker_direction",
        "reversal_signal_compliance": "must_execute_if_strength_over_80",
        "emergency_override_authority": "absolute_decision_power_in_crisis",
        "forced_stop_loss_triggers": ["single_trade_loss_approaching_2pct"]
      }
    }
    ```

  agent: risk_manager
  async_execution: false

smart_trading_task:
  description: |
    **使命：智能执行与利润最大化**
    
    基于分析师建议和风险管理师批准执行交易，专注于最佳时机、
    智能交易管理和从每个机会中最大化利润提取，实现利润最大化。
    
    **执行要求：**
    1. **最佳入场执行**
       - 为最大概率把握入场时机
       - 尽可能使用限价单获得更好成交
       - 根据风险管理师策略分批建仓
       - 监控市场微观结构以获得最佳执行
    
    2. **动态交易管理**
       - 根据实时价格行为调整止损和目标
       - 在预定水平分批平仓
       - 跟踪止损以保护利润同时允许收益
       - 在条件一致时向盈利仓位加仓
    
    3. **利润优化**
       - 通过智能管理最大化盈利交易的收益
       - 识别何时延伸目标vs获利了结
       - 利用市场动量增强仓位盈利能力
       - 实施利润回收以实现复合增长
    
    4. **损失最小化**
       - 毫不犹豫地执行止损
       - 及早识别恶化条件
       - 亏损后调低仓位规模
       - 在不利条件下保持纪律
    
    **执行纪律约束：**
    - 在风险管理师批准区域内选择入场/出场点
    - 严格按照预设参数调整仓位管理
    - **禁止覆盖标准程序和风险控制措施**
    - 连续亏损后必须等待重新评估和批准（但不限制后续交易频次）
    - **以机会质量和盈利为最高目标，移除交易次数限制，24/7持续捕获高质量机会，实现最大复利效应**
    - 专注于精准执行而非策略修改
    
    使用BitgetTradeTool、BitgetOrderTool进行执行，BitgetMarketTool获取市场数据，
    BitgetAccountTool进行仓位监控。

  expected_output: |
    以JSON格式提供详细的执行计划和结果：
    
    ```json
    {
      "execution_decision": {
        "action": "execute_long|execute_short|wait|close_position",
        "rationale": "reasoning_for_decision",
        "timing_assessment": "optimal|good|suboptimal|poor",
        "market_conditions": "favorable|neutral|unfavorable"
      },
      "trade_plan": {
        "entry_strategy": {
          "type": "market_order|limit_order|scaled_entry|conditional_entry",
          "target_price": price_level,
          "size": position_size,
          "execution_timeframe": "wait_for_optimal_setup",
          "opportunity_driven": "no_artificial_limits_focus_on_quality"
        },
        "position_management": {
          "initial_stop_loss": price_level,
          "take_profit_levels": [
            {"price": level, "size_reduction": percentage},
            {"price": level, "size_reduction": percentage}
          ],
          "trailing_stop_plan": "activation_level_and_distance",
          "scaling_plan": "add_on_pullback|add_on_breakout|single_position"
        }
      },
      "execution_analysis": {
        "orderbook_depth": {
          "bid_depth_50btc": price_levels,
          "ask_depth_50btc": price_levels,
          "large_orders_detected": true|false,
          "whale_activity": "buying|selling|neutral"
        },
        "execution_cost_estimate": {
          "estimated_slippage": percentage,
          "market_impact_cost": percentage,
          "optimal_execution_size": btc_amount,
          "twap_vs_market_comparison": percentage
        },
        "execution_quality_metrics": {
          "vwap_performance": percentage,
          "arrival_price_performance": percentage,
          "timing_score": 0.0-1.0,
          "execution_efficiency": percentage
        }
      },
      "trade_monitoring": {
        "key_levels_to_watch": [price_levels],
        "profit_targets_remaining": [levels],
        "stop_loss_current": price_level,
        "next_management_action": "planned_next_step",
        "monitoring_frequency": "4hourly_minimum",
        "profit_protection": "move_to_BE_at_1.5R",
        "time_stop": "review_after_24h"
      },
      "performance_tracking": {
        "trade_expectancy": expected_profit_loss,
        "risk_reward_actual": actual_ratio,
        "execution_quality": "excellent|good|average|poor",
        "lessons_learned": "key_insights_for_improvement"
      },
      "team_coordination": {
        "analyst_confidence": 0-100,
        "risk_manager_confidence": 0-100,
        "trader_confidence": 0-100,
        "consensus_level": "high|medium|low|conflict",
        "conflict_resolution_status": "resolved|escalated|pending",
        "decision_rationale": "详细的决策依据链条",
        "key_information_alerts": ["🚨标记的关键信息"],
        "information_validity_period": "时间范围",
        "required_confirmations": "需要的额外确认"
      },
      "extreme_execution_protocols": {
        "black_swan_execution_mode": "immediate_compliance_with_risk_manager",
        "dual_position_prevention": "reject_conflicting_signals_automatically",
        "reversal_signal_execution": "mandatory_if_strength_over_80pct",
        "emergency_stop_compliance": "immediate_execution_no_questions",
        "15min_precision_timing": "use_only_for_extreme_market_entry_exit"
      }
    }
    ```
 
 