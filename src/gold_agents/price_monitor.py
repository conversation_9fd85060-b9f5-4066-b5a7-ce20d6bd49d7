import os
import sys
import logging
from logging.handlers import TimedRotatingFileHandler
import sqlite3
import json
import requests
import time
import threading
from datetime import datetime, timedelta
from collections import deque
from typing import Dict, List, Optional, Tuple
import bisect
from  main import main
# 配置日志
def setup_logger():
    """配置日志记录器 - 优化日志级别和输出"""
    # 创建logs目录（如果不存在）
    log_dir = 'logs'
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)

    # 创建日志记录器
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.WARNING)  # 修改为WARNING级别，减少日志输出

    # 避免重复添加处理器
    if logger.handlers:
        logger.handlers.clear()

    # 创建格式化器
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.WARNING)  # 控制台只显示WARNING及以上级别
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    # 创建文件处理器（按天切割，限制文件大小）
    log_file = os.path.join(log_dir, 'price_monitor.log')
    file_handler = TimedRotatingFileHandler(
        filename=log_file,
        when='midnight',  # 每天午夜切割
        interval=1,       # 间隔为1天
        backupCount=7,    # 只保留7天的日志，减少磁盘占用
        encoding='utf-8'
    )
    file_handler.setLevel(logging.INFO)  # 文件记录INFO及以上级别
    file_handler.setFormatter(formatter)
    logger.addHandler(file_handler)

    # 设置第三方库的日志级别
    logging.getLogger('pybitget').setLevel(logging.ERROR)  # 减少pybitget日志
    logging.getLogger('urllib3').setLevel(logging.ERROR)   # 减少网络请求日志
    logging.getLogger('requests').setLevel(logging.ERROR)  # 减少requests日志

    return logger

# 初始化日志记录器
logger = setup_logger()

# 全局价格监控实例，供外部模块使用
price_monitor_instance = None

def get_price_monitor():
    """获取价格监控实例，如果不存在则创建"""
    global price_monitor_instance
    if price_monitor_instance is None:
        price_monitor_instance = PriceMonitor()
    return price_monitor_instance

def process_websocket_message(message):
    """处理WebSocket消息的公共函数，供外部调用"""
    monitor = get_price_monitor()
    monitor.process_message(message)

# 配置类
class Config:
    """配置管理类"""
    # 企业微信机器人配置
    WECHAT_ROBOT_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=22f9bc8d-bc61-45f2-beac-d996dbe425d3'


    # 交易对配置
    TRADING_PAIRS = ['BTC']  # 需要监控的交易对列表

    # 价格监控配置 - 优化版（基于点数变化）
    PRICE_MONITOR_CONFIG = {
        'target_points': 200,            # 目标变化点数（主要配置）
        'min_threshold_percent': 0.001,  # 最小阈值 0.1%
        'max_threshold_percent': 0.01,   # 最大阈值 1.0%
        'time_intervals': [1, 3, 5, 10], # 多时间区间（分钟）
        'trend_threshold_ratio': 0.67,   # 趋势阈值比例（相对于价格变化阈值）
        'trend_intervals': 3,            # 持续趋势需要的区间数
    }



class NotificationSender:
    """通知发送器"""

    def __init__(self, webhook_url: str):
        self.webhook_url = webhook_url
        self.session = requests.Session()  # 使用会话复用连接

    def send_wechat_message(self, trading_pair: str, price: float, price_change: float,
                           trigger_reason: str, interval_info: dict) -> bool:
        """发送简化的企业微信消息"""
        try:
            # 计算简单的波动率
            volatility = price_change  # 直接使用价格变化作为波动率

            # 解析触发原因中的价格信息，提取最重要的区间信息
            main_interval_info = self._extract_main_interval_info(trigger_reason, interval_info)

            message = {
                "msgtype": "markdown",
                "markdown": {
                    "content": (
                        f"# 🚨 价格监控告警\n"
                        f"**时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                        f"**交易对**: {trading_pair}USDT\n"
                        f"**当前价格**: {price:.2f}\n"
                        f"**价格波动**: {main_interval_info}\n"
                        f"**波动率**: <font color=\"warning\">{volatility:.2%}</font>\n"
                        f"**触发原因**: {trigger_reason}\n\n"
                        f""
                    )
                }
            }

            headers = {'Content-Type': 'application/json'}
            response = self.session.post(self.webhook_url, headers=headers,
                                       data=json.dumps(message), timeout=10)

            if response.status_code == 200:
                logger.info(f"企业微信消息发送成功: {trading_pair}")
                return True
            else:
                logger.error(f"企业微信消息发送失败: {response.status_code} - {response.text}")
                return False

        except requests.exceptions.Timeout:
            logger.error(f"发送企业微信消息超时: {trading_pair}")
            return False
        except Exception as e:
            logger.error(f"发送企业微信消息失败: {str(e)}")
            return False

    def _extract_main_interval_info(self, trigger_reason: str, interval_info: dict) -> str:
        """从触发原因中提取主要的价格波动信息"""
        try:
            # 找到变化最大的区间
            max_change = 0
            main_interval = None
            main_info = None

            for interval_name, info in interval_info.items():
                if info['data_count'] >= 2:
                    actual_points = info['max_price'] - info['min_price']
                    if actual_points > max_change:
                        max_change = actual_points
                        main_interval = interval_name
                        main_info = info

            if main_info:
                return (f"{main_interval}内从 {main_info['min_price']:.2f} "
                       f"→ {main_info['max_price']:.2f} "
                       f"(+{max_change:.2f}点)")
            else:
                return "价格波动详情暂无"

        except Exception as e:
            return f"价格波动详情解析失败: {str(e)}"

    def send_simple_message(self, content: str) -> bool:
        """发送简单的企业微信消息"""
        try:
            message = {
                "msgtype": "markdown",
                "markdown": {
                    "content": content
                }
            }

            headers = {'Content-Type': 'application/json'}
            response = self.session.post(self.webhook_url, headers=headers,
                                       data=json.dumps(message), timeout=10)

            if response.status_code == 200:
                logger.info("企业微信消息发送成功")
                return True
            else:
                logger.error(f"企业微信消息发送失败: {response.status_code} - {response.text}")
                return False

        except requests.exceptions.Timeout:
            logger.error("发送企业微信消息超时")
            return False
        except Exception as e:
            logger.error(f"发送企业微信消息失败: {str(e)}")
            return False

class RateLimiter:
    """基于滑动窗口的限流器"""
    
    def __init__(self, max_requests: int, window_seconds: int):
        """
        初始化限流器
        :param max_requests: 窗口期内最大请求数
        :param window_seconds: 窗口期长度（秒）
        """
        self.max_requests = max_requests
        self.window_seconds = window_seconds
        self.requests = []  # 存储请求时间戳的有序列表
        self._lock = threading.Lock()
    
    def is_allowed(self) -> bool:
        """检查是否允许当前请求"""
        with self._lock:
            current_time = time.time()
            
            # 清理过期的请求记录
            cutoff_time = current_time - self.window_seconds
            # 使用二分查找快速定位需要保留的记录
            cutoff_index = bisect.bisect_left(self.requests, cutoff_time)
            self.requests = self.requests[cutoff_index:]
            
            # 检查是否超过限制
            if len(self.requests) >= self.max_requests:
                return False
            
            # 记录当前请求
            bisect.insort(self.requests, current_time)
            return True
    
    def get_remaining_requests(self) -> int:
        """获取剩余可用请求数"""
        with self._lock:
            current_time = time.time()
            cutoff_time = current_time - self.window_seconds
            cutoff_index = bisect.bisect_left(self.requests, cutoff_time)
            valid_requests = len(self.requests) - cutoff_index
            return max(0, self.max_requests - valid_requests)
    
    def get_reset_time(self) -> float:
        """获取下次重置时间（秒）"""
        with self._lock:
            if not self.requests:
                return 0
            
            current_time = time.time()
            cutoff_time = current_time - self.window_seconds
            cutoff_index = bisect.bisect_left(self.requests, cutoff_time)
            
            if cutoff_index < len(self.requests):
                oldest_valid_request = self.requests[cutoff_index]
                return oldest_valid_request + self.window_seconds - current_time
            
            return 0

class SmartRateLimitManager:
    """智能限流管理器 - 支持多级限流策略"""
    
    def __init__(self):
        self.limiters = {}
        self._lock = threading.Lock()
        
        # 配置多级限流策略
        self.rate_limit_config = {
            # 短期限流：1分钟内最多3次
            'short_term': {'max_requests': 3, 'window_seconds': 60},
            # 中期限流：5分钟内最多8次
            'medium_term': {'max_requests': 8, 'window_seconds': 300},
            # 长期限流：30分钟内最多15次
            'long_term': {'max_requests': 15, 'window_seconds': 1800}
        }
    
    def _get_limiter_key(self, trading_pair: str, limiter_type: str) -> str:
        """生成限流器键名"""
        return f"{trading_pair}_{limiter_type}"
    
    def _get_or_create_limiter(self, trading_pair: str, limiter_type: str) -> RateLimiter:
        """获取或创建限流器"""
        key = self._get_limiter_key(trading_pair, limiter_type)
        
        if key not in self.limiters:
            config = self.rate_limit_config[limiter_type]
            self.limiters[key] = RateLimiter(
                max_requests=config['max_requests'],
                window_seconds=config['window_seconds']
            )
        
        return self.limiters[key]
    
    def is_allowed(self, trading_pair: str) -> tuple[bool, str]:
        """检查是否允许发送告警"""
        with self._lock:
            # 检查所有级别的限流
            for limiter_type, config in self.rate_limit_config.items():
                limiter = self._get_or_create_limiter(trading_pair, limiter_type)
                
                if not limiter.is_allowed():
                    # 计算重置时间
                    reset_time = limiter.get_reset_time()
                    reset_minutes = reset_time / 60
                    
                    window_minutes = config['window_seconds'] / 60
                    max_requests = config['max_requests']
                    
                    reason = f"{limiter_type}限流触发: {window_minutes:.0f}分钟内已达{max_requests}次上限，{reset_minutes:.1f}分钟后重置"
                    return False, reason
            
            return True, ""
    
    def can_send_alert(self, trading_pair: str, trigger_reason: str, 
                      price_change_ratio: float, last_trigger_reason: str) -> tuple[bool, str]:
        """检查是否可以发送告警"""
        # 检查基本限流
        allowed, reason = self.is_allowed(trading_pair)
        if not allowed:
            return False, reason
        
        # 检查触发原因相似性（简化版）
        if last_trigger_reason and self._is_similar_reason(trigger_reason, last_trigger_reason):
            if price_change_ratio < 0.01:  # 价格变化小于1%
                return False, f"相似触发且价格变化较小({price_change_ratio:.2%})"
        
        return True, ""
    
    def record_alert(self, trading_pair: str, trigger_reason: str):
        """记录告警（实际的限流计数在is_allowed中已完成）"""
        # 这里可以添加额外的记录逻辑，如日志记录等
        pass
    
    def _is_similar_reason(self, reason1: str, reason2: str) -> bool:
        """简单的触发原因相似性检查"""
        if not reason1 or not reason2:
            return False
        
        # 检查是否都包含相同的关键词
        keywords = ['价格从', '持续上涨', '持续下跌', '波动']
        for keyword in keywords:
            if keyword in reason1 and keyword in reason2:
                return True
        
        return False
    
    def get_status(self, trading_pair: str) -> dict:
        """获取限流状态"""
        status = {}
        
        for limiter_type, config in self.rate_limit_config.items():
            limiter = self._get_or_create_limiter(trading_pair, limiter_type)
            remaining = limiter.get_remaining_requests()
            reset_time = limiter.get_reset_time()
            
            status[limiter_type] = {
                'max_requests': config['max_requests'],
                'window_seconds': config['window_seconds'],
                'remaining_requests': remaining,
                'reset_time_seconds': reset_time
            }
        
        return status

class DatabaseManager:
    """数据库管理器 - 优化连接管理和性能"""

    def __init__(self, db_path: str = 'price_data.db'):
        self.db_path = db_path
        self._connection_pool = {}  # 连接池
        self._lock = threading.Lock()  # 线程锁

        # 设置各表的最大数据量
        self.max_records = {
            'price_data': 100000,    # 价格数据最大记录数
            'kline_data': 100000,    # K线数据最大记录数
            'alert_history': 5000    # 告警历史记录数
        }
        self._init_db()

    def _get_connection(self) -> sqlite3.Connection:
        """获取数据库连接"""
        thread_id = threading.get_ident()

        with self._lock:
            if thread_id not in self._connection_pool:
                conn = sqlite3.connect(self.db_path, check_same_thread=False)
                conn.execute("PRAGMA journal_mode=WAL")  # 启用WAL模式提高并发性能
                conn.execute("PRAGMA synchronous=NORMAL")  # 平衡性能和安全性
                self._connection_pool[thread_id] = conn

            return self._connection_pool[thread_id]

    def _init_db(self):
        """初始化数据库"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()

            # 创建价格数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS price_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trading_pair TEXT NOT NULL,
                    price REAL NOT NULL,
                    timestamp DATETIME NOT NULL,
                    volume REAL,
                    high REAL,
                    low REAL
                )
            ''')

            # 创建价格数据表索引
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_price_data_pair_time
                ON price_data(trading_pair, timestamp)
            ''')

            # 创建K线数据表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS kline_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trading_pair TEXT NOT NULL,
                    open_price REAL NOT NULL,
                    high_price REAL NOT NULL,
                    low_price REAL NOT NULL,
                    close_price REAL NOT NULL,
                    volume REAL NOT NULL,
                    timestamp DATETIME NOT NULL,
                    UNIQUE(trading_pair, timestamp)
                )
            ''')

            # 创建K线数据表索引
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_kline_data_pair_time
                ON kline_data(trading_pair, timestamp)
            ''')

            # 创建告警历史表
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS alert_history (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    trading_pair TEXT NOT NULL,
                    price REAL NOT NULL,
                    price_change REAL NOT NULL,
                    volatility REAL NOT NULL,
                    trigger_reason TEXT,
                    timestamp DATETIME NOT NULL
                )
            ''')

            # 创建告警历史表索引
            cursor.execute('''
                CREATE INDEX IF NOT EXISTS idx_alert_history_pair_time
                ON alert_history(trading_pair, timestamp)
            ''')

            conn.commit()
            logger.info("数据库初始化成功")
        except Exception as e:
            logger.error(f"数据库初始化失败: {str(e)}")
            raise

    def _cleanup_old_data(self, table_name: str):
        """清理旧数据 - 优化性能"""
        try:
            if table_name not in self.max_records:
                return

            conn = self._get_connection()
            cursor = conn.cursor()

            # 获取当前记录数
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            count = cursor.fetchone()[0]

            # 如果超过最大记录数，删除最旧的数据
            if count > self.max_records[table_name]:
                delete_count = count - self.max_records[table_name]
                cursor.execute(f"""
                    DELETE FROM {table_name}
                    WHERE id IN (
                        SELECT id FROM {table_name}
                        ORDER BY timestamp ASC
                        LIMIT ?
                    )
                """, (delete_count,))
                conn.commit()
                logger.debug(f"已清理 {table_name} 表中的 {delete_count} 条旧数据")
        except Exception as e:
            logger.error(f"清理 {table_name} 表数据失败: {str(e)}")

    def save_price_data(self, trading_pair: str, price: float, timestamp: datetime,
                       volume: float = None, high: float = None, low: float = None):
        """保存价格数据"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO price_data (trading_pair, price, timestamp, volume, high, low)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (trading_pair, price, timestamp, volume, high, low))
            conn.commit()

            # 定期清理旧数据（每100次插入清理一次）
            if hash(timestamp) % 100 == 0:
                self._cleanup_old_data('price_data')
        except Exception as e:
            logger.error(f"保存价格数据失败: {str(e)}")

    def save_kline_data(self, trading_pair: str, open_price: float, high_price: float, low_price: float,
                       close_price: float, volume: float, timestamp: datetime):
        """保存K线数据"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute('''
                INSERT OR REPLACE INTO kline_data
                (trading_pair, open_price, high_price, low_price, close_price, volume, timestamp)
                VALUES (?, ?, ?, ?, ?, ?, ?)
            ''', (trading_pair, open_price, high_price, low_price, close_price, volume, timestamp))
            conn.commit()

            # 定期清理旧数据
            if hash(timestamp) % 100 == 0:
                self._cleanup_old_data('kline_data')
        except Exception as e:
            logger.error(f"保存K线数据失败: {str(e)}")

    def save_alert_history(self, trading_pair: str, price: float, price_change: float,
                          volatility: float, trigger_reason: str, timestamp: datetime):
        """保存告警历史"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute('''
                INSERT INTO alert_history (trading_pair, price, price_change, volatility, trigger_reason, timestamp)
                VALUES (?, ?, ?, ?, ?, ?)
            ''', (trading_pair, price, price_change, volatility, trigger_reason, timestamp))
            conn.commit()

            # 定期清理旧数据
            if hash(timestamp) % 20 == 0:
                self._cleanup_old_data('alert_history')
        except Exception as e:
            logger.error(f"保存告警历史失败: {str(e)}")

    def get_table_size(self, table_name: str) -> int:
        """获取表的数据量"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute(f"SELECT COUNT(*) FROM {table_name}")
            return cursor.fetchone()[0]
        except Exception as e:
            logger.error(f"获取表 {table_name} 数据量失败: {str(e)}")
            return 0

    def get_database_size(self) -> dict:
        """获取数据库各表的数据量"""
        return {
            'price_data': self.get_table_size('price_data'),
            'kline_data': self.get_table_size('kline_data'),
            'alert_history': self.get_table_size('alert_history')
        }

    def get_recent_prices(self, trading_pair: str, minutes: int = 60) -> List[Tuple[float, datetime]]:
        """获取最近的价格数据"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute('''
                SELECT price, timestamp FROM price_data
                WHERE trading_pair = ? AND timestamp >= datetime('now', ?)
                ORDER BY timestamp DESC
            ''', (trading_pair, f'-{minutes} minutes'))
            return cursor.fetchall()
        except Exception as e:
            logger.error(f"获取价格数据失败: {str(e)}")
            return []

    def get_recent_klines(self, trading_pair: str, minutes: int = 60) -> List[Tuple]:
        """获取最近的K线数据"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute('''
                SELECT open_price, high_price, low_price, close_price, volume, timestamp
                FROM kline_data
                WHERE trading_pair = ? AND timestamp >= datetime('now', ?)
                ORDER BY timestamp DESC
            ''', (trading_pair, f'-{minutes} minutes'))
            return cursor.fetchall()
        except Exception as e:
            logger.error(f"获取K线数据失败: {str(e)}")
            return []

    def get_price_range(self, trading_pair: str, minutes: int = 60) -> Tuple[float, float]:
        """获取指定时间范围内的价格区间"""
        try:
            conn = self._get_connection()
            cursor = conn.cursor()
            cursor.execute('''
                SELECT MIN(price), MAX(price) FROM price_data
                WHERE trading_pair = ? AND timestamp >= datetime('now', ?)
            ''', (trading_pair, f'-{minutes} minutes'))

            result = cursor.fetchone()
            if result and result[0] is not None and result[1] is not None:
                return result[0], result[1]
            return 0.0, 0.0
        except Exception as e:
            logger.error(f"获取价格区间失败: {str(e)}")
            return 0.0, 0.0

    def close_connections(self):
        """关闭所有数据库连接"""
        with self._lock:
            for conn in self._connection_pool.values():
                try:
                    conn.close()
                except:
                    pass
            self._connection_pool.clear()

class SmartIntervalMonitor:
    """智能多区间价格监控器 - 检测价格变化和持续趋势"""

    def __init__(self, intervals: list = [1, 3, 5, 10], db_manager=None, trading_pair=None):
        self.intervals = intervals  # 多个时间区间（分钟）
        self.price_data = []  # 存储所有价格数据 [(price, timestamp), ...]
        self.db_manager = db_manager
        self.trading_pair = trading_pair

        # 如果提供了数据库管理器，加载历史数据
        if self.db_manager and self.trading_pair:
            self._load_historical_data()

    def _load_historical_data(self):
        """从数据库加载历史数据"""
        try:
            # 获取最长区间的历史数据
            max_interval = max(self.intervals)
            cutoff_time = datetime.now() - timedelta(minutes=max_interval)

            # 从数据库获取历史价格数据
            historical_data = self.db_manager.get_recent_prices(
                self.trading_pair,
                minutes=max_interval + 30  # 多获取一点数据确保覆盖
            )

            # 转换为内部格式并过滤时间范围
            for price, timestamp_str in historical_data:
                try:
                    # 处理不同的时间戳格式
                    if 'T' in timestamp_str:
                        # ISO格式: 2025-06-10T12:57:58.163000
                        timestamp = datetime.fromisoformat(timestamp_str.replace('Z', '+00:00'))
                    else:
                        # 简单格式: 2025-06-10 12:57:58
                        timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S')

                    if timestamp >= cutoff_time:
                        self.price_data.append((price, timestamp))
                except Exception as e:
                    logger.debug(f"解析时间戳失败: {timestamp_str}, 错误: {e}")
                    continue

            # 按时间排序
            self.price_data.sort(key=lambda x: x[1])

            logger.info(f"📚 加载历史数据 - {self.trading_pair}: {len(self.price_data)}条记录")
            if self.price_data:
                oldest = self.price_data[0][1].strftime('%H:%M:%S')
                newest = self.price_data[-1][1].strftime('%H:%M:%S')
                logger.info(f"   时间范围: {oldest} - {newest}")

        except Exception as e:
            logger.error(f"加载历史数据失败: {str(e)}")
            # 如果加载失败，继续使用空数据
            self.price_data = []

    def add_price(self, price: float, timestamp: datetime):
        """添加价格数据"""
        # 清理过期数据（保留最长区间的数据）
        max_interval = max(self.intervals)
        cutoff_time = timestamp - timedelta(minutes=max_interval)
        old_count = len(self.price_data)
        self.price_data = [(p, t) for p, t in self.price_data if t >= cutoff_time]
        new_count = len(self.price_data)

        # 添加新数据
        self.price_data.append((price, timestamp))

        # 记录数据变化
        if old_count != new_count:
            logger.debug(f"数据清理: {old_count} -> {new_count} (清理了{old_count - new_count}条过期数据)")
        logger.debug(f"添加价格数据: {price:.2f} @ {timestamp.strftime('%H:%M:%S')}, 总数据点: {len(self.price_data)}")

    def get_interval_change(self, interval_minutes: int) -> dict:
        """获取指定区间的价格变化"""
        if not self.price_data:
            return {'change': 0.0, 'direction': 'none', 'data_count': 0}

        # 获取指定区间内的数据
        cutoff_time = self.price_data[-1][1] - timedelta(minutes=interval_minutes)
        interval_data = [(p, t) for p, t in self.price_data if t >= cutoff_time]

        if len(interval_data) < 2:
            return {'change': 0.0, 'direction': 'none', 'data_count': len(interval_data)}

        # 计算价格变化
        prices = [p for p, t in interval_data]
        start_price = prices[0]
        end_price = prices[-1]
        max_price = max(prices)
        min_price = min(prices)

        # 计算变化幅度（最大波动）
        if min_price > 0:
            max_change = (max_price - min_price) / min_price
        else:
            max_change = 0.0

        # 计算趋势方向（起始到结束）
        if start_price > 0:
            trend_change = (end_price - start_price) / start_price
        else:
            trend_change = 0.0

        # 判断方向
        if trend_change > 0.005:  # 0.5%以上算上涨
            direction = 'up'
        elif trend_change < -0.005:  # -0.5%以下算下跌
            direction = 'down'
        else:
            direction = 'sideways'

        return {
            'change': max_change,
            'trend_change': trend_change,
            'direction': direction,
            'data_count': len(interval_data),
            'start_price': start_price,
            'end_price': end_price,
            'max_price': max_price,
            'min_price': min_price
        }

    def get_all_intervals_info(self) -> dict:
        """获取所有区间的信息"""
        result = {}
        for interval in self.intervals:
            result[f'{interval}m'] = self.get_interval_change(interval)
        return result

    def detect_continuous_trend(self, trend_threshold: float = 0.02, required_intervals: int = 3) -> dict:
        """检测持续趋势"""
        intervals_info = self.get_all_intervals_info()

        # 检查是否有足够的区间数据
        valid_intervals = [info for info in intervals_info.values() if info['data_count'] >= 2]
        if len(valid_intervals) < required_intervals:
            return {'trend': 'none', 'strength': 0.0, 'intervals': []}

        # 检查持续上涨
        up_intervals = []
        down_intervals = []

        for interval_name, info in intervals_info.items():
            if info['data_count'] >= 2:
                if info['direction'] == 'up' and info['trend_change'] >= trend_threshold:
                    up_intervals.append(interval_name)
                elif info['direction'] == 'down' and info['trend_change'] <= -trend_threshold:
                    down_intervals.append(interval_name)

        # 判断持续趋势
        if len(up_intervals) >= required_intervals:
            avg_change = sum(intervals_info[name]['trend_change'] for name in up_intervals) / len(up_intervals)
            return {'trend': 'continuous_up', 'strength': avg_change, 'intervals': up_intervals}
        elif len(down_intervals) >= required_intervals:
            avg_change = sum(intervals_info[name]['trend_change'] for name in down_intervals) / len(down_intervals)
            return {'trend': 'continuous_down', 'strength': abs(avg_change), 'intervals': down_intervals}
        else:
            return {'trend': 'none', 'strength': 0.0, 'intervals': []}




class KlineData:
    def __init__(self):
        self.open = None
        self.high = None
        self.low = None
        self.close = None
        self.volume = None
        self.timestamp = None

    def update(self, data: list):
        """更新K线数据
        data格式: [timestamp, open, high, low, close, volume]
        所有值都是字符串格式
        """
        try:
            # 确保数据是列表格式
            if not isinstance(data, list):
                raise ValueError(f"数据必须是列表格式，当前格式: {type(data)}")
            
            # 记录原始数据
            logger.debug(f"正在处理K线数据: {data}")
            
            # 解析数据
            self.timestamp = datetime.fromtimestamp(int(data[0]) / 1000)
            self.open = float(data[1])
            self.high = float(data[2])
            self.low = float(data[3])
            self.close = float(data[4])
            self.volume = float(data[5])
            
            logger.debug(f"K线数据更新成功 - 时间: {self.timestamp}, 开盘价: {self.open}, 收盘价: {self.close}")
        except Exception as e:
            logger.error(f"更新K线数据失败: {str(e)}, 数据: {data}")
            logger.exception("K线数据更新详细错误：")
            raise

    def to_dict(self):
        """转换为字典格式"""
        return {
            'open': self.open,
            'high': self.high,
            'low': self.low,
            'close': self.close,
            'volume': self.volume,
            'timestamp': self.timestamp
        }

class PriceMonitor:
    """价格监控主类 - 优化架构和性能"""

    def __init__(self):
        logger.info("初始化价格监控系统...")

        # 数据存储
        self._price_levels = {}   # 存储每个交易对的智能区间监控器
        self._kline_data = {}     # 存储每个交易对的K线数据

        # 组件初始化
        self._db_manager = DatabaseManager()
        self._notification_sender = NotificationSender(Config.WECHAT_ROBOT_URL)

        # 智能限流管理器
        self._rate_limit_manager = SmartRateLimitManager()
        self._last_trigger_reason = {}  # 存储上次触发的原因，避免重复推送
        self._last_trigger_price = {}   # 存储上次触发时的价格
        self._last_trigger_intervals = {}  # 存储上次触发的区间信息

        # 初始化智能多区间监控器（不再使用WebSocket）
        self._init_monitors()

        logger.info("价格监控系统初始化完成")
        logger.warning("🛡️ 智能限流管理器已启用 - 短期:1分钟3次, 中期:5分钟8次, 长期:30分钟15次")

    def _init_monitors(self):
        """初始化监控器（不使用WebSocket）"""
        try:
            logger.warning("开始初始化价格监控器...")  # 使用WARNING级别确保显示

            # 初始化所有配置的交易对
            for trading_pair in Config.TRADING_PAIRS:
                logger.warning(f"初始化交易对监控: {trading_pair}")  # 使用WARNING级别确保显示

                # 初始化智能多区间监控（传递数据库管理器以加载历史数据）
                intervals = Config.PRICE_MONITOR_CONFIG['time_intervals']
                self._price_levels[trading_pair] = SmartIntervalMonitor(
                    intervals=intervals,
                    db_manager=self._db_manager,
                    trading_pair=trading_pair
                )
                self._kline_data[trading_pair] = KlineData()

            logger.warning("价格监控器初始化成功")  # 使用WARNING级别确保显示

        except Exception as e:
            logger.error(f"初始化价格监控器失败: {str(e)}")





    def _calculate_dynamic_threshold(self, current_price: float) -> dict:
        """根据当前价格和配置动态计算阈值"""
        config = Config.PRICE_MONITOR_CONFIG

        # 从配置获取参数
        target_points = config['target_points']
        min_threshold = config['min_threshold_percent']
        max_threshold = config['max_threshold_percent']
        trend_ratio = config['trend_threshold_ratio']

        # 计算动态阈值
        dynamic_threshold = target_points / current_price

        # 限制阈值范围
        original_threshold = dynamic_threshold
        dynamic_threshold = max(min_threshold, min(dynamic_threshold, max_threshold))

        # 趋势阈值设为价格变化阈值的指定比例
        trend_threshold = dynamic_threshold * trend_ratio

        # 添加调试信息
        logger.debug(f"动态阈值计算: 价格={current_price:.2f}, 目标点数={target_points}, "
                    f"原始阈值={original_threshold:.4%}, 最终阈值={dynamic_threshold:.4%}, "
                    f"趋势阈值={trend_threshold:.4%}")

        return {
            'price_threshold': dynamic_threshold,
            'trend_threshold': trend_threshold,
            'target_points': target_points,
            'actual_points': current_price * dynamic_threshold
        }

    def _should_trigger_agent(self, trading_pair: str, current_price: float) -> Tuple[bool, str]:
        """智能触发逻辑 - 检测价格变化和持续趋势"""
        try:
            interval_monitor = self._price_levels[trading_pair]
            config = Config.PRICE_MONITOR_CONFIG

            # 使用动态阈值计算
            dynamic_config = self._calculate_dynamic_threshold(current_price)
            threshold = dynamic_config['price_threshold']
            threshold_points = dynamic_config['actual_points']

            # 获取所有区间信息
            all_intervals = interval_monitor.get_all_intervals_info()
            trigger_reasons = []

            # 检查价格变化触发
            triggered_intervals = []
            for interval_name, info in all_intervals.items():
                if info['data_count'] >= 2:
                    actual_points = info['max_price'] - info['min_price']
                    if actual_points >= threshold_points:
                        trigger_reasons.append(f"{interval_name}内价格从{info['min_price']:.2f}波动到{info['max_price']:.2f}，变化{actual_points:.2f}点({info['change']:.3%})超过阈值{threshold_points:.2f}点")
                        triggered_intervals.append(f"{interval_name}({actual_points:.1f}点)")

            # 检测持续趋势
            trend_threshold = dynamic_config['trend_threshold']
            trend_points = current_price * trend_threshold
            required_intervals = config['trend_intervals']

            trend_info = interval_monitor.detect_continuous_trend(
                trend_threshold=trend_threshold,
                required_intervals=required_intervals
            )

            if trend_info['trend'] != 'none':
                if trend_info['trend'] == 'continuous_up':
                    trigger_reasons.append(f"持续上涨趋势，强度{trend_info['strength']:.2%}，涉及区间{trend_info['intervals']}")
                elif trend_info['trend'] == 'continuous_down':
                    trigger_reasons.append(f"持续下跌趋势，强度{trend_info['strength']:.2%}，涉及区间{trend_info['intervals']}")

            # 判断是否触发
            if trigger_reasons:
                trigger_reason = "; ".join(trigger_reasons)
                logger.warning(f"🚨 触发告警 - {trading_pair}: 价格{current_price:.2f}, 阈值{threshold_points:.1f}点")
                if triggered_intervals:
                    logger.warning(f"    价格触发区间: {', '.join(triggered_intervals)}")
                if trend_info['trend'] != 'none':
                    logger.warning(f"    趋势触发: {trend_info['trend']} (强度{trend_info['strength']:.2%})")
                return True, trigger_reason
            else:
                # 只在DEBUG模式下输出未触发信息
                logger.debug(f"✗ 未触发 - {trading_pair}: {current_price:.2f} (阈值: {threshold_points:.1f}点)")

            return False, ""

        except Exception as e:
            logger.error(f"触发检查失败: {str(e)}")
            return False, f"检查失败: {str(e)}"

    def _process_price_update(self, trading_pair: str, current_price: float, timestamp: datetime):
        """智能价格更新处理"""
        if trading_pair not in self._price_levels:
            return

        # 更新智能多区间监控器
        self._price_levels[trading_pair].add_price(current_price, timestamp)

        # 打印价格变化区间详情
        self._log_interval_details(trading_pair, current_price)

        # 保存价格数据到数据库
        self._db_manager.save_price_data(
            trading_pair=trading_pair,
            price=current_price,
            timestamp=timestamp
        )

        # 检查是否触发
        should_trigger, trigger_reason = self._should_trigger_agent(trading_pair, current_price)

        if should_trigger:
            logger.warning(f"🚨 触发告警! - {trading_pair}: {trigger_reason}")
            self._handle_trigger_alert(trading_pair, current_price, trigger_reason, timestamp)

    def _handle_trigger_alert(self, trading_pair: str, current_price: float,
                             trigger_reason: str, timestamp: datetime):
        """智能告警处理 - 增强重复推送防护"""
        current_time = datetime.now()
        
        # 使用智能限流管理器检查是否允许发送
        last_reason = self._last_trigger_reason.get(trading_pair, "")
        last_price = self._last_trigger_price.get(trading_pair, 0.0)
        
        # 计算价格变化幅度
        price_change_ratio = abs(current_price - last_price) / last_price if last_price > 0 else 1.0
        
        # 检查限流
        can_send, reason = self._rate_limit_manager.can_send_alert(
            trading_pair=trading_pair,
            trigger_reason=trigger_reason,
            price_change_ratio=price_change_ratio,
            last_trigger_reason=last_reason
        )
        
        if not can_send:
            logger.info(f"{trading_pair} 限流跳过推送: {reason}")
            return

        # 获取多区间信息
        all_intervals = self._price_levels[trading_pair].get_all_intervals_info()

        # 计算主要价格变化（取最大变化的区间）
        max_change = 0.0
        max_change_points = 0.0
        for interval_name, info in all_intervals.items():
            if info['data_count'] >= 2:
                change_points = info['max_price'] - info['min_price']
                if change_points > max_change_points:
                    max_change = info['change']
                    max_change_points = change_points

        # 发送简化的企业微信消息
        success = self._notification_sender.send_wechat_message(
            trading_pair=trading_pair,
            price=current_price,
            price_change=max_change,
            trigger_reason=trigger_reason,
            interval_info=all_intervals
        )


        self._db_manager.save_alert_history(
            trading_pair=trading_pair,
            price=current_price,
            price_change=max_change,
            volatility=max_change,  # 使用价格变化作为波动率
            trigger_reason=trigger_reason,
            timestamp=timestamp
        )

        # 更新触发原因和价格
        self._last_trigger_reason[trading_pair] = trigger_reason
        self._last_trigger_price[trading_pair] = current_price

        # 记录告警到限流管理器
        self._rate_limit_manager.record_alert(trading_pair, trigger_reason)

        logger.warning(f"✅ 已发送告警 - {trading_pair} (价格: {current_price:.2f})")

        ##  出发调用agent 交易团队
        main()



    def _log_interval_details(self, trading_pair: str, current_price: float):
        """打印价格变化区间详情 - 简化日志输出"""
        try:
            interval_monitor = self._price_levels[trading_pair]
            all_intervals = interval_monitor.get_all_intervals_info()

            # 计算动态阈值
            dynamic_config = self._calculate_dynamic_threshold(current_price)
            threshold_points = dynamic_config['actual_points']

            # 只在有触发条件时才输出详细信息
            has_trigger = False
            trigger_intervals = []
            
            for interval_name, info in all_intervals.items():
                if info['data_count'] >= 2:
                    price_change_points = info['max_price'] - info['min_price']
                    if price_change_points >= threshold_points:
                        has_trigger = True
                        trigger_intervals.append(f"{interval_name}({price_change_points:.1f}点)")

            # 检测持续趋势
            trend_threshold = dynamic_config['trend_threshold']
            trend_info = interval_monitor.detect_continuous_trend(
                trend_threshold=trend_threshold,
                required_intervals=Config.PRICE_MONITOR_CONFIG['trend_intervals']
            )
            
            if trend_info['trend'] != 'none':
                has_trigger = True
                trigger_intervals.append(f"趋势({trend_info['trend']})")

            # 只有在有触发条件或者是WARNING级别时才输出详细信息
            if has_trigger:
                logger.warning(f"📊 {trading_pair} 价格分析 - 当前: {current_price:.2f}, 阈值: {threshold_points:.1f}点")
                logger.warning(f"🔥 触发区间: {', '.join(trigger_intervals)}")
            else:
                # 简化的日志输出，只显示基本信息
                logger.info(f"📊 {trading_pair}: {current_price:.2f} (阈值: {threshold_points:.1f}点, 无触发)")

        except Exception as e:
            logger.error(f"价格分析失败: {str(e)}")



    def process_message(self, message):
        """处理WebSocket消息 - 公共方法，供外部调用"""
        try:
            # 如果message是字符串，先解析为JSON对象
            if isinstance(message, str):
                message = json.loads(message)

            if 'data' not in message:
                return

            data = message['data']
            action = message.get('action', '')

            # 处理ticker数据
            if 'last' in data:
                current_price = float(data['last'])
                trading_pair = data.get('symbol', '').split('USDT')[0]
                timestamp = datetime.now()

                # 保存价格数据到数据库
                self._db_manager.save_price_data(
                    trading_pair=trading_pair,
                    price=current_price,
                    timestamp=timestamp,
                    volume=float(data.get('vol24h', 0)),
                    high=float(data.get('high24h', current_price)),
                    low=float(data.get('low24h', current_price))
                )

                # 处理价格更新
                self._process_price_update(trading_pair, current_price, timestamp)

            # 处理K线数据
            elif isinstance(data, list):
                arg = message.get('arg', {})
                inst_id = arg.get('instId', '') if arg else ''
                trading_pair = inst_id.split('USDT')[0] if inst_id else ''

                if not trading_pair or trading_pair not in self._kline_data:
                    return

                if action == 'snapshot':
                    # 处理历史数据
                    self._process_kline_snapshot(trading_pair, data)
                elif action == 'update':
                    # 处理实时更新数据（主要触发点）
                    self._process_kline_update(trading_pair, data)

        except Exception as e:
            logger.error(f"处理WebSocket消息失败: {str(e)}")
            logger.error(f"错误消息内容: {message}")
            logger.exception("详细错误信息：")

    def _process_kline_snapshot(self, trading_pair: str, data: list):
        """处理K线历史数据"""
        for kline in data:
            if isinstance(kline, list):
                self._kline_data[trading_pair].update(kline)
                kline_data = self._kline_data[trading_pair]

                logger.debug(f"处理历史K线 - 时间: {kline_data.timestamp}, 收盘价: {kline_data.close}")

                # 保存K线数据到数据库
                self._db_manager.save_kline_data(
                    trading_pair=trading_pair,
                    open_price=kline_data.open,
                    high_price=kline_data.high,
                    low_price=kline_data.low,
                    close_price=kline_data.close,
                    volume=kline_data.volume,
                    timestamp=kline_data.timestamp
                )

    def _process_kline_update(self, trading_pair: str, data: list):
        """处理K线实时更新数据"""
        if not isinstance(data[0], list):
            return

        kline = data[0]  # 获取第一个K线数据
        self._kline_data[trading_pair].update(kline)
        kline_data = self._kline_data[trading_pair]

        # 简化K线日志输出
        logger.info(f"📈 {trading_pair} K线: {kline_data.timestamp.strftime('%H:%M:%S')} "
                   f"收盘: {kline_data.close:.2f} (H:{kline_data.high:.2f} L:{kline_data.low:.2f})")

        # 保存K线数据到数据库
        self._db_manager.save_kline_data(
            trading_pair=trading_pair,
            open_price=kline_data.open,
            high_price=kline_data.high,
            low_price=kline_data.low,
            close_price=kline_data.close,
            volume=kline_data.volume,
            timestamp=kline_data.timestamp
        )

        # 使用K线收盘价更新价格历史和水平（这是主要的触发点）
        current_price = kline_data.close
        timestamp = kline_data.timestamp

        # 处理价格更新
        self._process_price_update(trading_pair, current_price, timestamp)



    def stop(self):
        """停止价格监控"""
        logger.info("正在停止价格监控系统...")

        # 关闭数据库连接
        self._db_manager.close_connections()

        logger.info("价格监控系统已停止")

 


if __name__ == "__main__":
    main()
