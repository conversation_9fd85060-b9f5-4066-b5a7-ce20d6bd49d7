
import requests
import akshare as ak
from datetime import datetime

base_url = "https://4a735ea38f8146198dc205d2e2d1bd28.z3c.jin10.com/"


# 获取最新市场快讯
def get_latest_market_news():
    url = base_url + "flash"
    headers = {
        "accept": "application/json, text/plain, */*",
        "accept-encoding": "identity",
        "accept-language": "zh-CN,zh;q=0.9",
        "handleerror": "true",
        "origin": "https://www.jin10.com",
        "priority": "u=1, i",
        "referer": "https://www.jin10.com/",
        "sec-ch-ua-mobile": "?0",
        "sec-ch-ua-platform": "Windows",
        "sec-fetch-dest": "empty",
        "sec-fetch-mode": "cors",
        "sec-fetch-site": "same-site",
        "user-agent": "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
        "x-app-id": "bVBF4FyRTn5NJF5n",
        "x-version": "1.0"
    }
    params = {
        "channel": "-8200",
        "vip": "1",
        "classify": "[2]",
    }
    r = requests.get(url, params=params, headers=headers)
    print("Status Code:", r.status_code)  # 先检查状态码
    if r.status_code != 200:
        print("Error:", r.status_code)
        return []

    data_json = r.json()
    data_ = data_json['data']
    if data_ is None:
        return []

    def extract_field(data: dict, field: str, default="") -> str:
        """从data中提取字段值，若字段不存在则返回默认值"""
        return data.get(field, default)

    # 遍历data_ 获取数据里面的data对象，过滤data 里面有vip_title字段的对象数据
    news_data = [
        {
            "pic": extract_field(data__, "pic"),
            "title": extract_field(data__, "title"),
            "source": extract_field(data__, "source"),
            "content": extract_field(data__, "content"),
            "source_link": extract_field(data__, "source_link"),  # 修复字段名错误
            "time": row['time'],
            "id": row['id']
        }
        for row in data_
        if 'vip_title' not in (data__ := row['data']) and 'pic' in data__
    ]

    return {"new_data": news_data, "data_source": "金十数据"}


#  金十数据-外汇-投机情绪报告
def speculative_sentiment_report():
    now_data = datetime.now().date().isoformat().replace("-", "")
    macro_fx_sentiment_df = ak.macro_fx_sentiment(start_date=now_data, end_date=now_data)
    remark = """
    金十数据-外汇-投机情绪报告
    外汇投机情绪报告显示当前市场多空仓位比例，数据由8家交易平台提供，涵盖11个主要货币对和1个黄金品种。
    报告内容: 品种: 澳元兑日元、澳元兑美元、欧元兑美元、欧元兑澳元、欧元兑日元、英镑兑美元、英镑兑日元、纽元兑美元、美元兑加元、美元兑瑞郎、美元兑日元以及现货黄金兑美元。
             数据: 由Shark - fx整合全球8家交易平台（ 包括 Oanda、 FXCM、 Insta、 Dukas、 MyFxBook以及FiboGroup） 的多空投机仓位数据而成。
    名词释义: 外汇投机情绪报告显示当前市场多空仓位比例，数据由8家交易平台提供，涵盖11个主要货币对和1个黄金品种。
    工具使用策略: Shark-fx声明表示，基于“主流通常都是错误的”的事实，当空头头寸超过60%，交易者就应该建立多头仓位； 同理，当市场多头头寸超过60%，交易者则应该建立空头仓位。此外，当多空仓位比例接近50%的情况下，我们则倾向于建议交易者不要进场，保持观望。
    """
    return {"speculative_sentiment_report": macro_fx_sentiment_df, "data_description": remark}


if __name__ == '__main__':
    # print(get_latest_market_news())
    # speculative_sentiment_report()

    # print("金十数据-比特币持仓报告")
    # print(ak.crypto_bitcoin_hold_report())
    print("-----------金十数据-其他-加密货币实时行情------------")
    crypto_js_spot_df = ak.crypto_js_spot()
    for index, row in crypto_js_spot_df.iterrows():
        print(row["市场"], row["交易品种"], row["最近报价"], row["涨跌额"], row["涨跌幅"], row["24小时最高"], row["24小时最低"], row["24小时成交量"], row["更新时间"])

    print("-----------华尔街见闻-日历-宏观------------")
    macro_info_ws_df =  ak.macro_info_ws(date="20250530")
    for index, row in macro_info_ws_df.iterrows():
        print(row["时间"], row["地区"], row["事件"], row["重要性"], row["今值"], row["预期"], row["前值"], row["链接"])

