import pandas as pd
import logging
from typing import Dict
from datetime import datetime
import os

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

try:
    import qlib
    from qlib.data import D
    from qlib.contrib.model import TwoStageLGBModel
    QLIB_AVAILABLE = True
except ImportError:
    QLIB_AVAILABLE = False
    logger.warning("Qlib not installed. Some features will be disabled.")


from .qlib_integration_tool import QlibIntegrationAnalyzer
# from qlib_integration_tool import QlibIntegrationAnalyzer


class QlibPredictionAnalyzer:
    """Qlib预测分析器"""
    name: str = "Qlib交易信号预测分析器"
    description: str = """
    使用Qlib风格的分析方法预测BTCUSDT的交易信号。
    主要功能：
    1. 基于历史K线数据预测未来价格走势
    2. 提供交易信号质量评估
    3. 集成风险评估和机会识别
    4. 计算入场价格、止盈止损位

    预测结果包括：
    - 信号方向（做多/做空/持有）
    - 信号置信度
    - 预期收益率
    - 风险评估
    - 入场价格
    - 止盈止损位
    """

    def __init__(self, model_path: str = "./qlib_models") -> None:
        """初始化分析器"""
        self.model_path = model_path
        self.analyzer = QlibIntegrationAnalyzer()
        self.model = None
        self.model_info = None
        self._load_model()

    def _load_model(self) -> None:
        """加载训练好的模型"""
        try:
            # 查找最新的模型文件
            if not os.path.exists(self.model_path):
                logger.warning(f"Model path {self.model_path} does not exist")
                return

            # 查找模型文件
            model_files = [f for f in os.listdir(self.model_path) if f.endswith('.json')]
            if not model_files:
                logger.warning("No model files found")
                return

            # 选择最新的模型文件
            latest_model = sorted(model_files)[-1]
            model_file_path = os.path.join(self.model_path, latest_model)

            # 加载模型信息
            self.model_info = pd.read_json(model_file_path, typ='series').to_dict()
            logger.info(f"Model loaded: {latest_model}")
            logger.info(f"Model info: {self.model_info}")

        except Exception as e:
            logger.error(f"Error loading model: {str(e)}")

    def _prepare_features(self, kline_type: int = 5) -> pd.DataFrame:
        """
        准备特征数据用于预测

        Args:
            kline_type: K线周期类型

        Returns:
            pd.DataFrame: 特征数据
        """
        try:
            # 使用Qlib集成分析器获取数据
            analysis_data = self.analyzer.get_qlib_analysis_data(kline_type)
            if 'error' in analysis_data:
                raise ValueError(f"Error getting analysis data: {analysis_data['error']}")
            
            # 获取原始数据
            raw_data = self.analyzer._get_raw_data(kline_type, limit=100)
            if not raw_data:
                raise ValueError("No raw data available")

            # 转换数据格式
            data = self.analyzer._convert_data(raw_data)

            # 添加技术指标
            data = self.analyzer._calculate_comprehensive_indicators(data)

            return data

        except Exception as e:
            logger.error(f"Error preparing features: {str(e)}")
            raise

    def _make_prediction(self, features: pd.DataFrame) -> Dict:
        """
        使用模型进行预测

        Args:
            features: 特征数据

        Returns:
            Dict: 预测结果
        """
        try:
            # 获取最新数据
            latest_data = features.iloc[-1]

            # 使用启发式方法进行预测
            prediction = self._heuristic_predict(latest_data, features)

            return prediction

        except Exception as e:
            logger.error(f"Error making prediction: {str(e)}")
            # 返回默认预测
            return {
                "signal": "hold",
                "confidence": 0.5,
                "expected_return": 0.0,
                "risk_level": "medium",
                "timestamp": datetime.now().isoformat()
            }

    def _heuristic_predict(self, latest_data: pd.Series, features: pd.DataFrame) -> Dict:
        """
        使用启发式方法进行预测

        Args:
            latest_data: 最新数据
            features: 特征数据

        Returns:
            Dict: 预测结果
        """
        try:
            signal = "hold"
            confidence = 0.5
            expected_return = 0.0
            risk_level = "medium"

            # 基于RSI判断
            rsi = latest_data.get('rsi_14', 50)
            if rsi is not None:
                if rsi < 30:
                    signal = "buy"
                    confidence = min(0.9, (30 - rsi) / 30 * 0.5 + 0.5)
                    expected_return = 0.02
                elif rsi > 70:
                    signal = "sell"
                    confidence = min(0.9, (rsi - 70) / 30 * 0.5 + 0.5)
                    expected_return = -0.02

            # 基于MACD判断
            macd_hist = latest_data.get('macd_histogram', 0)
            if macd_hist is not None:
                if macd_hist > 0:
                    if signal == "hold":
                        signal = "buy"
                    elif signal == "sell":
                        signal = "hold"
                    confidence = min(0.95, confidence + abs(macd_hist) * 10)
                elif macd_hist < 0:
                    if signal == "hold":
                        signal = "sell"
                    elif signal == "buy":
                        signal = "hold"
                    confidence = min(0.95, confidence + abs(macd_hist) * 10)

            # 基于趋势判断
            trend_strength = latest_data.get('trend_strength', 0)
            if trend_strength is not None:
                if abs(trend_strength) > 0.5:
                    if trend_strength > 0 and signal != "sell":
                        signal = "buy"
                        expected_return += 0.01
                    elif trend_strength < 0 and signal != "buy":
                        signal = "sell"
                        expected_return -= 0.01
                    confidence = min(0.95, confidence + abs(trend_strength) * 0.3)

            # 风险评估
            volatility = latest_data.get('atr_14', latest_data['close'] * 0.02)
            if volatility is not None:
                price = latest_data['close']
                vol_ratio = volatility / price
                if vol_ratio > 0.05:  # 波动率大于5%
                    risk_level = "high"
                elif vol_ratio < 0.02:  # 波动率小于2%
                    risk_level = "low"
                else:
                    risk_level = "medium"

            return {
                "signal": signal,
                "confidence": round(confidence, 2),
                "expected_return": round(expected_return, 4),
                "risk_level": risk_level,
                "timestamp": datetime.now().isoformat(),
                "features_used": ["rsi", "macd", "trend", "volatility"]
            }

        except Exception as e:
            logger.error(f"Error in heuristic prediction: {str(e)}")
            return {
                "signal": "hold",
                "confidence": 0.5,
                "expected_return": 0.0,
                "risk_level": "medium",
                "timestamp": datetime.now().isoformat()
            }

    def _calculate_trade_levels(self, features: pd.DataFrame, signal: str) -> Dict:
        """
        计算交易水平（入场价、止盈止损）

        Args:
            features: 特征数据
            signal: 交易信号

        Returns:
            Dict: 交易水平信息
        """
        try:
            latest_data = features.iloc[-1]
            current_price = float(latest_data['close'])
            
            # 获取ATR用于计算止损
            atr = latest_data.get('atr_14', current_price * 0.02)
            
            # 获取关键支撑阻力位
            key_levels = self.analyzer._find_smart_levels(features)
            support = key_levels.get('support')
            resistance = key_levels.get('resistance')
            
            if signal == "buy":
                # 买入信号
                entry_price = current_price
                
                # 止损：ATR的2倍或关键支撑位，取更近的
                stop_loss = entry_price - (atr * 2)
                if support is not None and support > stop_loss:
                    stop_loss = support
                
                # 止盈：ATR的4倍或关键阻力位，取更近的
                take_profit = entry_price + (atr * 4)
                if resistance is not None and resistance < take_profit:
                    take_profit = resistance
                    
            elif signal == "sell":
                # 卖出信号
                entry_price = current_price
                
                # 止损：ATR的2倍或关键阻力位，取更近的
                stop_loss = entry_price + (atr * 2)
                if resistance is not None and resistance < stop_loss:
                    stop_loss = resistance
                
                # 止盈：ATR的4倍或关键支撑位，取更近的
                take_profit = entry_price - (atr * 4)
                if support is not None and support > take_profit:
                    take_profit = support
            else:
                # 持有信号
                entry_price = current_price
                stop_loss = current_price - (atr * 2)
                take_profit = current_price + (atr * 4)
            
            return {
                "entry_price": round(entry_price, 2),
                "stop_loss": round(stop_loss, 2),
                "take_profit": round(take_profit, 2),
                "risk_reward_ratio": round(abs(take_profit - entry_price) / abs(entry_price - stop_loss), 2) if stop_loss != entry_price else 2.0
            }
            
        except Exception as e:
            logger.error(f"Error calculating trade levels: {str(e)}")
            current_price = float(features.iloc[-1]['close']) if not features.empty else 0
            return {
                "entry_price": round(current_price, 2),
                "stop_loss": round(current_price * 0.98, 2),  # 默认2%止损
                "take_profit": round(current_price * 1.04, 2),  # 默认4%止盈
                "risk_reward_ratio": 2.0
            }

    def predict(self, kline_type: int = 5, prediction_horizon: int = 1) -> Dict:
        """执行预测的主要逻辑"""
        try:
            logger.info(f"Making prediction for k_line_cycle={kline_type}, horizon={prediction_horizon}")

            # 准备特征数据
            features = self._prepare_features(kline_type)

            # 进行预测
            prediction = self._make_prediction(features)

            # 添加时间框架信息
            timeframe_map = {3: "15min", 4: "30min", 5: "1H", 7: "4H", 8: "1D"}
            prediction["timeframe"] = timeframe_map.get(kline_type, "Unknown")
            prediction["prediction_horizon"] = prediction_horizon

            # 添加信号质量评估
            signal_quality = self._evaluate_signal_quality(prediction, features.iloc[-1])
            prediction["signal_quality"] = signal_quality

            # 计算交易水平
            trade_levels = self._calculate_trade_levels(features, prediction["signal"])
            prediction["trade_levels"] = trade_levels

            # 添加当前价格
            if not features.empty:
                prediction["current_price"] = float(features.iloc[-1]['close'])

            return prediction

        except Exception as e:
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }

    def _evaluate_signal_quality(self, prediction: Dict, latest_features: pd.Series) -> Dict:
        """
        评估信号质量

        Args:
            prediction: 预测结果
            latest_features: 最新特征数据

        Returns:
            Dict: 信号质量评估
        """
        try:
            quality_score = prediction.get('confidence', 0.5) * 50  # 基础分数

            # 基于技术指标一致性加分
            rsi = latest_features.get('rsi_14', 50)
            macd_hist = latest_features.get('macd_histogram', 0)
            trend_strength = latest_features.get('trend_strength', 0)

            # RSI和MACD一致性
            if rsi is not None and macd_hist is not None:
                if (rsi < 30 and macd_hist > 0) or (rsi > 70 and macd_hist < 0):
                    quality_score += 10  # 一致性加分

            # 趋势强度加分
            if trend_strength is not None:
                quality_score += min(20, abs(trend_strength) * 20)

            # 确定质量等级
            if quality_score >= 80:
                quality_level = "ALPHA"
            elif quality_score >= 60:
                quality_level = "BETA"
            elif quality_score >= 40:
                quality_level = "GAMMA"
            else:
                quality_level = "WEAK"

            return {
                "quality_level": quality_level,
                "quality_score": round(quality_score, 2),
                "recommendation": self._get_recommendation(quality_level, prediction['signal'])
            }

        except Exception as e:
            logger.error(f"Error evaluating signal quality: {str(e)}")
            return {
                "quality_level": "WEAK",
                "quality_score": 30,
                "recommendation": "谨慎观望"
            }

    def _get_recommendation(self, quality_level: str, signal: str) -> str:
        """
        根据信号质量和方向获取推荐

        Args:
            quality_level: 信号质量等级
            signal: 信号方向

        Returns:
            str: 推荐操作
        """
        recommendations = {
            ("ALPHA", "buy"): "强烈买入",
            ("ALPHA", "sell"): "强烈卖出",
            ("ALPHA", "hold"): "持有观察",
            ("BETA", "buy"): "买入",
            ("BETA", "sell"): "卖出",
            ("BETA", "hold"): "谨慎持有",
            ("GAMMA", "buy"): "轻仓买入",
            ("GAMMA", "sell"): "轻仓卖出",
            ("GAMMA", "hold"): "观望",
            ("WEAK", "buy"): "非常谨慎买入",
            ("WEAK", "sell"): "非常谨慎卖出",
            ("WEAK", "hold"): "强烈建议观望"
        }

        return recommendations.get((quality_level, signal), "谨慎观望")

    def get_multi_timeframe_predictions(self) -> Dict:
        """获取多时间框架预测（仅包括1H、4H、1D）"""
        try:
            # 只包含1H、4H、1D时间框架
            timeframes = {
                '1H': 5,
                '4H': 7,
                '1D': 8
            }
            
            predictions = {}
            for tf_name, tf_code in timeframes.items():
                prediction = self.predict(tf_code)
                predictions[tf_name] = prediction
                
            return {
                "predictions": predictions,
                "analysis_timestamp": datetime.now().isoformat()
            }
        except Exception as e:
            return {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().isoformat()
            }


def main():
    """主函数，用于直接执行预测分析"""
    print("=== Qlib预测分析器 ===")
    
    # 创建分析器实例
    analyzer = QlibPredictionAnalyzer()
    
    # 获取多时间框架预测
    print("\n--- 多时间框架预测分析 ---")
    multi_predictions = analyzer.get_multi_timeframe_predictions()
    
    if "status" in multi_predictions and multi_predictions["status"] == "error":
        print(f"预测分析出错: {multi_predictions['message']}")
        return
    
    predictions = multi_predictions.get("predictions", {})
    
    for timeframe, prediction in predictions.items():
        if "status" in prediction and prediction["status"] == "error":
            print(f"{timeframe} 预测出错: {prediction['message']}")
            continue
            
        print(f"\n{timeframe} 预测结果:")
        print(f"  当前价格: {prediction.get('current_price', 'N/A')}")
        print(f"  信号方向: {prediction.get('signal', 'N/A')}")
        print(f"  置信度: {prediction.get('confidence', 0.0):.2f}")
        print(f"  预期收益率: {prediction.get('expected_return', 0.0):.4f}")
        print(f"  风险等级: {prediction.get('risk_level', 'N/A')}")
        
        signal_quality = prediction.get('signal_quality', {})
        print(f"  信号质量: {signal_quality.get('quality_level', 'N/A')} (评分: {signal_quality.get('quality_score', 0)})")
        print(f"  推荐操作: {signal_quality.get('recommendation', 'N/A')}")
        
        # 显示交易水平
        trade_levels = prediction.get('trade_levels', {})
        if trade_levels:
            print(f"  入场价格: {trade_levels.get('entry_price', 'N/A')}")
            print(f"  止损位: {trade_levels.get('stop_loss', 'N/A')}")
            print(f"  止盈位: {trade_levels.get('take_profit', 'N/A')}")
            print(f"  风险回报比: {trade_levels.get('risk_reward_ratio', 'N/A')}")
    
    print(f"\n分析完成时间: {multi_predictions.get('analysis_timestamp', 'N/A')}")


if __name__ == '__main__':
    main()