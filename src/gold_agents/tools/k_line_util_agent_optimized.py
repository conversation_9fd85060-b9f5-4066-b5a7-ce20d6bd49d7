import pandas as pd
import numpy as np
import logging
from typing import Dict, Union, Tuple
# from market_database_manager import get_market_data_by_symbol
from .market_database_manager import get_market_data_by_symbol

# 配置基础日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

KLIN_TYPE_MAP = {
    1: "1m", 2: "5m", 3: "15m", 4: "30m", 5: "1H",
    6: "2H", 7: "4H", 8: "1D", 9: "1W", 10: "1M"
}

symbol = "BTCUSDT"

class AgentOptimizedKlineAnalyzer:
    """
    Agent优化的K线分析器
    只计算综合指标，不生成推荐信号，让agent自行判断
    """

    def __init__(self):
        self.performance_metrics = {}
        # 🚀 复利策略与资金管理
        self.trading_history = []
        self.consecutive_wins = 0
        self.consecutive_losses = 0
        self.current_win_rate = 0.6  # 初始胜率
        self.compound_multiplier = 1.0  # 复利倍数
        self.last_performance_update = None
        # 🎯 智能机会捕获参数
        self.opportunity_sensitivity = 0.8  # 机会敏感度（0.5-1.0，越高越敏感）
        self.entry_precision_mode = True    # 精准入场模式
        self.adaptive_thresholds = True     # 自适应阈值
        self.max_compound_factor = 5.0      # 最大复利倍数

    def get_optimized_kline_data(self, kline_type=5) -> Dict:
        """
        获取优化后的K线数据
        只计算指标，不生成信号
        """
        try:
            time_type = KLIN_TYPE_MAP.get(kline_type)
            if not time_type:
                return {"error": f"Unsupported kline_type: {kline_type}"}

            # 🎯 智能数据获取 - 根据时间框架调整数据量
            data_requirements = {
                "15m": 100, "30m": 100, "1H": 150, "4H": 200, "1D": 300
            }
            min_required = data_requirements.get(time_type, 150)

            raw_data = self._get_raw_data(kline_type, limit=min_required)
            if not raw_data or len(raw_data) < 30:
                return {"error": f"Insufficient data: only {len(raw_data) if raw_data else 0} records, need at least 30"}

            # 转换和计算
            data = self._convert_data(raw_data)
            if len(data) < 30:
                return {"error": f"Insufficient converted data: {len(data)} records, need at least 30"}

            data = self._calculate_comprehensive_indicators(data, time_type)

            return {
                "symbol": symbol,
                "timeframe": time_type,
                "current_price": float(data.iloc[-1]['close']),
                "indicators": self._extract_indicators(data),
                "key_levels": self._find_smart_levels(data),
                "market_state": self._classify_market_state(data),
                "last_update": data.index[-1].isoformat() if hasattr(data.index[-1], 'isoformat') else str(data.index[-1])
            }

        except Exception as e:
            logger.error(f"K线分析异常: {str(e)}")
            return {"error": f"Analysis error: {str(e)}"}

    def _get_raw_data(self, kline_type, limit=150):
        """🎯 智能获取原始数据 - 根据分析需求调整数据量"""
        time_type = KLIN_TYPE_MAP.get(kline_type)
        if not time_type:
            return []

        # 🚀 根据时间框架智能调整数据量
        optimal_limits = {
            "15m": 120,  # 短期高频分析
            "30m": 120,  # 短期高频分析
            "1H": 180,   # 主要交易框架
            "4H": 250,   # 中期趋势分析
            "1D": 350    # 长期趋势分析
        }

        actual_limit = optimal_limits.get(time_type, limit)
        return sorted(get_market_data_by_symbol(type=time_type, limit=actual_limit), key=lambda x: x.open_time)

    def _convert_data(self, raw_data):
        """转换数据格式"""
        df = pd.DataFrame([{
            'time': pd.to_datetime(int(k.open_time), unit='ms', utc=True).tz_convert('Asia/Shanghai'),
            'timestamp': k.open_time,
            'open': float(k.open),
            'high': float(k.high),
            'low': float(k.low),
            'close': float(k.close),
            'volume': float(k.volume)
        } for k in raw_data])

        df.set_index('time', inplace=True)
        return df

    def _calculate_comprehensive_indicators(self, data: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """
        计算综合技术指标
        不生成信号，只提供计算好的指标数据
        """
        # 1. 移动平均线系统
        data = self._calculate_moving_averages(data, timeframe)

        # 2. 趋势强度指标
        data = self._calculate_trend_indicators(data, timeframe)

        # 3. 动量指标
        data = self._calculate_momentum_indicators(data, timeframe)

        # 4. 成交量指标
        data = self._calculate_volume_indicators(data, timeframe)

        # 5. 波动率指标
        data = self._calculate_volatility_indicators(data, timeframe)

        # 6. 价格结构指标
        data = self._calculate_price_structure_indicators(data, timeframe)

        return data

    def _calculate_moving_averages(self, data: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """移动平均线系统"""
        # 🔧 修复：确保所有时间框架都有基础EMA周期
        base_periods = [8, 21, 50]  # 所有时间框架必须的基础周期

        # 根据时间框架调整参数
        if timeframe in ["15m", "30m"]:
            periods = [8, 21, 50, 55]  # 🔧 添加ema_50支持
            sensitivity_multiplier = 1.2
        elif timeframe == "1H":
            periods = [8, 21, 50, 100]  # 添加ema_8支持
            sensitivity_multiplier = 1.0
        else:
            periods = [8, 21, 50, 100, 200]  # 添加ema_8支持
            sensitivity_multiplier = 0.8

        # 计算多个周期的移动平均线
        for period in periods:
            data[f'sma_{period}'] = data['close'].rolling(window=period).mean()
            data[f'ema_{period}'] = data['close'].ewm(span=period, adjust=False).mean()

        # 计算均线排列关系（不判断方向，只提供数据）
        # 🔧 修复：添加安全检查
        if 'ema_8' in data.columns and 'ema_21' in data.columns:
            data['ma_spacing'] = (data['ema_8'] - data['ema_21']) / data['ema_21']
            data['ma_trend_angle'] = np.arctan(data['ma_spacing']) * 180 / np.pi
        else:
            data['ma_spacing'] = 0.0
            data['ma_trend_angle'] = 0.0

        return data

    def _calculate_trend_indicators(self, data: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """趋势强度指标 - 优化版，提高精准度"""
        # 趋势强度计算
        data['trend_strength'] = 0.0

        # 基于均线排列的趋势强度 - 增强版
        data['ma_alignment_score'] = 0.0
        required_emas = ['ema_8', 'ema_21', 'ema_50']

        if all(col in data.columns for col in required_emas):
            # 精确的均线排列评分 - 考虑均线间距和角度
            ema8_ema21_diff = (data['ema_8'] - data['ema_21']) / data['ema_21']
            ema21_ema50_diff = (data['ema_21'] - data['ema_50']) / data['ema_50']

            # 强多头排列：EMA8>EMA21>EMA50且间距足够大
            strong_bullish = (
                    (data['ema_8'] > data['ema_21']) &
                    (data['ema_21'] > data['ema_50']) &
                    (ema8_ema21_diff > 0.003) &  # 至少0.3%的间距
                    (ema21_ema50_diff > 0.002)   # 至少0.2%的间距
            )

            # 强空头排列：EMA8<EMA21<EMA50且间距足够大
            strong_bearish = (
                    (data['ema_8'] < data['ema_21']) &
                    (data['ema_21'] < data['ema_50']) &
                    (ema8_ema21_diff < -0.003) &
                    (ema21_ema50_diff < -0.002)
            )

            # 弱排列（间距不足）
            weak_bullish = (
                    (data['ema_8'] > data['ema_21']) &
                    (data['ema_21'] > data['ema_50']) &
                    ~strong_bullish
            )

            weak_bearish = (
                    (data['ema_8'] < data['ema_21']) &
                    (data['ema_21'] < data['ema_50']) &
                    ~strong_bearish
            )

            # 评分：强排列1.0，弱排列0.5，混乱0.0
            data['ma_alignment_score'] = np.where(
                strong_bullish, 1.0,
                np.where(weak_bullish, 0.5,
                         np.where(strong_bearish, -1.0,
                                  np.where(weak_bearish, -0.5, 0.0)
                                  )
                         )
            )
        else:
            logger.warning(f"缺少必要的EMA指标，可用列: {list(data.columns)}")
            data['ma_alignment_score'] = 0.0

        # 价格动量 - 多周期动量评估
        data['price_momentum_5'] = data['close'].pct_change(5)
        data['price_momentum_10'] = data['close'].pct_change(10)
        data['price_momentum_20'] = data['close'].pct_change(20)

        # 趋势持续性 - 改进版
        data['trend_persistence'] = 0.0
        trend_periods = [3, 5, 8, 13, 21]  # 使用斐波那契数列

        for period in trend_periods:
            if len(data) > period:
                trend_direction = np.where(data['close'] > data['close'].shift(period), 1, -1)
                # 根据周期长短赋予不同权重
                weight = 1.0 / period
                data['trend_persistence'] += trend_direction * weight

        # 趋势加速度 - 新增
        data['trend_acceleration'] = 0.0
        if len(data) >= 10:
            # 短期动量变化率
            short_momentum_change = data['price_momentum_5'].diff()
            # 中期动量变化率
            mid_momentum_change = data['price_momentum_10'].diff()

            data['trend_acceleration'] = (short_momentum_change + mid_momentum_change) / 2

        # 综合趋势强度 - 优化权重分配
        ma_weight = 0.4  # 均线排列权重
        persistence_weight = 0.3  # 持续性权重
        acceleration_weight = 0.3  # 加速度权重

        # 标准化持续性分数
        max_persistence = sum(1.0 / period for period in trend_periods)
        normalized_persistence = data['trend_persistence'] / max_persistence

        # 标准化加速度分数（限制在-1到1之间）
        normalized_acceleration = np.tanh(data['trend_acceleration'] * 100)

        data['trend_strength'] = (
                data['ma_alignment_score'] * ma_weight +
                normalized_persistence * persistence_weight +
                normalized_acceleration * acceleration_weight
        )

        return data

    def _calculate_momentum_indicators(self, data: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """动量指标"""
        # RSI - 多个周期
        rsi_periods = [7, 14, 21] if timeframe in ["15m", "30m"] else [14, 21, 50]
        for period in rsi_periods:
            data[f'rsi_{period}'] = self._calculate_rsi(data['close'], period)

        # MACD - 多个参数组合
        if timeframe in ["15m", "30m"]:
            macd_configs = [(6, 13, 4), (8, 21, 5)]
        else:
            macd_configs = [(12, 26, 9), (21, 55, 13)]

        for i, (fast, slow, signal) in enumerate(macd_configs):
            data[f'macd_{i+1}'] = data['close'].ewm(span=fast).mean() - data['close'].ewm(span=slow).mean()
            data[f'macd_signal_{i+1}'] = data[f'macd_{i+1}'].ewm(span=signal).mean()
            data[f'macd_histogram_{i+1}'] = data[f'macd_{i+1}'] - data[f'macd_signal_{i+1}']

        # 随机指标
        data['stoch_k'] = self._calculate_stochastic(data, 14)
        data['stoch_d'] = data['stoch_k'].rolling(window=3).mean()

        # 威廉指标
        data['williams_r'] = self._calculate_williams_r(data, 14)

        return data

    def _calculate_volume_indicators(self, data: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """成交量指标"""
        # 成交量移动平均
        volume_periods = [10, 20, 50] if timeframe in ["15m", "30m"] else [20, 50, 100]
        for period in volume_periods:
            data[f'volume_ma_{period}'] = data['volume'].rolling(window=period).mean()
            data[f'volume_std_{period}'] = data['volume'].rolling(window=period).std()

        # 成交量比率
        data['volume_ratio'] = data['volume'] / data['volume_ma_20']

        # 成交量价格趋势
        data['volume_price_trend'] = (data['close'] - data['close'].shift(1)) * data['volume']

        # 资金流量指标
        data['mfi'] = self._calculate_mfi(data, 14)

        # 成交量加权平均价格
        data['vwap'] = (data['close'] * data['volume']).rolling(window=20).sum() / data['volume'].rolling(window=20).sum()

        return data

    def _calculate_volatility_indicators(self, data: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """波动率指标"""
        # ATR
        atr_periods = [10, 14, 21] if timeframe in ["15m", "30m"] else [14, 21, 50]
        for period in atr_periods:
            data[f'atr_{period}'] = self._calculate_atr(data, period)

        # 布林带
        bb_periods = [20, 50] if timeframe in ["15m", "30m"] else [20, 50]
        for period in bb_periods:
            bb_std = data['close'].rolling(window=period).std()
            bb_ma = data['close'].rolling(window=period).mean()
            data[f'bb_upper_{period}'] = bb_ma + (bb_std * 2)
            data[f'bb_lower_{period}'] = bb_ma - (bb_std * 2)
            data[f'bb_width_{period}'] = (data[f'bb_upper_{period}'] - data[f'bb_lower_{period}']) / bb_ma

        # 真实波动率
        data['true_range'] = self._calculate_true_range(data)
        data['volatility_ratio'] = data['true_range'] / data['close']

        return data

    def _calculate_price_structure_indicators(self, data: pd.DataFrame, timeframe: str) -> pd.DataFrame:
        """价格结构指标"""
        # 支撑阻力位
        data['pivot_point'] = (data['high'] + data['low'] + data['close']) / 3
        data['r1'] = 2 * data['pivot_point'] - data['low']
        data['s1'] = 2 * data['pivot_point'] - data['high']

        # 价格位置
        data['price_position'] = (data['close'] - data['low']) / (data['high'] - data['low'])

        # 价格通道
        lookback = 20 if timeframe in ["15m", "30m"] else 50
        data['price_channel_high'] = data['high'].rolling(window=lookback).max()
        data['price_channel_low'] = data['low'].rolling(window=lookback).min()
        data['price_channel_position'] = (data['close'] - data['price_channel_low']) / (data['price_channel_high'] - data['price_channel_low'])

        # 价格动量
        data['price_acceleration'] = data['close'].diff().diff()

        return data

    def _calculate_recent_performance(self) -> Dict:
        """📊 计算最近交易表现"""
        if len(self.trading_history) < 5:
            return {'insufficient_data': True}

        recent_trades = self.trading_history[-20:]
        wins = [t for t in recent_trades if t['result'] == 'win']
        losses = [t for t in recent_trades if t['result'] == 'loss']

        total_pnl = sum(t['pnl'] for t in recent_trades)
        avg_win = np.mean([t['pnl'] for t in wins]) if wins else 0
        avg_loss = np.mean([abs(t['pnl']) for t in losses]) if losses else 0

        return {
            'total_trades': len(recent_trades),
            'win_count': len(wins),
            'loss_count': len(losses),
            'win_rate': len(wins) / len(recent_trades) if recent_trades else 0,
            'total_pnl': total_pnl,
            'average_win': avg_win,
            'average_loss': avg_loss,
            'profit_factor': abs(avg_win / avg_loss) if avg_loss > 0 else float('inf')
        }

    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi.fillna(50)
        except Exception as e:
            logger.error(f"RSI计算异常: {str(e)}")
            return pd.Series([50] * len(prices), index=prices.index)

    def _calculate_stochastic(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算随机指标"""
        try:
            low_min = data['low'].rolling(window=period).min()
            high_max = data['high'].rolling(window=period).max()
            k_percent = 100 * ((data['close'] - low_min) / (high_max - low_min))
            return k_percent.fillna(50)
        except Exception as e:
            logger.error(f"随机指标计算异常: {str(e)}")
            return pd.Series([50] * len(data), index=data.index)

    def _calculate_williams_r(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算威廉指标"""
        try:
            high_max = data['high'].rolling(window=period).max()
            low_min = data['low'].rolling(window=period).min()
            wr = -100 * ((high_max - data['close']) / (high_max - low_min))
            return wr.fillna(-50)
        except Exception as e:
            logger.error(f"威廉指标计算异常: {str(e)}")
            return pd.Series([-50] * len(data), index=data.index)

    def _calculate_mfi(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算资金流量指标"""
        try:
            typical_price = (data['high'] + data['low'] + data['close']) / 3
            money_flow = typical_price * data['volume']

            positive_flow = money_flow.where(typical_price > typical_price.shift(1), 0)
            negative_flow = money_flow.where(typical_price < typical_price.shift(1), 0)

            positive_mf = positive_flow.rolling(window=period).sum()
            negative_mf = negative_flow.rolling(window=period).sum()

            mfi = 100 - (100 / (1 + positive_mf / negative_mf))
            return mfi.fillna(50)
        except Exception as e:
            logger.error(f"MFI计算异常: {str(e)}")
            return pd.Series([50] * len(data), index=data.index)

    def _calculate_atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算ATR指标"""
        try:
            high_low = data['high'] - data['low']
            high_close = np.abs(data['high'] - data['close'].shift())
            low_close = np.abs(data['low'] - data['close'].shift())

            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = ranges.max(axis=1)
            atr = true_range.rolling(window=period).mean()
            return atr.fillna(data['close'] * 0.02)
        except Exception as e:
            logger.error(f"ATR计算异常: {str(e)}")
            return pd.Series([data['close'].iloc[-1] * 0.02] * len(data), index=data.index)

    def _calculate_true_range(self, data: pd.DataFrame) -> pd.Series:
        """计算真实波动率"""
        try:
            high_low = data['high'] - data['low']
            high_close = np.abs(data['high'] - data['close'].shift())
            low_close = np.abs(data['low'] - data['close'].shift())

            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            return ranges.max(axis=1).fillna(0)
        except Exception as e:
            logger.error(f"真实波动率计算异常: {str(e)}")
            return pd.Series([0] * len(data), index=data.index)

    def _extract_indicators(self, data: pd.DataFrame) -> Dict:
        """提取所有指标数据，包含智能信号质量评估"""
        latest = data.iloc[-1]

        indicators = {}

        # 移动平均线
        ma_columns = [col for col in data.columns if col.startswith(('sma_', 'ema_'))]
        for col in ma_columns:
            indicators[col] = float(latest[col]) if pd.notna(latest[col]) else None

        # 趋势指标
        trend_columns = ['trend_strength', 'ma_alignment_score', 'price_momentum_5', 'price_momentum_10', 'price_momentum_20', 'trend_persistence']
        for col in trend_columns:
            if col in data.columns:
                indicators[col] = float(latest[col]) if pd.notna(latest[col]) else None

        # 动量指标
        momentum_columns = [col for col in data.columns if col.startswith(('rsi_', 'macd_', 'stoch_', 'williams_r'))]
        for col in momentum_columns:
            indicators[col] = float(latest[col]) if pd.notna(latest[col]) else None

        # 成交量指标
        volume_columns = [col for col in data.columns if col.startswith(('volume_', 'mfi', 'vwap'))]
        for col in volume_columns:
            indicators[col] = float(latest[col]) if pd.notna(latest[col]) else None

        # 波动率指标
        volatility_columns = [col for col in data.columns if col.startswith(('atr_', 'bb_', 'true_range', 'volatility_ratio'))]
        for col in volatility_columns:
            indicators[col] = float(latest[col]) if pd.notna(latest[col]) else None

        # 价格结构指标
        structure_columns = ['pivot_point', 'r1', 's1', 'price_position', 'price_channel_position', 'price_acceleration']
        for col in structure_columns:
            if col in data.columns:
                indicators[col] = float(latest[col]) if pd.notna(latest[col]) else None

        # 🎯 智能信号质量评估与精准入场点识别
        signal_quality_data = self._calculate_signal_quality(data, indicators)
        indicators.update(signal_quality_data)

        # 🚀 新增：精准入场点分析
        entry_points_analysis = self._analyze_precision_entry_points(data, indicators)
        indicators.update(entry_points_analysis)

        # 🎯 实时市场异动检测
        anomaly_detection = self._detect_market_anomalies(data, indicators)
        indicators.update(anomaly_detection)

        return indicators

    def _find_smart_levels(self, data: pd.DataFrame) -> Dict:
        """智能寻找关键价格水平"""
        try:
            if data.empty or len(data) < 20:
                return {"support": None, "resistance": None, "key_levels": []}

            current_price = float(data.iloc[-1]['close'])

            # 寻找近期高低点
            highs = []
            lows = []

            lookback = min(30, len(data) // 2)
            recent_data = data.tail(lookback)

            for i in range(2, len(recent_data) - 2):
                if (recent_data.iloc[i]['high'] > recent_data.iloc[i-1]['high'] and
                        recent_data.iloc[i]['high'] > recent_data.iloc[i+1]['high'] and
                        recent_data.iloc[i]['high'] > recent_data.iloc[i-2]['high'] and
                        recent_data.iloc[i]['high'] > recent_data.iloc[i+2]['high']):
                    highs.append(recent_data.iloc[i]['high'])

                if (recent_data.iloc[i]['low'] < recent_data.iloc[i-1]['low'] and
                        recent_data.iloc[i]['low'] < recent_data.iloc[i+1]['low'] and
                        recent_data.iloc[i]['low'] < recent_data.iloc[i-2]['low'] and
                        recent_data.iloc[i]['low'] < recent_data.iloc[i+2]['low']):
                    lows.append(recent_data.iloc[i]['low'])

            resistance_levels = [h for h in highs if h > current_price]
            support_levels = [l for l in lows if l < current_price]

            nearest_resistance = min(resistance_levels) if resistance_levels else None
            nearest_support = max(support_levels) if support_levels else None

            return {
                "support": nearest_support,
                "resistance": nearest_resistance,
                "key_levels": sorted(set(highs + lows)),
                "current_price": current_price
            }

        except Exception as e:
            logger.error(f"关键位计算异常: {str(e)}")
            return {"support": None, "resistance": None, "key_levels": [], "current_price": 0}

    def _classify_market_state(self, data: pd.DataFrame) -> Dict:
        """市场状态分类 - 只提供数据，不判断方向"""
        latest = data.iloc[-1]

        # 基于波动率分类
        if 'atr_14' in data.columns and pd.notna(latest['atr_14']):
            atr_ratio = latest['atr_14'] / data['atr_14'].rolling(window=20).mean().iloc[-1]
            if atr_ratio > 1.5:
                volatility_state = "high_volatility"
            elif atr_ratio < 0.7:
                volatility_state = "low_volatility"
            else:
                volatility_state = "normal_volatility"
        else:
            volatility_state = "unknown"

        # 基于趋势强度分类
        if 'trend_strength' in data.columns and pd.notna(latest['trend_strength']):
            if abs(latest['trend_strength']) > 1.0:
                trend_state = "strong_trend"
            elif abs(latest['trend_strength']) > 0.3:
                trend_state = "moderate_trend"
            else:
                trend_state = "weak_trend"
        else:
            trend_state = "unknown"

        return {
            "volatility_state": volatility_state,
            "trend_state": trend_state,
            "trend_strength_value": float(latest['trend_strength']) if 'trend_strength' in data.columns else None
        }

    def _calculate_signal_quality(self, data: pd.DataFrame, indicators: Dict) -> Dict:
        """🚀 智能信号质量评估 - 核心创新功能"""
        try:
            latest = data.iloc[-1]
            current_price = float(latest['close'])

            # 1. 🎯 精准趋势强度评分（基于优化后的趋势计算）
            trend_score = 0
            trend_strength = indicators.get('trend_strength')
            if trend_strength is not None:
                trend_val = abs(trend_strength)
                if trend_val > 0.8:  # 强趋势
                    trend_score = 35
                elif trend_val > 0.6:  # 中强趋势
                    trend_score = 28
                elif trend_val > 0.4:  # 中等趋势
                    trend_score = 20
                elif trend_val > 0.25:  # 弱趋势
                    trend_score = 12
                else:
                    trend_score = 5

            # 🚀 新增：价格动量加分
            price_momentum_5 = indicators.get('price_momentum_5')
            if price_momentum_5 is not None:
                momentum_5 = abs(price_momentum_5)
                if momentum_5 > 0.03:  # 3%以上动量
                    trend_score += 8
                elif momentum_5 > 0.015:  # 1.5%以上动量
                    trend_score += 5

            # 2. 🚀 增强多指标汇合评分 (0-30分, 提高5分)
            confluence_score = 0
            signals = []

            # MACD信号确认
            macd_1 = indicators.get('macd_1')
            macd_signal_1 = indicators.get('macd_signal_1')
            if macd_1 is not None and macd_signal_1 is not None:
                macd_diff = macd_1 - macd_signal_1
                if macd_diff > 0:
                    signals.append('bullish_macd')
                    macd_histogram_1 = indicators.get('macd_histogram_1', 0)
                    if macd_histogram_1 > 0:  # 柱状图确认
                        signals.append('bullish_macd_strong')
                else:
                    signals.append('bearish_macd')
                    macd_histogram_1 = indicators.get('macd_histogram_1', 0)
                    if macd_histogram_1 < 0:
                        signals.append('bearish_macd_strong')

            # RSI信号确认 - 恢复更严格的阈值，提高质量
            rsi_14 = indicators.get('rsi_14', 50)
            if rsi_14 is not None:
                if rsi_14 < 30:  # 恢复30阈值，确保真正超卖
                    signals.append('oversold_rsi')
                elif rsi_14 > 70:  # 恢复70阈值，确保真正超买
                    signals.append('overbought_rsi')

            # 均线排列确认 - 提高阈值，确保信号质量
            ma_score = indicators.get('ma_alignment_score', 0)
            if ma_score is not None:
                if ma_score > 0.8:  # 提高到0.8，确保强排列
                    signals.append('bullish_ma')
                elif ma_score < -0.8:  # 提高到-0.8，确保强排列
                    signals.append('bearish_ma')

            # 🚀 新增：成交量信号确认
            volume_ratio = indicators.get('volume_ratio', 1.0)
            if volume_ratio is not None and pd.notna(volume_ratio) and volume_ratio > 1.3:  # 降低阈值
                signals.append('volume_surge')

            # 🚀 新增：布林带信号
            bb_width = indicators.get('bb_width_20', 0)
            price_position = indicators.get('price_position', 0.5)
            if (bb_width is not None and pd.notna(bb_width) and
                    price_position is not None and pd.notna(price_position) and bb_width > 0.02):
                if price_position > 0.8:  # 突破上轨
                    signals.append('bullish_bb_breakout')
                elif price_position < 0.2:  # 突破下轨
                    signals.append('bearish_bb_breakout')

            # 🚀 新增：随机指标确认
            stoch_k = indicators.get('stoch_k', 50)
            if stoch_k is not None and pd.notna(stoch_k):
                if stoch_k < 25:
                    signals.append('oversold_stoch')
                elif stoch_k > 75:
                    signals.append('overbought_stoch')

            # 计算信号汇合度
            bullish_signals = len([s for s in signals if 'bullish' in s or 'oversold' in s or 'surge' in s])
            bearish_signals = len([s for s in signals if 'bearish' in s or 'overbought' in s])
            max_signals = max(bullish_signals, bearish_signals)

            # 降低要求，提高敏感度
            if max_signals >= 4:  # 4个以上信号
                confluence_score = 30
            elif max_signals >= 3:
                confluence_score = 25
            elif max_signals >= 2:
                confluence_score = 18
            elif max_signals >= 1:
                confluence_score = 10

            # 3. 🚀 优化成交量确认评分 (0-25分, 提高5分)
            volume_score = 0
            volume_ratio = indicators.get('volume_ratio', 1.0)
            if volume_ratio is not None and pd.notna(volume_ratio):
                if volume_ratio > 1.8:  # 降低阈值
                    volume_score = 25
                elif volume_ratio > 1.3:  # 降低阈值
                    volume_score = 20
                elif volume_ratio > 1.1:  # 降低阈值
                    volume_score = 15
                elif volume_ratio > 0.9:  # 新增低阈值档位
                    volume_score = 8

            # 4. 🚀 增强关键位突破评分 (0-20分, 提高5分)
            support_resistance_score = 0
            price_position = indicators.get('price_position', 0.5)
            price_channel_position = indicators.get('price_channel_position', 0.5)

            # 价格位置评分
            if price_position is not None and pd.notna(price_position):
                if price_position > 0.85 or price_position < 0.15:  # 降低阈值
                    support_resistance_score += 12
                elif price_position > 0.75 or price_position < 0.25:  # 降低阈值
                    support_resistance_score += 8

            # 价格通道位置评分
            if price_channel_position is not None and pd.notna(price_channel_position):
                if price_channel_position > 0.8 or price_channel_position < 0.2:
                    support_resistance_score += 8
                elif price_channel_position > 0.7 or price_channel_position < 0.3:
                    support_resistance_score += 5

            # 5. 🚀 优化波动率环境评分 (0-15分, 提高5分)
            volatility_score = 0
            atr_14 = indicators.get('atr_14', 0)
            if atr_14 is not None and pd.notna(atr_14) and atr_14 > 0:
                # 计算ATR相对强度
                volatility_ratio = indicators.get('volatility_ratio', 0)
                if volatility_ratio is not None and pd.notna(volatility_ratio):
                    atr_ratio = volatility_ratio * 1000
                    if 12 <= atr_ratio <= 60:  # 扩大适中波动率范围
                        volatility_score = 15
                    elif 8 <= atr_ratio <= 100:  # 扩大可接受波动率范围
                        volatility_score = 10
                    elif 5 <= atr_ratio <= 150:  # 新增更宽泛的可接受范围
                        volatility_score = 6
                    else:  # 极端波动率
                        volatility_score = 2

            # 🚀 新增：波动率突破奖励
            bb_width = indicators.get('bb_width_20', 0)
            if bb_width is not None and pd.notna(bb_width):
                if bb_width > 0.025:  # 高波动环境
                    volatility_score += 3
                elif bb_width < 0.01:  # 低波动环境（适合突破）
                    volatility_score += 2

            # 计算总分和信号质量级别
            total_score = trend_score + confluence_score + volume_score + support_resistance_score + volatility_score

            # 🔧 基于历史表现的动态阈值调整（更保守版）
            base_alpha_threshold = 75  # 恢复75
            base_beta_threshold = 55   # 恢复55
            base_gamma_threshold = 35  # 恢复35

            # 💡 根据当前胜率和连胜情况调整阈值
            win_rate_adjustment = (self.current_win_rate - 0.6) * 50  # 胜率调整
            sensitivity_adjustment = (self.opportunity_sensitivity - 0.8) * 30  # 敏感度调整

            # 🚀 自适应阈值计算（更激进的机会捕获）
            if self.adaptive_thresholds:
                alpha_threshold = max(55, base_alpha_threshold + win_rate_adjustment)  # 从65降到55
                beta_threshold = max(35, base_beta_threshold + win_rate_adjustment + sensitivity_adjustment)  # 从45降到35
                gamma_threshold = max(20, base_gamma_threshold + win_rate_adjustment + sensitivity_adjustment)  # 从30降到20
            else:
                alpha_threshold = base_alpha_threshold
                beta_threshold = base_beta_threshold
                gamma_threshold = base_gamma_threshold

            # 🎯 智能信号分级 - 更加激进的机会捕获（优化版）
            if total_score >= alpha_threshold:
                signal_quality = "ALPHA"
                confidence_level = min(0.95, 0.78 + (total_score - alpha_threshold) * 0.01)  # 从0.85降到0.78
                estimated_win_rate = min(0.85, 0.72 + (total_score - alpha_threshold) * 0.005)  # 从0.80降到0.72
            elif total_score >= beta_threshold:
                signal_quality = "BETA"
                confidence_level = min(0.85, 0.62 + (total_score - beta_threshold) * 0.012)  # 从0.70降到0.62
                estimated_win_rate = min(0.78, 0.62 + (total_score - beta_threshold) * 0.008)  # 从0.70降到0.62
            elif total_score >= gamma_threshold:
                signal_quality = "GAMMA"
                confidence_level = min(0.75, 0.52 + (total_score - gamma_threshold) * 0.015)  # 从0.60降到0.52
                estimated_win_rate = min(0.70, 0.52 + (total_score - gamma_threshold) * 0.01)   # 从0.60降到0.52
            else:
                signal_quality = "WEAK"
                confidence_level = max(0.35, 0.3 + total_score * 0.01)
                estimated_win_rate = max(0.40, 0.35 + total_score * 0.008)

            # 🎯 智能风险回报比优化 - 保证资金安全的同时最大化收益
            atr_value = indicators.get('atr_14', current_price * 0.02)
            if atr_value is None:
                atr_value = current_price * 0.02

            # 💡 基于信号质量和市场环境动态调整止损
            base_stop_multiplier = 0.8  # 基础止损倍数

            # 根据波动率调整止损
            volatility_ratio = indicators.get('volatility_ratio', 0.02)
            if volatility_ratio is not None and pd.notna(volatility_ratio):
                volatility_ratio_scaled = volatility_ratio * 1000
                if volatility_ratio_scaled > 25:  # 高波动环境
                    stop_multiplier = base_stop_multiplier * 1.3
                elif volatility_ratio_scaled < 10:  # 低波动环境
                    stop_multiplier = base_stop_multiplier * 0.7
                else:
                    stop_multiplier = base_stop_multiplier
            else:
                stop_multiplier = base_stop_multiplier

            stop_distance = atr_value * stop_multiplier

            # 🚀 动态目标计算 - 根据信号质量优化收益目标
            if signal_quality == "ALPHA":
                base_target_ratio = 5.5  # 提升目标
                # 连胜奖励
                if self.consecutive_wins >= 3:
                    base_target_ratio *= 1.2
            elif signal_quality == "BETA":
                base_target_ratio = 3.8
                if self.consecutive_wins >= 2:
                    base_target_ratio *= 1.15
            elif signal_quality == "GAMMA":
                base_target_ratio = 2.5
                if self.consecutive_wins >= 1:
                    base_target_ratio *= 1.1
            else:
                base_target_ratio = 1.8

            target_distance = stop_distance * base_target_ratio
            risk_reward_ratio = target_distance / stop_distance if stop_distance > 0 else base_target_ratio

            # 返回信号质量数据
            return {
                'signal_quality': signal_quality,
                'signal_strength_score': total_score,
                'confidence_level': confidence_level,
                'estimated_win_rate': estimated_win_rate,
                'risk_reward_ratio': risk_reward_ratio,
                'trend_score': trend_score,
                'confluence_score': confluence_score,
                'volume_score': volume_score,
                'support_resistance_score': support_resistance_score,
                'volatility_score': volatility_score,
                'bullish_signals_count': bullish_signals,
                'bearish_signals_count': bearish_signals,
                'signal_direction': 'bullish' if bullish_signals > bearish_signals else 'bearish' if bearish_signals > bullish_signals else 'neutral',
                'stop_loss_distance': stop_distance,
                'target_distance': target_distance
            }

        except Exception as e:
            logger.error(f"信号质量评估异常: {str(e)}")
            return {
                'signal_quality': 'WEAK',
                'signal_strength_score': 0,
                'confidence_level': 0.3,
                'estimated_win_rate': 0.4,
                'risk_reward_ratio': 1.5
            }

    def _analyze_precision_entry_points(self, data: pd.DataFrame, indicators: Dict) -> Dict:
        """🎯 精准入场点分析 - 找到最佳进入时机"""
        try:
            current_price = float(data.iloc[-1]['close'])
            latest = data.iloc[-1]

            entry_signals = []
            entry_quality = 0

            # 1. 🔥 动量突破信号
            macd_1 = indicators.get('macd_1')
            macd_histogram_1 = indicators.get('macd_histogram_1')
            if macd_1 is not None and macd_histogram_1 is not None:
                if macd_histogram_1 > 0 and macd_1 > 0:
                    entry_signals.append('macd_bullish_momentum')
                    entry_quality += 15
                elif macd_histogram_1 < 0 and macd_1 < 0:
                    entry_signals.append('macd_bearish_momentum')
                    entry_quality += 15

            # 2. 🎨 RSI动量信号
            rsi_14 = indicators.get('rsi_14', 50)
            rsi_21 = indicators.get('rsi_21', 50)
            if rsi_14 is not None and rsi_21 is not None:
                if 25 < rsi_14 < 35 and rsi_14 > rsi_21:  # RSI从超卖回升
                    entry_signals.append('rsi_oversold_recovery')
                    entry_quality += 12
                elif 65 < rsi_14 < 75 and rsi_14 < rsi_21:  # RSI从超买回落
                    entry_signals.append('rsi_overbought_decline')
                    entry_quality += 12

            # 3. 📊 均线系统信号
            ma_alignment = indicators.get('ma_alignment_score', 0)
            if ma_alignment is not None and abs(ma_alignment) > 0.6:  # 强势均线排列
                entry_signals.append('strong_ma_alignment')
                entry_quality += 10

            # 4. 🚀 成交量确认信号
            volume_ratio = indicators.get('volume_ratio', 1.0)
            if volume_ratio is not None and pd.notna(volume_ratio) and volume_ratio > 1.4:  # 成交量放大
                entry_signals.append('volume_surge')
                entry_quality += 8

            # 5. 🎯 布林带位置信号
            price_position = indicators.get('price_position', 0.5)
            bb_width = indicators.get('bb_width_20', 0)
            if (price_position is not None and pd.notna(price_position) and
                    bb_width is not None and pd.notna(bb_width) and bb_width > 0.015):  # 有足够波动率
                if price_position > 0.8:  # 靠近上轨
                    entry_signals.append('bb_upper_approach')
                    entry_quality += 6
                elif price_position < 0.2:  # 靠近下轨
                    entry_signals.append('bb_lower_approach')
                    entry_quality += 6

            # 6. 📨 鈲合信号质量评估
            confluence_count = len(entry_signals)
            if confluence_count >= 4:
                entry_timing = "OPTIMAL"  # 最佳进入时机
                entry_quality += 20
            elif confluence_count >= 3:
                entry_timing = "GOOD"     # 较好进入时机
                entry_quality += 12
            elif confluence_count >= 2:
                entry_timing = "FAIR"     # 一般进入时机
                entry_quality += 6
            else:
                entry_timing = "POOR"     # 较差进入时机

            # 7. 🏁 进入点精度计算
            atr_14 = indicators.get('atr_14', current_price * 0.02)
            if atr_14 is None:
                atr_14 = current_price * 0.02

            if entry_timing in ["OPTIMAL", "GOOD"]:
                entry_precision_range = atr_14 * 0.3  # 高精度范围
            else:
                entry_precision_range = atr_14 * 0.5  # 标准精度范围

            return {
                'entry_timing': entry_timing,
                'entry_quality_score': entry_quality,
                'entry_signals': entry_signals,
                'entry_confluence_count': confluence_count,
                'entry_precision_range': entry_precision_range,
                'optimal_entry_price': current_price,
                'entry_range_lower': current_price - entry_precision_range,
                'entry_range_upper': current_price + entry_precision_range
            }

        except Exception as e:
            logger.error(f"精准入场点分析异常: {str(e)}")
            return {
                'entry_timing': 'POOR',
                'entry_quality_score': 0,
                'entry_signals': [],
                'entry_confluence_count': 0
            }

    def _detect_market_anomalies(self, data: pd.DataFrame, indicators: Dict) -> Dict:
        """🔍 实时市场异动检测 - 捕捉突发机会"""
        try:
            current_price = float(data.iloc[-1]['close'])
            latest = data.iloc[-1]

            anomaly_signals = []
            anomaly_level = "NORMAL"
            anomaly_score = 0

            # 1. 💥 成交量异动检测（提高敏感度）
            volume_ratio = indicators.get('volume_ratio', 1.0)
            if volume_ratio is not None and pd.notna(volume_ratio):
                if volume_ratio > 2.2:  # 从2.5降到2.2
                    anomaly_signals.append('extreme_volume_spike')
                    anomaly_score += 25
                elif volume_ratio > 1.6:  # 从1.8降到1.6
                    anomaly_signals.append('high_volume_spike')
                    anomaly_score += 15
                elif volume_ratio > 1.3:  # 新增中等异动检测
                    anomaly_signals.append('moderate_volume_spike')
                    anomaly_score += 8

            # 2. 🌊 价格波动异动（提高敏感度）
            volatility_ratio = indicators.get('volatility_ratio', 0.02)
            if volatility_ratio is not None and pd.notna(volatility_ratio):
                volatility_ratio_scaled = volatility_ratio * 1000
                if volatility_ratio_scaled > 35:  # 从40降到35
                    anomaly_signals.append('extreme_volatility')
                    anomaly_score += 20
                elif volatility_ratio_scaled > 20:  # 从25降到20
                    anomaly_signals.append('high_volatility')
                    anomaly_score += 12
                elif volatility_ratio_scaled > 15:  # 新增中等波动检测
                    anomaly_signals.append('moderate_volatility')
                    anomaly_score += 6

            # 🚀 新增：价格加速度异常
            price_acceleration = indicators.get('price_acceleration', 0)
            if (price_acceleration is not None and pd.notna(price_acceleration) and
                    abs(price_acceleration) > current_price * 0.001):  # 价格加速度异常
                anomaly_signals.append('price_acceleration_anomaly')
                anomaly_score += 15

            # 4. 🎆 布林带挤压与扩张
            bb_width = indicators.get('bb_width_20', 0)
            if len(data) >= 10:
                recent_bb_widths = [indicators.get(f'bb_width_20', 0) for _ in range(min(10, len(data)))]
                if bb_width > np.mean(recent_bb_widths) * 1.5:  # 布林带快速扩张
                    anomaly_signals.append('bb_rapid_expansion')
                    anomaly_score += 12
                elif bb_width < np.mean(recent_bb_widths) * 0.6:  # 布林带挤压
                    anomaly_signals.append('bb_squeeze')
                    anomaly_score += 8

            # 5. 🔄 动量背离信号
            rsi_14 = indicators.get('rsi_14', 50)
            price_momentum_5 = indicators.get('price_momentum_5', 0)
            if rsi_14 is not None and price_momentum_5 is not None:
                if rsi_14 > 70 and price_momentum_5 < 0:  # 顶背离
                    anomaly_signals.append('bearish_divergence')
                    anomaly_score += 18
                elif rsi_14 < 30 and price_momentum_5 > 0:  # 底背离
                    anomaly_signals.append('bullish_divergence')
                    anomaly_score += 18

            # 6. 🎯 关键位突破信号
            price_channel_position = indicators.get('price_channel_position', 0.5)
            if price_channel_position is not None and pd.notna(price_channel_position):
                if price_channel_position > 0.95:  # 突破高点
                    anomaly_signals.append('breakout_resistance')
                    anomaly_score += 15
                elif price_channel_position < 0.05:  # 突破低点
                    anomaly_signals.append('breakdown_support')
                    anomaly_score += 15

            # 📊 综合异动等级评估（更敏感的阈值）
            if anomaly_score >= 50:  # 从60降到50
                anomaly_level = "EXTREME"    # 极端异动 - 重大机会
            elif anomaly_score >= 28:  # 从35降到28
                anomaly_level = "HIGH"       # 高度异动 - 重要机会
            elif anomaly_score >= 15:  # 从20降到15
                anomaly_level = "MODERATE"   # 中度异动 - 关注机会
            elif anomaly_score >= 8:   # 从10降到8
                anomaly_level = "LOW"        # 轻度异动 - 潜在机会
            else:
                anomaly_level = "NORMAL"     # 正常状态

            # 🎆 机会捕捉建议（更激进）
            opportunity_alert = False
            if anomaly_level in ["EXTREME", "HIGH"] and len(anomaly_signals) >= 2:  # 从>=3降到>=2
                opportunity_alert = True
            elif anomaly_level == "MODERATE" and len(anomaly_signals) >= 3:  # 新增MODERATE级别触发
                opportunity_alert = True

            return {
                'anomaly_level': anomaly_level,
                'anomaly_score': anomaly_score,
                'anomaly_signals': anomaly_signals,
                'anomaly_count': len(anomaly_signals),
                'opportunity_alert': opportunity_alert,
                'volatility_spike_detected': volume_ratio is not None and pd.notna(volume_ratio) and volume_ratio > 1.8,
                'volume_spike_detected': volatility_ratio is not None and pd.notna(volatility_ratio) and volatility_ratio * 1000 > 25,
                'divergence_detected': any('divergence' in signal for signal in anomaly_signals),
                'breakout_detected': any('breakout' in signal or 'breakdown' in signal for signal in anomaly_signals),
                'market_stress_indicator': min(anomaly_score / 100, 1.0)
            }

        except Exception as e:
            logger.error(f"市场异动检测异常: {str(e)}")
            return {
                'anomaly_level': 'NORMAL',
                'anomaly_score': 0,
                'anomaly_signals': [],
                'opportunity_alert': False
            }

    def get_multi_timeframe_analysis(self) -> Dict:
        """
        多时间框架分析 - 只提供指标数据
        """
        try:
            # 主要时间框架
            primary_timeframes = {
                '1H': 5,
                '4H': 7,
                '1D': 8
            }

            # 辅助时间框架
            auxiliary_timeframes = {
                '30m': 4
            }

            analysis_results = {}

            # 分析所有时间框架
            all_timeframes = {**primary_timeframes, **auxiliary_timeframes}
            for tf_name, tf_code in all_timeframes.items():
                result = self.get_optimized_kline_data(tf_code)
                if 'error' not in result:
                    analysis_results[tf_name] = result

            if not analysis_results:
                return {"error": "No valid timeframe data available"}

            # 🚀 新增：跨时间框架机会质量评估
            opportunity_assessment = self._assess_cross_timeframe_opportunities(analysis_results)

            return {
                "timeframe_data": analysis_results,
                "opportunity_assessment": opportunity_assessment,
                "analysis_timestamp": pd.Timestamp.now().isoformat()
            }

        except Exception as e:
            logger.error(f"多时间框架分析异常: {str(e)}")
            return {"error": f"Multi-timeframe analysis error: {str(e)}"}

    def _assess_cross_timeframe_opportunities(self, timeframe_data: Dict) -> Dict:
        """🚀 跨时间框架机会质量评估"""
        try:
            opportunities = []

            # 获取各时间框架的信号质量
            tf_signals = {}
            for tf_name, tf_data in timeframe_data.items():
                indicators = tf_data.get('indicators', {})
                signal_quality = indicators.get('signal_quality', 'WEAK')
                signal_direction = indicators.get('signal_direction', 'neutral')
                confidence = indicators.get('confidence_level', 0.3)

                tf_signals[tf_name] = {
                    'quality': signal_quality,
                    'direction': signal_direction,
                    'confidence': confidence,
                    'score': indicators.get('signal_strength_score', 0)
                }

            # 寻找跨时间框架确认的机会
            primary_timeframes = ['1D', '4H', '1H']
            for direction in ['bullish', 'bearish']:
                confirmed_tfs = []
                total_confidence = 0
                total_score = 0

                for tf in primary_timeframes:
                    if tf in tf_signals and tf_signals[tf]['direction'] == direction:
                        confirmed_tfs.append(tf)
                        total_confidence += tf_signals[tf]['confidence']
                        total_score += tf_signals[tf]['score']

                # 如果至少有2个主要时间框架确认
                if len(confirmed_tfs) >= 2:
                    avg_confidence = total_confidence / len(confirmed_tfs)
                    avg_score = total_score / len(confirmed_tfs)

                    # 确定机会质量
                    if len(confirmed_tfs) == 3 and avg_score >= 70:
                        opportunity_quality = "EXCEPTIONAL"
                    elif len(confirmed_tfs) == 3 and avg_score >= 60:
                        opportunity_quality = "HIGH"
                    elif len(confirmed_tfs) >= 2 and avg_score >= 60:
                        opportunity_quality = "MEDIUM"
                    else:
                        opportunity_quality = "LOW"

                    opportunities.append({
                        'direction': direction,
                        'quality': opportunity_quality,
                        'confirmed_timeframes': confirmed_tfs,
                        'confidence': avg_confidence,
                        'score': avg_score,
                        'timeframe_count': len(confirmed_tfs)
                    })

            # 选择最佳机会
            best_opportunity = None
            if opportunities:
                # 按质量和确认时间框架数量排序
                quality_rank = {"EXCEPTIONAL": 4, "HIGH": 3, "MEDIUM": 2, "LOW": 1}
                opportunities.sort(key=lambda x: (quality_rank.get(x['quality'], 0), x['timeframe_count'], x['score']), reverse=True)
                best_opportunity = opportunities[0]

            return {
                'best_opportunity': best_opportunity,
                'all_opportunities': opportunities,
                'market_consensus': self._calculate_market_consensus(tf_signals),
                'timeframe_alignment_score': self._calculate_alignment_score(tf_signals)
            }

        except Exception as e:
            logger.error(f"机会评估异常: {str(e)}")
            return {'error': f"Opportunity assessment error: {str(e)}"}

    def _calculate_market_consensus(self, tf_signals: Dict) -> Dict:
        """计算市场共识度"""
        directions = [sig['direction'] for sig in tf_signals.values() if sig['direction'] != 'neutral']
        if not directions:
            return {'consensus': 'neutral', 'strength': 0.0}

        bullish_count = directions.count('bullish')
        bearish_count = directions.count('bearish')
        total_count = len(directions)

        if bullish_count > bearish_count:
            consensus = 'bullish'
            strength = bullish_count / total_count
        elif bearish_count > bullish_count:
            consensus = 'bearish'
            strength = bearish_count / total_count
        else:
            consensus = 'neutral'
            strength = 0.5

        return {'consensus': consensus, 'strength': strength}

    def _calculate_alignment_score(self, tf_signals: Dict) -> float:
        """计算时间框架对齐评分"""
        if not tf_signals:
            return 0.0

        # 主要时间框架权重
        weights = {'1D': 0.4, '4H': 0.3, '1H': 0.2, '30m': 0.1}

        total_score = 0
        total_weight = 0

        for tf_name, signal in tf_signals.items():
            if tf_name in weights and signal['direction'] != 'neutral':
                # 方向一致性奖励
                direction_bonus = 1.0 if signal['direction'] in ['bullish', 'bearish'] else 0.5
                score = signal['confidence'] * direction_bonus * weights[tf_name]
                total_score += score
                total_weight += weights[tf_name]

        return total_score / total_weight if total_weight > 0 else 0.0

    def get_trading_opportunities(self) -> Dict:
        """🚀 智能交易机会识别 - 新增高级功能"""
        try:
            # 获取多时间框架分析
            multi_tf_analysis = self.get_multi_timeframe_analysis()
            if 'error' in multi_tf_analysis:
                return multi_tf_analysis

            opportunities = []
            timeframe_data = multi_tf_analysis.get('timeframe_data', {})
            opportunity_assessment = multi_tf_analysis.get('opportunity_assessment', {})

            # 获取最佳机会
            best_opportunity = opportunity_assessment.get('best_opportunity')
            if best_opportunity:
                # 获取对应时间框架的详细数据
                primary_tf = best_opportunity['confirmed_timeframes'][0] if best_opportunity['confirmed_timeframes'] else '1H'
                tf_data = timeframe_data.get(primary_tf, {})

                if tf_data:
                    indicators = tf_data.get('indicators', {})
                    current_price = tf_data.get('current_price', 0)

                    # 计算入场和止损位
                    stop_distance = indicators.get('stop_loss_distance', current_price * 0.02)
                    target_distance = indicators.get('target_distance', stop_distance * 3)

                    if best_opportunity['direction'] == 'bullish':
                        entry_price = current_price
                        stop_loss = current_price - stop_distance
                        target_price = current_price + target_distance
                    else:
                        entry_price = current_price
                        stop_loss = current_price + stop_distance
                        target_price = current_price - target_distance

                    opportunity = {
                        'opportunity_id': f"{best_opportunity['direction']}_{primary_tf}_{int(pd.Timestamp.now().timestamp())}",
                        'direction': best_opportunity['direction'],
                        'signal_quality': indicators.get('signal_quality', 'GAMMA'),
                        'opportunity_quality': best_opportunity['quality'],
                        'confidence_level': best_opportunity['confidence'],
                        'timeframe_focus': primary_tf,
                        'confirmed_timeframes': best_opportunity['confirmed_timeframes'],
                        'entry_zone': {
                            'optimal': entry_price,
                            'range': [entry_price * 0.999, entry_price * 1.001]
                        },
                        'stop_loss': stop_loss,
                        'target_price': target_price,
                        'risk_reward_ratio': indicators.get('risk_reward_ratio', 2.0),
                        'estimated_win_rate': indicators.get('estimated_win_rate', 0.6),
                        'current_price': current_price,
                        'atr_value': indicators.get('atr_14', current_price * 0.02),
                        'volume_confirmation': indicators.get('volume_ratio', 1.0) > 1.2,
                        'market_structure': tf_data.get('market_state', {}),
                        'signal_strength_score': indicators.get('signal_strength_score', 0),
                        'timeframe_alignment_score': opportunity_assessment.get('timeframe_alignment_score', 0.5)
                    }

                    opportunities.append(opportunity)

            # 智能机会筛选和排序
            qualified_opportunities = self._filter_and_rank_opportunities(opportunities)

            return {
                'qualified_opportunities': qualified_opportunities,
                'market_assessment': {
                    'consensus': opportunity_assessment.get('market_consensus', {}),
                    'alignment_score': opportunity_assessment.get('timeframe_alignment_score', 0.0),
                    'total_opportunities_found': len(opportunities),
                    'qualified_opportunities_count': len(qualified_opportunities)
                },
                'analysis_timestamp': pd.Timestamp.now().isoformat()
            }

        except Exception as e:
            logger.error(f"交易机会识别异常: {str(e)}")
            return {"error": f"Trading opportunities identification error: {str(e)}"}

    def _filter_and_rank_opportunities(self, opportunities: list) -> list:
        """🎯 智能筛选和排序交易机会 - 不错过任何机会"""
        if not opportunities:
            return []

        qualified = []

        for opp in opportunities:
            # 🚀 更加宽松的基本质量过滤 - 最大化机会捕获
            basic_quality_check = (
                    opp['signal_quality'] in ['ALPHA', 'BETA', 'GAMMA'] and
                    opp['risk_reward_ratio'] >= 1.2  # 从1.3降至1.2
            )

            # 🎯 特殊机会检查 - 高质量信号放宽标准
            special_opportunity = (
                    opp['signal_quality'] == 'ALPHA' or  # ALPHA信号无条件通过
                    opp['opportunity_quality'] == 'EXCEPTIONAL' or  # 异常机会无条件通过
                    (opp['confidence_level'] > 0.8 and len(opp['confirmed_timeframes']) >= 3)  # 高置信度+多时间框架确认
            )

            # 💪 异动机会检查 - 捕获市场异动
            anomaly_opportunity = False
            if 'anomaly_level' in opp:
                anomaly_opportunity = (
                        opp.get('anomaly_level') in ['EXTREME', 'HIGH'] or
                        opp.get('opportunity_alert', False)
                )

            if basic_quality_check or special_opportunity or anomaly_opportunity:
                # 📊 计算综合评分
                quality_score = {
                    'EXCEPTIONAL': 110, 'HIGH': 95, 'MEDIUM': 80, 'LOW': 65  # 提高所有等级分数
                }.get(opp['opportunity_quality'], 50)

                signal_score = {
                    'ALPHA': 100, 'BETA': 85, 'GAMMA': 70  # 提高所有信号分数
                }.get(opp['signal_quality'], 40)

                timeframe_score = len(opp['confirmed_timeframes']) * 20  # 提高从18到20
                confidence_score = opp['confidence_level'] * 100
                rr_score = min(opp['risk_reward_ratio'] * 30, 150)  # 提高从25到30

                # 🚀 复利加速器分数 - 鼓励高质量信号
                compound_bonus = 0
                if opp['signal_quality'] == 'ALPHA':
                    compound_bonus = 25  # 提高从15到25
                elif opp['signal_quality'] == 'BETA':
                    compound_bonus = 15  # 提高从8到15
                elif opp['signal_quality'] == 'GAMMA':
                    compound_bonus = 8   # 新增GAMMA加分

                # 🎆 异动机会加分
                anomaly_bonus = 0
                if anomaly_opportunity:
                    anomaly_bonus = 20

                # 🏆 连胜奖励加分
                winning_streak_bonus = 0
                if self.consecutive_wins >= 3:
                    winning_streak_bonus = 15
                elif self.consecutive_wins >= 2:
                    winning_streak_bonus = 8

                total_score = (
                        quality_score * 0.25 + signal_score * 0.25 +
                        timeframe_score * 0.2 + confidence_score * 0.15 +
                        rr_score * 0.1 + compound_bonus * 0.03 +
                        anomaly_bonus * 0.015 + winning_streak_bonus * 0.005
                )

                opp['total_score'] = total_score
                opp['compound_bonus'] = compound_bonus
                opp['anomaly_bonus'] = anomaly_bonus
                opp['winning_streak_bonus'] = winning_streak_bonus

                # 🚀 进一步降低准入门槛 - 从40降到30，最大化机会捕获，绝不错过盈利机会
                if total_score >= 30:
                    qualified.append(opp)

        # 按评分排序
        qualified.sort(key=lambda x: x['total_score'], reverse=True)

        # 🎯 最多返回10个机会，保证多样化
        return qualified[:10]

    def get_indicators_summary(self, kline_type=5) -> Dict:
        """
        获取指标摘要 - 便于agent快速理解市场状态
        不包含任何交易建议，只提供数据摘要
        """
        try:
            result = self.get_optimized_kline_data(kline_type)
            if 'error' in result:
                return result

            indicators = result.get('indicators', {})
            current_price = result.get('current_price', 0)

            # 计算指标摘要
            summary = {
                "current_price": current_price,
                "price_change_5": indicators.get('price_momentum_5', 0),
                "price_change_20": indicators.get('price_momentum_20', 0),
                "trend_strength": indicators.get('trend_strength', 0),
                "ma_alignment": indicators.get('ma_alignment_score', 0),
                "rsi_14": indicators.get('rsi_14', 50),
                "rsi_21": indicators.get('rsi_21', 50),
                "macd_1": indicators.get('macd_1', 0),
                "macd_histogram_1": indicators.get('macd_histogram_1', 0),
                "volume_ratio": indicators.get('volume_ratio', 1.0),
                "atr_14": indicators.get('atr_14', 0),
                "bb_width_20": indicators.get('bb_width_20', 0),
                "volatility_ratio": indicators.get('volatility_ratio', 0),
                "price_position": indicators.get('price_position', 0.5),
                "price_channel_position": indicators.get('price_channel_position', 0.5)
            }

            # 添加关键价格水平
            key_levels = result.get('key_levels', {})
            if key_levels:
                summary.update({
                    "nearest_support": key_levels.get('support'),
                    "nearest_resistance": key_levels.get('resistance'),
                    "support_distance": (current_price - key_levels.get('support', current_price)) / current_price if key_levels.get('support') else None,
                    "resistance_distance": (key_levels.get('resistance', current_price) - current_price) / current_price if key_levels.get('resistance') else None
                })

            # 添加市场状态
            market_state = result.get('market_state', {})
            if market_state:
                summary.update({
                    "volatility_state": market_state.get('volatility_state'),
                    "trend_state": market_state.get('trend_state')
                })

            return {
                "symbol": result.get('symbol'),
                "timeframe": result.get('timeframe'),
                "summary": summary,
                "full_indicators": indicators,
                "timestamp": result.get('last_update')
            }

        except Exception as e:
            logger.error(f"指标摘要生成异常: {str(e)}")
            return {"error": f"Summary generation error: {str(e)}"}

    def update_trading_performance(self, trade_result: Dict) -> None:
        """🚀 更新交易表现，支持智能复利策略调整"""
        try:
            self.trading_history.append({
                'timestamp': pd.Timestamp.now(),
                'result': trade_result.get('result', 'unknown'),
                'pnl': trade_result.get('pnl', 0),
                'signal_quality': trade_result.get('signal_quality', 'UNKNOWN'),
                'risk_reward_achieved': trade_result.get('risk_reward_achieved', 0),
                'entry_timing': trade_result.get('entry_timing', 'UNKNOWN')
            })

            # 🏆 智能连胜管理
            if trade_result.get('result') == 'win':
                self.consecutive_wins += 1
                self.consecutive_losses = 0

                # 🚀 动态复利倍数调整
                if self.consecutive_wins >= 5:  # 5连胜
                    self.compound_multiplier = min(self.compound_multiplier * 1.3, self.max_compound_factor)
                elif self.consecutive_wins >= 3:  # 3连胜
                    self.compound_multiplier = min(self.compound_multiplier * 1.2, self.max_compound_factor)
                elif self.consecutive_wins >= 2:  # 2连胜
                    self.compound_multiplier = min(self.compound_multiplier * 1.1, self.max_compound_factor)

            elif trade_result.get('result') == 'loss':
                self.consecutive_losses += 1
                self.consecutive_wins = 0

                # 🛡️ 风险控制机制
                if self.consecutive_losses >= 3:  # 3连败
                    self.compound_multiplier = max(self.compound_multiplier * 0.7, 0.3)
                elif self.consecutive_losses >= 2:  # 2连败
                    self.compound_multiplier = max(self.compound_multiplier * 0.8, 0.5)

            # 📊 实时胜率更新
            recent_trades = self.trading_history[-30:]  # 最近30笔交易
            if len(recent_trades) >= 5:
                wins = len([t for t in recent_trades if t['result'] == 'win'])
                self.current_win_rate = wins / len(recent_trades)

                # 🎯 基于胜率的敏感度调整
                if self.current_win_rate > 0.75:
                    self.opportunity_sensitivity = min(0.9, self.opportunity_sensitivity + 0.05)  # 提高敏感度
                elif self.current_win_rate < 0.5:
                    self.opportunity_sensitivity = max(0.6, self.opportunity_sensitivity - 0.05)  # 降低敏感度

        except Exception as e:
            logger.error(f"交易表现更新异常: {str(e)}")

    def get_compound_strategy_adjustment(self) -> Dict:
        """🚀 获取智能复利策略调整建议"""
        # 🏆 计算表现指标
        recent_performance = self._calculate_recent_performance()

        # 🎯 策略模式判定
        if self.consecutive_wins >= 5:
            strategy_mode = 'super_aggressive'  # 超级激进模式
        elif self.consecutive_wins >= 3:
            strategy_mode = 'aggressive'        # 激进模式
        elif self.consecutive_losses >= 3:
            strategy_mode = 'defensive'         # 防御模式
        elif self.consecutive_losses >= 2:
            strategy_mode = 'conservative'      # 保守模式
        else:
            strategy_mode = 'normal'            # 正常模式

        # 📈 风险调整计算
        base_risk_adjustment = max(0, (self.current_win_rate - 0.6) * 100)

        # 💪 连胜奖励系数
        win_streak_multiplier = 1.0
        if self.consecutive_wins >= 5:
            win_streak_multiplier = 1.5
        elif self.consecutive_wins >= 3:
            win_streak_multiplier = 1.3
        elif self.consecutive_wins >= 2:
            win_streak_multiplier = 1.1

        return {
            'compound_multiplier': self.compound_multiplier,
            'consecutive_wins': self.consecutive_wins,
            'consecutive_losses': self.consecutive_losses,
            'current_win_rate': self.current_win_rate,
            'strategy_mode': strategy_mode,
            'risk_adjustment': base_risk_adjustment,
            'win_streak_multiplier': win_streak_multiplier,
            'opportunity_sensitivity': self.opportunity_sensitivity,
            'max_position_size': min(self.compound_multiplier * win_streak_multiplier, self.max_compound_factor),
            'recent_performance': recent_performance,
            'is_hot_streak': self.consecutive_wins >= 3,
            'needs_cooling_down': self.consecutive_losses >= 2
        }

    def get_enhanced_opportunities_with_compound(self) -> Dict:
        """🚀 获取增强的交易机会（包含复利策略）"""
        try:
            opportunities = self.get_trading_opportunities()
            if 'error' in opportunities:
                return opportunities

            # 应用复利策略调整
            compound_adjustment = self.get_compound_strategy_adjustment()

            # 调整机会评分
            for opp in opportunities.get('qualified_opportunities', []):
                opp['compound_multiplier'] = compound_adjustment['compound_multiplier']
                opp['adjusted_score'] = opp.get('total_score', 0) * compound_adjustment['compound_multiplier']
                opp['recommended_position_size_multiplier'] = compound_adjustment['compound_multiplier']

            opportunities['compound_strategy'] = compound_adjustment
            return opportunities

        except Exception as e:
            logger.error(f"增强机会识别异常: {str(e)}")
            return {"error": f"Enhanced opportunities error: {str(e)}"}

# 创建全局实例
analyzer = AgentOptimizedKlineAnalyzer()

# 兼容性函数
def get_k_line_data(kline_type=5) -> Dict:
    """获取K线数据 - 兼容性接口"""
    return analyzer.get_optimized_kline_data(kline_type)

def get_multi_timeframe_analysis() -> Dict:
    """多时间框架分析 - 兼容性接口"""
    return analyzer.get_multi_timeframe_analysis()

def get_indicators_summary(kline_type=5) -> Dict:
    """获取指标摘要 - 兼容性接口"""
    return analyzer.get_indicators_summary(kline_type)

# 🚀 新增智能接口
def get_trading_opportunities() -> Dict:
    """智能交易机会识别 - 新增高级功能"""
    return analyzer.get_trading_opportunities()

def get_signal_quality_assessment(kline_type=5) -> Dict:
    """信号质量评估 - 新增专业功能"""
    result = analyzer.get_optimized_kline_data(kline_type)
    if 'error' in result:
        return result

    indicators = result.get('indicators', {})
    return {
        'signal_quality': indicators.get('signal_quality', 'WEAK'),
        'signal_strength_score': indicators.get('signal_strength_score', 0),
        'confidence_level': indicators.get('confidence_level', 0.3),
        'estimated_win_rate': indicators.get('estimated_win_rate', 0.4),
        'risk_reward_ratio': indicators.get('risk_reward_ratio', 1.5),
        'signal_direction': indicators.get('signal_direction', 'neutral'),
        'timeframe': result.get('timeframe'),
        'current_price': result.get('current_price'),
        'analysis_timestamp': result.get('last_update')
    }

# 🚀 复利策略支持接口
def update_trading_performance(trade_result: Dict) -> None:
    """更新交易表现，支持复利策略"""
    analyzer.update_trading_performance(trade_result)

def get_compound_strategy_adjustment() -> Dict:
    """获取复利策略调整建议"""
    return analyzer.get_compound_strategy_adjustment()

def get_enhanced_opportunities_with_compound() -> Dict:
    """获取增强的交易机会（包含复利策略）"""
    return analyzer.get_enhanced_opportunities_with_compound()

if __name__ == '__main__':
    # 测试代码
    print("=== K线分析器测试 ===")

    # 测试1H数据
    result = get_k_line_data(5)  # 1H数据
    if 'error' not in result:
        print(f"\n1H 分析结果:")
        print(f"当前价格: {result.get('current_price')}")
        print(f"时间框架: {result.get('timeframe')}")
        print(f"指标数量: {len(result.get('indicators', {}))}")

        # 显示部分关键指标
        indicators = result.get('indicators', {})
        print(f"趋势强度: {indicators.get('trend_strength', 'N/A')}")
        print(f"RSI(14): {indicators.get('rsi_14', 'N/A')}")
        print(f"MACD: {indicators.get('macd_1', 'N/A')}")
        print(f"成交量比率: {indicators.get('volume_ratio', 'N/A')}")
        print(f"ATR(14): {indicators.get('atr_14', 'N/A')}")
    else:
        print(f"1H 分析错误: {result.get('error')}")

    # 测试指标摘要
    print("\n=== 指标摘要测试 ===")
    summary = get_indicators_summary(5)  # 1H摘要
    if 'error' not in summary:
        print(f"指标摘要:")
        summary_data = summary.get('summary', {})
        print(f"价格变化(5期): {summary_data.get('price_change_5', 'N/A'):.4f}")
        print(f"价格变化(20期): {summary_data.get('price_change_20', 'N/A'):.4f}")
        print(f"趋势强度: {summary_data.get('trend_strength', 'N/A'):.4f}")
        print(f"均线排列: {summary_data.get('ma_alignment', 'N/A'):.4f}")
        print(f"RSI(14): {summary_data.get('rsi_14', 'N/A'):.2f}")
        print(f"成交量比率: {summary_data.get('volume_ratio', 'N/A'):.2f}")
        print(f"波动率状态: {summary.get('summary', {}).get('volatility_state', 'N/A')}")
        print(f"趋势状态: {summary.get('summary', {}).get('trend_state', 'N/A')}")
    else:
        print(f"指标摘要错误: {summary.get('error')}")

    # 测试多时间框架分析
    print("\n=== 多时间框架分析测试 ===")
    multi_tf_result = get_multi_timeframe_analysis()
    if 'error' not in multi_tf_result:
        timeframes = multi_tf_result.get('timeframe_data', {})
        print(f"可用时间框架: {list(timeframes.keys())}")

        for tf, data in timeframes.items():
            if 'error' not in data:
                print(f"\n{tf} 框架:")
                print(f"  当前价格: {data.get('current_price')}")
                print(f"  指标数量: {len(data.get('indicators', {}))}")
                print(f"  市场状态: {data.get('market_state', 'N/A')}")
    else:
        print(f"多时间框架分析错误: {multi_tf_result.get('error')}")

    print("\n=== 测试完成 ===")
    print("注意: 此工具只提供计算好的指标数据，不生成交易信号")
    print("Agent需要根据这些指标自行判断交易策略")
