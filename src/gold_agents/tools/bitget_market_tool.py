
from datetime import datetime
from pybitget import Client
from crewai.tools import BaseTool
from pydantic import BaseModel, Field, PrivateAttr
from typing import Type, Dict, Any
import yaml
import os

class BitgetMarketToolSchema(BaseModel):
    """Bitget市场工具的输入参数模式"""
    trading_pair: str = Field(..., description="交易对，可选值：BTC")

class BitgetMarketTool(BaseTool):
    """Bitget市场信息工具类"""
    name: str = "获取Bitget市场信息的工具"
    description: str = """
    这是一个用于获取Bitget市场信息的工具。
    主要功能：
    1. 获取市场深度数据
    2. 获取K线数据
    3. 获取最新成交价
    
    使用示例：
    tool.run(trading_pair="BTC")
    """
    args_schema: Type[BaseModel] = BitgetMarketToolSchema

    _config: Dict[str, Any] = PrivateAttr(default_factory=dict)
    _environment: str = PrivateAttr(default="test")
    _is_test: bool = PrivateAttr(default=True)
    _margin_coin: str = PrivateAttr(default="SUSDT")
    _product_type: str = PrivateAttr(default="SUMCBL")
    _client: Client = PrivateAttr(default=None)

    def __init__(self, **kwargs: Any) -> None:
        """初始化工具"""
        super().__init__(**kwargs)
        self._config = self._load_config()
        print(f"成功加载配置文件: {self._config}")
        self._environment = self._config.get('environment', 'test')
        self._is_test = self._environment == 'test'
        self._margin_coin = "SUSDT" if self._is_test else "USDT"
        self._product_type = "SUMCBL" if self._is_test else "UMCBL"
        self._client = self._init_client()

    def _load_config(self) -> Dict[str, Any]:
        """加载配置文件"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), '../config/trader_config.yaml')
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            raise Exception(f"加载配置文件失败: {str(e)}")

    def _init_client(self) -> Client:
        """初始化API客户端"""
        api_config = self._config['api']
        if not all([api_config['api_key'], api_config['api_secret'], api_config['passphrase']]):
            raise ValueError("API配置不完整，请检查配置文件")

        try:
            print(f"初始化API客户端: {api_config}")
            return Client(
                api_key=api_config['api_key'],
                api_secret_key=api_config['api_secret'],
                passphrase=api_config['passphrase']
            )
        except Exception as e:
            raise Exception(f"初始化API客户端失败: {str(e)}")

    def _run(self, **kwargs: Any) -> str:
        """执行工具的主要逻辑"""
        try:
            trading_pair = kwargs.get('trading_pair', 'BTC')
            symbol = f"S{trading_pair}SUSDT_SUMCBL" if self._is_test else f"{trading_pair}USDT_UMCBL"
            # 获取市场深度
            depth = self._get_market_depth(symbol)

            # 获取K线数据
            klines = self._get_klines(symbol)

            # 获取最新成交价
            ticker = self._get_ticker(symbol)

            result = {
                "status": "success",
                "depth": depth,
                "1m_klines": klines,
                "ticker": ticker,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }

            return str(result)
        except Exception as e:
            error_result = {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            return str(error_result)

    def _get_market_depth(self, symbol: str) -> Dict[str, Any]:
        """获取市场深度数据"""
        try:

            result = self._client.mix_get_depth(
                symbol=symbol,
                limit=100  # 深度档位：5，15，50，100，默认100
            )
            print("===============获取市场深度==================")
            print(f"交易对: {symbol}")
            print(result)
            
            if result and 'data' in result:
                return {
                    "bids": result['data'].get('bids', []),
                    "asks": result['data'].get('asks', [])
                }
            return {"bids": [], "asks": []}
        except Exception as e:
            raise Exception(f"获取市场深度失败: {str(e)}")

    def _get_klines(self, symbol: str) -> Dict[str, Any]:
        """获取K线数据"""
        try:
            # 获取当前时间和一小时前的时间
            end_time = int(datetime.now().timestamp() * 1000)
            start_time = end_time - (60 * 60 * 1000)  # 一小时前
            
            result = self._client.mix_get_candles(
                symbol=symbol,
                granularity='1m',  # 1分钟K线
                startTime=start_time,
                endTime=end_time,
                kLineType='market',  # 市场K线
                limit=50  # 最多返回100条数据
            )
            print("===============获取K线数据==================")
            print(f"交易对: {symbol}")
            print(f"时间范围: {datetime.fromtimestamp(start_time/1000)} - {datetime.fromtimestamp(end_time/1000)}")
            print(result)
            
            if result and 'data' in result:
                return {
                    "klines": result['data']
                }
            return {"klines": []}
        except Exception as e:
            raise Exception(f"获取K线数据失败: {str(e)}")

    def _get_ticker(self, symbol: str) -> Dict[str, Any]:
        """获取最新成交价"""
        try:
            result = self._client.mix_get_single_symbol_ticker(symbol=symbol)
            print("===============获取最新成交价==================")
            print(f"交易对: {symbol}")
            print(result)
            
            if result and 'data' in result:
                return {
                    "last_price": float(result['data'].get('last', 0)),
                    "high_24h": float(result['data'].get('high24h', 0)),
                    "low_24h": float(result['data'].get('low24h', 0)),
                    "volume_24h": float(result['data'].get('volume24h', 0)),
                    "price_change_24h": float(result['data'].get('priceChange24h', 0)),
                    "price_change_percent_24h": float(result['data'].get('priceChangePercent24h', 0)),
                    "open_24h": float(result['data'].get('open24h', 0)),
                    "close_24h": float(result['data'].get('close24h', 0))
                }
            return {
                "last_price": 0.0,
                "high_24h": 0.0,
                "low_24h": 0.0,
                "volume_24h": 0.0,
                "price_change_24h": 0.0,
                "price_change_percent_24h": 0.0,
                "open_24h": 0.0,
                "close_24h": 0.0
            }
        except Exception as e:
            raise Exception(f"获取最新成交价失败: {str(e)}")

if __name__ == '__main__':
    # 测试工具
    tool = BitgetMarketTool()
    
    # 测试获取市场信息
    market_info = tool.run(trading_pair="BTC")
    print("市场信息:", market_info)