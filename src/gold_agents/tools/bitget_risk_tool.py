from typing import Dict, Any, Type, Optional
from datetime import datetime
from pybitget import Client
from crewai.tools import BaseTool
from pydantic import BaseModel, Field, PrivateAttr
import yaml
import os
import logging
from .db_manager import DatabaseManager

logger = logging.getLogger(__name__)

class BitgetRiskToolSchema(BaseModel):
    """Bitget风险管理工具的输入参数模式"""
    trading_pair: str = Field(..., description="交易对，可选值：BTC")
    action: str = Field(..., description="操作类型，可选值：hedge（对冲）, reduce（减仓）, dynamic_stop（动态止损）, monitor（风险监控）, manage_margin（资金管理）")
    hedge_ratio: Optional[float] = Field(None, description="对冲比例，0-1之间")
    reduce_ratio: Optional[float] = Field(None, description="减仓比例，0-1之间")
    steps: Optional[int] = Field(None, description="减仓步数")
    trailing_stop: Optional[bool] = Field(None, description="是否使用追踪止损")
    stop_distance: Optional[float] = Field(None, description="止损距离，百分比")
    margin_action: Optional[str] = Field(None, description="资金管理操作，可选值：add_margin（增加保证金）, reduce_margin（减少保证金）, adjust_leverage（调整杠杆）")
    amount: Optional[float] = Field(None, description="资金管理数量")

class BitgetRiskTool(BaseTool):
    """Bitget风险管理工具类"""
    name: str = "BitgetRiskTool"
    description: str = """
    这是一个用于管理交易风险的工具。
    主要功能：
    1. 对冲锁仓：开立反向仓位对冲风险
    2. 分批减仓：按比例逐步减少持仓
    3. 动态止损：根据市场波动调整止损位置
    4. 风险预警：监控账户风险指标
    5. 资金管理：调整保证金和杠杆
    
    使用示例：
    1. 对冲锁仓：
       tool.run(
           trading_pair="BTC",
           action="hedge",
           hedge_ratio=0.5  # 对冲比例
       )
    
    2. 分批减仓：
       tool.run(
           trading_pair="BTC",
           action="reduce",
           reduce_ratio=0.3,  # 减仓比例
           steps=3  # 分3次减仓
       )
    
    3. 动态止损：
       tool.run(
           trading_pair="BTC",
           action="dynamic_stop",
           trailing_stop=True,  # 追踪止损
           stop_distance=0.02  # 止损距离
       )
    
    4. 风险监控：
       tool.run(
           trading_pair="BTC",
           action="monitor"
       )
    """
    args_schema: Type[BaseModel] = BitgetRiskToolSchema

    # 使用私有属性存储内部状态
    _config: Dict[str, Any] = PrivateAttr(default_factory=dict)
    _environment: str = PrivateAttr(default="test")
    _is_test: bool = PrivateAttr(default=True)
    _margin_coin: str = PrivateAttr(default="SUSDT")
    _client: Client = PrivateAttr(default=None)
    _db: DatabaseManager = PrivateAttr(default=None)
    _product_type: str = PrivateAttr(default="SUMCBL")

    def __init__(self, **kwargs: Any) -> None:
        """初始化工具"""
        super().__init__(**kwargs)
        self._load_config()
        self._init_environment()
        self._init_client()
        self._init_database()

    def _load_config(self) -> None:
        """加载配置文件"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), '../config/trader_config.yaml')
            with open(config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f)
        except Exception as e:
            raise Exception(f"加载配置文件失败: {str(e)}")

    def _init_environment(self) -> None:
        """初始化环境配置"""
        self._environment = self._config['environment']
        if self._environment not in ['test', 'prod']:
            raise ValueError("环境配置错误，必须是 'test' 或 'prod'")
        self._is_test = self._environment == 'test'
        self._margin_coin = "SUSDT" if self._is_test else "USDT"
        self._product_type = "SUMCBL" if self._is_test else "UMCBL"

    def _init_client(self) -> None:
        """初始化API客户端"""
        api_config = self._config['api']
        if not all([api_config['api_key'], api_config['api_secret'], api_config['passphrase']]):
            raise ValueError("API配置不完整，请检查配置文件")
        
        try:
            self._client = Client(
                api_key=api_config['api_key'],
                api_secret_key=api_config['api_secret'],
                passphrase=api_config['passphrase']
            )
        except Exception as e:
            raise Exception(f"初始化API客户端失败: {str(e)}")

    def _init_database(self) -> None:
        """初始化数据库管理器"""
        self._db = DatabaseManager()

    def _run(self, **kwargs: Any) -> str:
        """执行工具的主要逻辑"""
        try:
            trading_pair = kwargs.get('trading_pair', 'BTC')
            action = kwargs.get('action')
            symbol = f"S{trading_pair}SUSDT_SUMCBL" if self._is_test else f"{trading_pair}USDT_UMCBL"
            
            if action == 'hedge':
                result = self._hedge_position(symbol, kwargs.get('hedge_ratio', 0.5))
                return str(result)
            elif action == 'reduce':
                result = self._reduce_position(symbol, kwargs.get('reduce_ratio', 0.3), kwargs.get('steps', 3))
                return str(result)
            elif action == 'dynamic_stop':
                result = self._dynamic_stop_loss(symbol, kwargs.get('trailing_stop', True), kwargs.get('stop_distance', 0.02))
                return str(result)
            elif action == 'monitor':
                result = self._monitor_risk(symbol)
                return str(result)
            elif action == 'manage_margin':
                result = self._manage_margin(symbol, kwargs.get('margin_action'), kwargs.get('amount'))
                return str(result)
            else:
                raise ValueError(f"不支持的操作类型: {action}")

        except Exception as e:
            logger.error(f"执行资金管理失败: {str(e)}")
            error_result = {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            return str(error_result)

    def _hedge_position(self, symbol: str, hedge_ratio: float) -> Dict[str, Any]:
        """对冲锁仓"""
        try:
            # 获取当前持仓
            result = self._client.mix_get_all_positions(
                productType=self._product_type,
                marginCoin=self._margin_coin
            )
            
            print("===============对冲锁仓==================")
            print(f"交易对: {symbol}")
            print(f"对冲比例: {hedge_ratio}")
            print(f"持仓信息: {result}")
            
            if not result or 'data' not in result or not result['data']:
                raise ValueError("没有找到持仓信息")
                
            # 找到对应交易对的持仓
            position = None
            for pos in result['data']:
                if pos.get('symbol') == symbol:
                    position = pos
                    break
                    
            if not position:
                raise ValueError(f"没有找到{symbol}的持仓信息")
                
            # 计算对冲数量
            hedge_size = float(position.get('total', 0)) * hedge_ratio
            
            # 确定对冲方向
            hedge_side = 'open_short' if position.get('holdSide') == 'long' else 'open_long'
            
            # 执行对冲
            order_params = {
                "symbol": symbol,
                "marginCoin": self._margin_coin,
                "size": str(hedge_size),
                "side": hedge_side,
                "orderType": "market",
                "timeInForceValue": "normal",
                "reduceOnly": False
            }
            
            print(f"对冲订单参数: {order_params}")
            result = self._client.mix_place_order(**order_params)
            print(f"对冲结果: {result}")
            
            return {
                "status": "success",
                "message": "成功执行对冲",
                "hedge_size": hedge_size,
                "hedge_side": hedge_side,
                "order_id": result.get('data', {}).get('orderId'),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"对冲失败: {str(e)}")

    def _reduce_position(self, symbol: str, reduce_ratio: float, steps: int) -> Dict[str, Any]:
        """分批减仓"""
        try:
            # 获取当前持仓
            result = self._client.mix_get_all_positions(
                productType=self._product_type,
                marginCoin=self._margin_coin
            )
            
            print("===============分批减仓==================")
            print(f"交易对: {symbol}")
            print(f"减仓比例: {reduce_ratio}")
            print(f"减仓步数: {steps}")
            print(f"持仓信息: {result}")
            
            if not result or 'data' not in result or not result['data']:
                raise ValueError("没有找到持仓信息")
                
            # 找到对应交易对的持仓
            position = None
            for pos in result['data']:
                if pos.get('symbol') == symbol:
                    position = pos
                    break
                    
            if not position:
                raise ValueError(f"没有找到{symbol}的持仓信息")
                
            # 计算每次减仓数量
            total_size = float(position.get('total', 0))
            reduce_size = (total_size * reduce_ratio) / steps
            
            # 确定平仓方向
            close_side = 'close_long' if position.get('holdSide') == 'long' else 'close_short'
            
            results = []
            for i in range(steps):
                # 执行减仓
                order_params = {
                    "symbol": symbol,
                    "marginCoin": self._margin_coin,
                    "size": str(reduce_size),
                    "side": close_side,
                    "orderType": "market",
                    "timeInForceValue": "normal",
                    "reduceOnly": True
                }
                
                print(f"第{i+1}次减仓参数: {order_params}")
                result = self._client.mix_place_order(**order_params)
                print(f"减仓结果: {result}")
                
                results.append({
                    "step": i + 1,
                    "size": reduce_size,
                    "order_id": result.get('data', {}).get('orderId')
                })
            
            return {
                "status": "success",
                "message": f"成功分{steps}次减仓",
                "total_reduce_size": total_size * reduce_ratio,
                "results": results,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"分批减仓失败: {str(e)}")

    def _dynamic_stop_loss(self, symbol: str, trailing_stop: bool, stop_distance: float) -> Dict[str, Any]:
        """动态止损"""
        try:
            # 获取当前持仓
            result = self._client.mix_get_all_positions(
                productType=self._product_type,
                marginCoin=self._margin_coin
            )
            
            print("===============动态止损==================")
            print(f"交易对: {symbol}")
            print(f"追踪止损: {trailing_stop}")
            print(f"止损距离: {stop_distance}")
            print(f"持仓信息: {result}")
            
            if not result or 'data' not in result or not result['data']:
                raise ValueError("没有找到持仓信息")
                
            # 找到对应交易对的持仓
            position = None
            for pos in result['data']:
                if pos.get('symbol') == symbol:
                    position = pos
                    break
                    
            if not position:
                raise ValueError(f"没有找到{symbol}的持仓信息")
                
            # 获取当前价格
            ticker = self._client.mix_get_single_symbol_ticker(symbol=symbol)
            if not ticker or 'data' not in ticker:
                raise ValueError("获取价格失败")
                
            current_price = float(ticker['data'].get('last', 0))
            
            # 计算止损价格
            if position.get('holdSide') == 'long':
                stop_price = current_price * (1 - stop_distance)
            else:
                stop_price = current_price * (1 + stop_distance)
                
            # 设置止损
            order_params = {
                "symbol": symbol,
                "marginCoin": self._margin_coin,
                "size": position.get('total'),
                "side": 'close_long' if position.get('holdSide') == 'long' else 'close_short',
                "orderType": "market",
                "timeInForceValue": "normal",
                "reduceOnly": True,
                "presetStopLossPrice": str(stop_price)
            }
            
            print(f"止损订单参数: {order_params}")
            result = self._client.mix_place_order(**order_params)
            print(f"止损结果: {result}")
            
            return {
                "status": "success",
                "message": "成功设置动态止损",
                "stop_price": stop_price,
                "order_id": result.get('data', {}).get('orderId'),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"设置动态止损失败: {str(e)}")

    def _monitor_risk(self, symbol: str) -> Dict[str, Any]:
        """风险监控"""
        try:
            # 获取账户信息
            account_result = self._client.mix_get_accounts(
                productType=self._product_type
            )
            
            print("===============风险监控==================")
            print(f"交易对: {symbol}")
            print(f"账户信息: {account_result}")
            
            if not account_result or 'data' not in account_result:
                raise ValueError("获取账户信息失败")
                
            account = account_result['data'][0]
            
            # 获取持仓信息
            position_result = self._client.mix_get_all_positions(
                productType=self._product_type,
                marginCoin=self._margin_coin
            )
            
            print(f"持仓信息: {position_result}")
            
            if not position_result or 'data' not in position_result:
                raise ValueError("获取持仓信息失败")
                
            # 找到对应交易对的持仓
            position = None
            for pos in position_result['data']:
                if pos.get('symbol') == symbol:
                    position = pos
                    break
                    
            if not position:
                raise ValueError(f"没有找到{symbol}的持仓信息")
                
            # 计算风险指标
            risk_metrics = {
                "margin_ratio": float(account.get('marginRatio', 0)),
                "unrealized_pnl": float(position.get('unrealizedPL', 0)),
                "position_value": float(position.get('total', 0)) * float(position.get('averageOpenPrice', 0)),
                "leverage": float(position.get('leverage', 0))
            }
            
            # 风险预警
            warnings = []
            margin_ratio_threshold = self._config['risk_control']['warning']['margin_ratio_threshold']
            unrealized_pnl_threshold = self._config['risk_control']['warning']['unrealized_pnl_threshold']

            if risk_metrics['margin_ratio'] > margin_ratio_threshold:
                warnings.append(f"保证金率过高 ({risk_metrics['margin_ratio']:.2f} > {margin_ratio_threshold:.2f})")
            if abs(risk_metrics['unrealized_pnl']) > float(account.get('equity', 0)) * unrealized_pnl_threshold:
                warnings.append(f"未实现盈亏过大 ({risk_metrics['unrealized_pnl']:.2f} > {float(account.get('equity', 0)) * unrealized_pnl_threshold:.2f})")
                
            return {
                "status": "success",
                "risk_metrics": risk_metrics,
                "warnings": warnings,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            
        except Exception as e:
            raise Exception(f"风险监控失败: {str(e)}")

    def _manage_margin(self, symbol: str, margin_action: str, amount: float) -> Dict[str, Any]:
        """资金管理：增加/减少保证金，调整杠杆"""
        try:
            if not amount or amount <= 0:
                raise ValueError("资金管理数量无效")

            if margin_action == 'add_margin':
                result = self._client.mix_transfer(marginCoin=self._margin_coin, amount=str(amount), type='transfer_in')
                logger.info(f"成功增加保证金: {amount} {self._margin_coin} for {symbol}. 结果: {result}")
                return {
                    "status": "success",
                    "message": f"成功增加保证金 {amount}",
                    "action": margin_action,
                    "amount": amount,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            elif margin_action == 'reduce_margin':
                result = self._client.mix_transfer(marginCoin=self._margin_coin, amount=str(amount), type='transfer_out')
                logger.info(f"成功减少保证金: {amount} {self._margin_coin} for {symbol}. 结果: {result}")
                return {
                    "status": "success",
                    "message": f"成功减少保证金 {amount}",
                    "action": margin_action,
                    "amount": amount,
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            elif margin_action == 'adjust_leverage':
                result = self._client.mix_set_leverage(symbol=symbol, leverage=str(int(amount)), marginCoin=self._margin_coin)
                logger.info(f"成功调整杠杆为: {int(amount)} for {symbol}. 结果: {result}")
                return {
                    "status": "success",
                    "message": f"成功调整杠杆为 {int(amount)}",
                    "action": margin_action,
                    "amount": int(amount),
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            else:
                raise ValueError(f"不支持的资金管理操作类型: {margin_action}")

        except Exception as e:
            logger.error(f"执行资金管理失败: {str(e)}")
            raise Exception(f"执行资金管理失败: {str(e)}")


if __name__ == '__main__':
    # 测试工具
    tool = BitgetRiskTool()
    
    # 测试对冲锁仓
    # result = tool.run(
    #     trading_pair="BTC",
    #     action="hedge",
    #     hedge_ratio=0.5
    # )
    
    # 测试分批减仓
    # result = tool.run(
    #     trading_pair="BTC",
    #     action="reduce",
    #     reduce_ratio=0.3,
    #     steps=3
    # )
    
    # 测试动态止损
    # result = tool.run(
    #     trading_pair="BTC",
    #     action="dynamic_stop",
    #     trailing_stop=True,
    #     stop_distance=0.02
    # )
    
    # 测试风险监控
    result = tool.run(
        trading_pair="BTC",
        action="monitor"
    )
    
    print("操作结果:", result)