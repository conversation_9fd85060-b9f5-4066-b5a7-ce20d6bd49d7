import pandas as pd
import numpy as np
import logging
from typing import Dict, Union
from .market_database_manager import get_market_data_by_symbol, get_trading_statistics, post_trade_analysis_records

# 配置基础日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

KLIN_TYPE_MAP = {
    1: "1m",
    2: "5m", 
    3: "15m",
    4: "30m",
    5: "1H",
    6: "2H",
    7: "4H",
    8: "1D",
    9: "1W",
    10: "1M"
}

symbol = "BTCUSDT"

def get_cau_line_data(kline_type=3):
    """获取K线原始数据"""
    time_type = KLIN_TYPE_MAP.get(kline_type)
    return sorted(get_market_data_by_symbol(type=time_type, limit=80), key=lambda x: x.open_time)

def convert_data(raw_data):
    """转换原始数据为DataFrame"""
    df = pd.DataFrame([{
        'time': pd.to_datetime(int(k.open_time), unit='ms', utc=True).tz_convert('Asia/Shanghai'),
        'timestamp': k.open_time,
        'open': float(k.open),
        'high': float(k.high),
        'low': float(k.low),
        'close': float(k.close),
        'volume': float(k.volume),
        'quote_asset_volume': float(k.quote_asset_volume)
    } for k in raw_data])
    
    df.set_index('time', inplace=True)
    return df

def detect_professional_divergence(price, indicator, min_bars=5, max_bars=30, min_strength=0.05):
    """
    专业级背离检测算法 - 完全重构
    识别真实的双重底/顶背离形态，减少误报
    
    Args:
        price: 价格数据
        indicator: 技术指标数据
        min_bars: 最小间隔周期
        max_bars: 最大回溯周期
        min_strength: 最小背离强度阈值
    
    Returns:
        tuple: (背离信号, 背离强度, 背离类型)
    """
    try:
        if len(price) == 0 or len(indicator) == 0 or len(price) != len(indicator):
            empty_series = pd.Series(0, index=price.index if len(price) > 0 else pd.Index([]))
            return empty_series, empty_series.astype(float), empty_series.astype(str)
        
        divergence_signals = pd.Series(0, index=price.index)
        divergence_strength = pd.Series(0.0, index=price.index)
        divergence_type = pd.Series('', index=price.index)
        
        # 寻找局部极值点
        def find_peaks_troughs(data, window=3):
            peaks = []
            troughs = []
            
            if len(data) < window * 2 + 1:
                return peaks, troughs
            
            for i in range(window, len(data) - window):
                try:
                    # 寻找峰值
                    if all(data.iloc[i] >= data.iloc[i-j] for j in range(1, window+1)) and \
                       all(data.iloc[i] >= data.iloc[i+j] for j in range(1, window+1)):
                        if data.iloc[i] > data.iloc[i-window] or data.iloc[i] > data.iloc[i+window]:
                            peaks.append((i, data.iloc[i]))
                    
                    # 寻找谷值
                    if all(data.iloc[i] <= data.iloc[i-j] for j in range(1, window+1)) and \
                       all(data.iloc[i] <= data.iloc[i+j] for j in range(1, window+1)):
                        if data.iloc[i] < data.iloc[i-window] or data.iloc[i] < data.iloc[i+window]:
                            troughs.append((i, data.iloc[i]))
                except IndexError:
                    continue
            
            return peaks, troughs
    
        price_peaks, price_troughs = find_peaks_troughs(price)
        indicator_peaks, indicator_troughs = find_peaks_troughs(indicator)
        
        if len(price_troughs) < 2 or len(indicator_troughs) < 2:
            return divergence_signals, divergence_strength, divergence_type
        
        # 检测牛市背离（价格创新低，指标不创新低）
        for i, (curr_idx, curr_price) in enumerate(price_troughs[1:], 1):
            for prev_idx, prev_price in price_troughs[max(0, i-3):i]:
                if min_bars <= curr_idx - prev_idx <= max_bars:
                    # 价格创新低
                    if curr_price < prev_price:
                        # 查找对应的指标值
                        curr_indicator = None
                        prev_indicator = None
                        
                        # 在指标谷值中寻找最接近的点
                        for ind_idx, ind_val in indicator_troughs:
                            if abs(ind_idx - curr_idx) <= 3:
                                curr_indicator = ind_val
                            if abs(ind_idx - prev_idx) <= 3:
                                prev_indicator = ind_val
                        
                        if curr_indicator is not None and prev_indicator is not None and prev_indicator != 0:
                            # 指标未创新低（背离）
                            if curr_indicator > prev_indicator:
                                strength = abs((curr_indicator - prev_indicator) / prev_indicator)
                                if strength >= min_strength and curr_idx < len(divergence_signals):
                                    divergence_signals.iloc[curr_idx] = 1
                                    divergence_strength.iloc[curr_idx] = strength
                                    divergence_type.iloc[curr_idx] = 'bullish_regular'
    
        # 检测熊市背离（价格创新高，指标不创新高）
        for i, (curr_idx, curr_price) in enumerate(price_peaks[1:], 1):
            for prev_idx, prev_price in price_peaks[max(0, i-3):i]:
                if min_bars <= curr_idx - prev_idx <= max_bars:
                    # 价格创新高
                    if curr_price > prev_price:
                        # 查找对应的指标值
                        curr_indicator = None
                        prev_indicator = None
                        
                        # 在指标峰值中寻找最接近的点
                        for ind_idx, ind_val in indicator_peaks:
                            if abs(ind_idx - curr_idx) <= 3:
                                curr_indicator = ind_val
                            if abs(ind_idx - prev_idx) <= 3:
                                prev_indicator = ind_val
                        
                        if curr_indicator is not None and prev_indicator is not None and prev_indicator != 0:
                            # 指标未创新高（背离）
                            if curr_indicator < prev_indicator:
                                strength = abs((prev_indicator - curr_indicator) / prev_indicator)
                                if strength >= min_strength and curr_idx < len(divergence_signals):
                                    divergence_signals.iloc[curr_idx] = -1
                                    divergence_strength.iloc[curr_idx] = strength
                                    divergence_type.iloc[curr_idx] = 'bearish_regular'
    
        # 检测隐藏背离
        # 牛市隐藏背离：价格更高的低点，指标更低的低点
        for i, (curr_idx, curr_price) in enumerate(price_troughs[1:], 1):
            for prev_idx, prev_price in price_troughs[max(0, i-3):i]:
                if min_bars <= curr_idx - prev_idx <= max_bars:
                    # 价格更高的低点
                    if curr_price > prev_price:
                        curr_indicator = None
                        prev_indicator = None
                        
                        for ind_idx, ind_val in indicator_troughs:
                            if abs(ind_idx - curr_idx) <= 3:
                                curr_indicator = ind_val
                            if abs(ind_idx - prev_idx) <= 3:
                                prev_indicator = ind_val
                        
                        if curr_indicator is not None and prev_indicator is not None and prev_indicator != 0:
                            # 指标更低的低点（隐藏背离）
                            if curr_indicator < prev_indicator:
                                strength = abs((prev_indicator - curr_indicator) / prev_indicator)
                                if strength >= min_strength and curr_idx < len(divergence_signals):
                                    divergence_signals.iloc[curr_idx] = 1
                                    divergence_strength.iloc[curr_idx] = strength * 1.2  # 隐藏背离权重更高
                                    divergence_type.iloc[curr_idx] = 'bullish_hidden'
        
        return divergence_signals, divergence_strength, divergence_type
    
    except Exception as e:
        logger.error(f"背离检测异常: {str(e)}")
        empty_series = pd.Series(0, index=price.index if len(price) > 0 else pd.Index([]))
        return empty_series, empty_series.astype(float), empty_series.astype(str)

def calculate_comprehensive_indicators(data):
    """
    专业级技术分析指标计算
    为Agent提供全面的技术数据，无预设建议
    """
    if not isinstance(data, pd.DataFrame):
        raise ValueError("Input data must be a pandas DataFrame")
    
    required_columns = ['open', 'high', 'low', 'close', 'volume']
    missing_cols = [col for col in required_columns if col not in data.columns]
    if missing_cols:
        raise KeyError(f"Missing required columns: {missing_cols}")
    
    # 1. MACD指标族
    def calculate_macd_enhanced(prices, fast=12, slow=26, signal=9):
        ema_fast = prices.ewm(span=fast, adjust=False).mean()
        ema_slow = prices.ewm(span=slow, adjust=False).mean()
        macd_line = ema_fast - ema_slow
        signal_line = macd_line.ewm(span=signal, adjust=False).mean()
        histogram = macd_line - signal_line
        # 使用新的专业背离检测
        macd_divergence, macd_div_strength, macd_div_type = detect_professional_divergence(prices, macd_line)
        macd_momentum = macd_line.diff()
        return macd_line, signal_line, histogram, macd_divergence, macd_momentum, macd_div_strength, macd_div_type
    
    (data['macd'], data['signal'], data['hist'], data['macd_divergence'], 
     data['macd_momentum'], data['macd_div_strength'], data['macd_div_type']) = calculate_macd_enhanced(data['close'])
    data['macd_cross'] = (data['macd'] > data['signal']) & (data['macd'].shift(1) <= data['signal'].shift(1))
    data['signal_cross'] = (data['macd'] < data['signal']) & (data['macd'].shift(1) >= data['signal'].shift(1))
    
    # 2. RSI指标族
    def calculate_rsi_enhanced(prices, period=14):
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            
            # 避免除零错误
            loss = loss.replace(0, np.finfo(float).eps)
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            
            # 使用增强的背离检测
            rsi_divergence, rsi_div_strength, rsi_div_type = detect_professional_divergence(prices, rsi)
            return rsi, rsi_divergence, rsi_div_strength, rsi_div_type
        except Exception as e:
            logger.warning(f"RSI计算异常: {e}")
            return prices * 0 + 50, prices * 0, prices * 0, prices.astype(str).replace('.+', '', regex=True)
    
    (data['rsi'], data['rsi_divergence'], data['rsi_div_strength'], data['rsi_div_type']) = calculate_rsi_enhanced(data['close'])
    data['rsi_fast'], _, _, _ = calculate_rsi_enhanced(data['close'], 7)
    data['rsi_slow'], _, _, _ = calculate_rsi_enhanced(data['close'], 21)
    
    # 3. 布林带指标
    def calculate_bollinger_bands(prices, period=20, std_dev=2):
        sma = prices.rolling(window=period).mean()
        std = prices.rolling(window=period).std()
        upper_band = sma + (std * std_dev)
        lower_band = sma - (std * std_dev)
        bb_width = (upper_band - lower_band) / sma
        bb_position = (prices - lower_band) / (upper_band - lower_band)
        bb_squeeze = bb_width < bb_width.rolling(window=20).quantile(0.1)
        return upper_band, sma, lower_band, bb_width, bb_position, bb_squeeze
    
    data['bb_upper'], data['bb_middle'], data['bb_lower'], data['bb_width'], data['bb_position'], data['bb_squeeze'] = calculate_bollinger_bands(data['close'])
    
    # 4. ATR波动率指标
    def calculate_atr_enhanced(high, low, close, period=14):
        try:
            tr1 = high - low
            tr2 = abs(high - close.shift(1))
            tr3 = abs(low - close.shift(1))
            tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
            atr = tr.rolling(window=period).mean()
            
            # 避免空值
            atr = atr.fillna(method='bfill').fillna(0)
            
            atr_percentile = atr.rolling(window=50).rank(pct=True)
            atr_percentile = atr_percentile.fillna(0.5)
            
            volatility_regime = pd.cut(atr_percentile, bins=[0, 0.3, 0.7, 1.0], labels=['Low', 'Medium', 'High'])
            return atr, volatility_regime
        except Exception as e:
            logger.warning(f"ATR计算异常: {e}")
            return high * 0, pd.Series(['Medium'] * len(high), index=high.index)
    
    data['atr'], data['volatility_regime'] = calculate_atr_enhanced(data['high'], data['low'], data['close'])
    
    # 5. 随机指标
    def calculate_stochastic(high, low, close, k_period=14, d_period=3):
        lowest_low = low.rolling(window=k_period).min()
        highest_high = high.rolling(window=k_period).max()
        k_percent = 100 * ((close - lowest_low) / (highest_high - lowest_low))
        d_percent = k_percent.rolling(window=d_period).mean()
        return k_percent, d_percent
    
    data['stoch_k'], data['stoch_d'] = calculate_stochastic(data['high'], data['low'], data['close'])
    
    # 6. Williams %R
    def calculate_williams_r(high, low, close, period=14):
        highest_high = high.rolling(window=period).max()
        lowest_low = low.rolling(window=period).min()
        williams_r = -100 * ((highest_high - close) / (highest_high - lowest_low))
        return williams_r
    
    data['williams_r'] = calculate_williams_r(data['high'], data['low'], data['close'])
    
    # 7. 增强成交量指标和机构资金流向分析
    def calculate_advanced_volume_indicators(close, volume, high, low, open_price):
        # 原有OBV、VWAP等
        obv = (volume * ((close > close.shift(1)).astype(int) - (close < close.shift(1)).astype(int))).cumsum()
        typical_price = (high + low + close) / 3
        vwap = (typical_price * volume).cumsum() / volume.cumsum()
        volume_roc = volume.pct_change(periods=10) * 100
        
        # 资金流量指标
        money_flow_multiplier = ((close - low) - (high - close)) / (high - low)
        money_flow_volume = money_flow_multiplier * volume
        ad_line = money_flow_volume.cumsum()
        cmf = money_flow_volume.rolling(window=20).sum() / volume.rolling(window=20).sum()
        
        # 新增：价量配合分析
        price_change = close.pct_change()
        volume_change = volume.pct_change()
        pv_correlation = price_change.rolling(window=20).corr(volume_change)
        
        # Volume Profile 简化版：成交量分布分析
        volume_weighted_price = (typical_price * volume).rolling(window=20).sum() / volume.rolling(window=20).sum()
        volume_deviation = abs(typical_price - volume_weighted_price) / volume_weighted_price
        
        # 机构资金流向指标
        # 大单流向检测（基于成交量异常）
        volume_ma = volume.rolling(window=20).mean()
        volume_std = volume.rolling(window=20).std()
        large_volume_threshold = volume_ma + 2 * volume_std
        large_volume_flow = pd.Series(0, index=close.index)
        
        for i in range(len(volume)):
            if volume.iloc[i] > large_volume_threshold.iloc[i]:
                if close.iloc[i] > open_price.iloc[i]:  # 上涨大单
                    large_volume_flow.iloc[i] = 1
                elif close.iloc[i] < open_price.iloc[i]:  # 下跌大单
                    large_volume_flow.iloc[i] = -1
        
        # 累积大单流向
        cumulative_large_flow = large_volume_flow.rolling(window=20).sum()
        
        # 成交量能量指标
        volume_energy = (volume * abs(price_change)).rolling(window=14).sum()
        volume_momentum = volume.rolling(window=10).mean() / volume.rolling(window=30).mean()
        
        # Wyckoff 理论指标：累积/派发检测
        def detect_accumulation_distribution(close, volume, high, low):
            try:
                # 简化版Wyckoff分析
                high_low_diff = high - low
                high_low_diff = high_low_diff.replace(0, np.finfo(float).eps)  # 避免除零
                
                clv = ((close - low) - (high - close)) / high_low_diff
                # 避免除以零
                clv = clv.fillna(0)
                wyckoff_ad = (clv * volume).cumsum()
                
                # 检测累积或派发信号
                def safe_polyfit_slope(x):
                    try:
                        if len(x) < 2 or x.isna().all():
                            return 0
                        return np.polyfit(range(len(x)), x.fillna(0), 1)[0]
                    except:
                        return 0
                
                ad_slope = wyckoff_ad.rolling(window=20).apply(safe_polyfit_slope)
                volume_trend = volume.rolling(window=20).mean()
                
                accumulation_signal = (ad_slope > 0) & (volume_trend > volume_trend.shift(10))
                distribution_signal = (ad_slope < 0) & (volume_trend > volume_trend.shift(10))
                
                return wyckoff_ad.fillna(0), accumulation_signal.astype(int), distribution_signal.astype(int)
            except Exception as e:
                logger.warning(f"Wyckoff指标计算异常: {e}")
                return close * 0, close.astype(int) * 0, close.astype(int) * 0
        
        wyckoff_ad, accumulation, distribution = detect_accumulation_distribution(close, volume, high, low)
        
        return (obv, vwap, volume_roc, ad_line, cmf, pv_correlation, 
                volume_weighted_price, volume_deviation, large_volume_flow,
                cumulative_large_flow, volume_energy, volume_momentum,
                wyckoff_ad, accumulation, distribution)
    
    (data['obv'], data['vwap'], data['volume_roc'], data['ad_line'], data['cmf'], 
     data['pv_correlation'], data['volume_weighted_price'], data['volume_deviation'],
     data['large_volume_flow'], data['cumulative_large_flow'], data['volume_energy'],
     data['volume_momentum'], data['wyckoff_ad'], data['accumulation'], data['distribution']
    ) = calculate_advanced_volume_indicators(data['close'], data['volume'], data['high'], data['low'], data['open'])
    
    # 8. 移动平均线
    data['sma_10'] = data['close'].rolling(window=10).mean()
    data['sma_20'] = data['close'].rolling(window=20).mean()
    data['sma_50'] = data['close'].rolling(window=50).mean()
    data['ema_10'] = data['close'].ewm(span=10, adjust=False).mean()
    data['ema_20'] = data['close'].ewm(span=20, adjust=False).mean()
    data['ema_50'] = data['close'].ewm(span=50, adjust=False).mean()
    
    # 9. ADX趋势强度
    def calculate_adx(high, low, close, period=14):
        try:
            tr = pd.concat([high - low, abs(high - close.shift(1)), abs(low - close.shift(1))], axis=1).max(axis=1)
            plus_dm = np.where((high - high.shift(1)) > (low.shift(1) - low), 
                              np.maximum(high - high.shift(1), 0), 0)
            minus_dm = np.where((low.shift(1) - low) > (high - high.shift(1)), 
                               np.maximum(low.shift(1) - low, 0), 0)
            
            plus_dm = pd.Series(plus_dm, index=high.index).rolling(window=period).mean()
            minus_dm = pd.Series(minus_dm, index=high.index).rolling(window=period).mean()
            tr_smooth = tr.rolling(window=period).mean()
            
            # 避免除零错误
            tr_smooth = tr_smooth.replace(0, np.finfo(float).eps)
            
            plus_di = 100 * (plus_dm / tr_smooth)
            minus_di = 100 * (minus_dm / tr_smooth)
            
            # 避免除零错误
            di_sum = plus_di + minus_di
            di_sum = di_sum.replace(0, np.finfo(float).eps)
            
            dx = 100 * abs(plus_di - minus_di) / di_sum
            adx = dx.rolling(window=period).mean()
            
            return adx.fillna(0), plus_di.fillna(0), minus_di.fillna(0)
        except Exception as e:
            logger.warning(f"ADX计算异常: {e}")
            return high * 0, high * 0, high * 0
    
    data['adx'], data['plus_di'], data['minus_di'] = calculate_adx(data['high'], data['low'], data['close'])
    
    # 10. 一目均衡表
    def calculate_ichimoku(high, low, close):
        period_9_high = high.rolling(window=9).max()
        period_9_low = low.rolling(window=9).min()
        tenkan_sen = (period_9_high + period_9_low) / 2
        
        period_26_high = high.rolling(window=26).max()
        period_26_low = low.rolling(window=26).min()
        kijun_sen = (period_26_high + period_26_low) / 2
        
        senkou_span_a = ((tenkan_sen + kijun_sen) / 2).shift(26)
        
        period_52_high = high.rolling(window=52).max()
        period_52_low = low.rolling(window=52).min()
        senkou_span_b = ((period_52_high + period_52_low) / 2).shift(26)
        
        chikou_span = close.shift(-26)
        cloud_thickness = abs(senkou_span_a - senkou_span_b)
        
        cloud_top = pd.concat([senkou_span_a, senkou_span_b], axis=1).max(axis=1)
        cloud_bottom = pd.concat([senkou_span_a, senkou_span_b], axis=1).min(axis=1)
        
        trend_signal = pd.Series(0, index=close.index)
        trend_signal[close > cloud_top] = 1
        trend_signal[close < cloud_bottom] = -1
        
        return tenkan_sen, kijun_sen, senkou_span_a, senkou_span_b, chikou_span, cloud_thickness, trend_signal
    
    data['tenkan_sen'], data['kijun_sen'], data['senkou_span_a'], data['senkou_span_b'], data['chikou_span'], data['cloud_thickness'], data['ichimoku_trend'] = calculate_ichimoku(data['high'], data['low'], data['close'])
    
    # 11. 斐波那契回撤
    def calculate_fibonacci_levels(high, low, period=20):
        recent_high = high.rolling(window=period).max()
        recent_low = low.rolling(window=period).min()
        price_range = recent_high - recent_low
        
        fib_236 = recent_high - (price_range * 0.236)
        fib_382 = recent_high - (price_range * 0.382)
        fib_500 = recent_high - (price_range * 0.500)
        fib_618 = recent_high - (price_range * 0.618)
        fib_786 = recent_high - (price_range * 0.786)
        
        return fib_236, fib_382, fib_500, fib_618, fib_786, recent_high, recent_low
    
    data['fib_236'], data['fib_382'], data['fib_500'], data['fib_618'], data['fib_786'], data['swing_high'], data['swing_low'] = calculate_fibonacci_levels(data['high'], data['low'])
    
    # 12. 其他指标
    # CCI商品通道指数
    def calculate_cci(high, low, close, period=20):
        typical_price = (high + low + close) / 3
        sma_tp = typical_price.rolling(window=period).mean()
        mad = typical_price.rolling(window=period).apply(lambda x: np.mean(np.abs(x - x.mean())))
        cci = (typical_price - sma_tp) / (0.015 * mad)
        return cci
    
    data['cci'] = calculate_cci(data['high'], data['low'], data['close'])
    
    # MFI资金流量指数
    def calculate_mfi(high, low, close, volume, period=14):
        typical_price = (high + low + close) / 3
        money_flow = typical_price * volume
        positive_flow = money_flow.where(typical_price > typical_price.shift(1), 0).rolling(window=period).sum()
        negative_flow = money_flow.where(typical_price < typical_price.shift(1), 0).rolling(window=period).sum()
        money_ratio = positive_flow / negative_flow
        mfi = 100 - (100 / (1 + money_ratio))
        return mfi
    
    data['mfi'] = calculate_mfi(data['high'], data['low'], data['close'], data['volume'])
    
    return data

def find_professional_support_resistance(data, lookback_periods=[20, 50, 100], 
                                        volume_threshold=1.5, cluster_tolerance=0.002):
    """
    专业级支撑阻力位计算 - 完全重构
    基于成交量确认、价格聚类和历史有效性验证
    
    Args:
        data: 包含OHLCV的DataFrame
        lookback_periods: 不同时间周期的回溯期数
        volume_threshold: 成交量确认阈值（相对于平均成交量）
        cluster_tolerance: 价格聚类容差率
    
    Returns:
        dict: 包含不同强度的支撑阻力位
    """
    try:
        if data.empty or len(data) < 10:
            logger.warning("数据不足，无法计算支撑阻力位")
            return {
                'current_price': 0,
                'supports': {'primary': None, 'secondary': None, 'all': []},
                'resistances': {'primary': None, 'secondary': None, 'all': []},
                'analysis_metadata': {'total_potential_levels': 0, 'avg_volume': 0, 'lookback_periods': lookback_periods}
            }
        
        current_price = float(data.iloc[-1]['close'])
        avg_volume = float(data['volume'].tail(20).mean())
        
        # 收集所有潜在的价格水平
        potential_levels = []
        
        for period in lookback_periods:
            if len(data) < period:
                continue
                
            period_data = data.tail(period).copy()
            
            # 1. 寻找成交量放大的关键价格点
            high_volume_indices = period_data[period_data['volume'] > avg_volume * volume_threshold].index
            
            for idx in high_volume_indices:
                try:
                    row = period_data.loc[idx]
                    # 添加高低点和收盘价
                    potential_levels.extend([
                        {'price': float(row['high']), 'volume': float(row['volume']), 'type': 'resistance', 'timestamp': idx, 'period': period},
                        {'price': float(row['low']), 'volume': float(row['volume']), 'type': 'support', 'timestamp': idx, 'period': period},
                        {'price': float(row['close']), 'volume': float(row['volume']), 'type': 'pivot', 'timestamp': idx, 'period': period}
                    ])
                except (KeyError, TypeError) as e:
                    logger.warning(f"处理索引 {idx} 时出错: {e}")
                    continue
            
            # 2. 寻找局部极值点
            period_data_reset = period_data.reset_index(drop=True)
            for i in range(2, len(period_data_reset) - 2):
                try:
                    current_row = period_data_reset.iloc[i]
                    original_idx = period_data.index[i]
                    
                    # 阻力位：局部最高点
                    if (current_row['high'] > period_data_reset.iloc[i-1]['high'] and 
                        current_row['high'] > period_data_reset.iloc[i-2]['high'] and
                        current_row['high'] > period_data_reset.iloc[i+1]['high'] and 
                        current_row['high'] > period_data_reset.iloc[i+2]['high']):
                        
                        potential_levels.append({
                            'price': float(current_row['high']), 
                            'volume': float(current_row['volume']), 
                            'type': 'resistance',
                            'timestamp': original_idx,
                            'period': period
                        })
                    
                    # 支撑位：局部最低点
                    if (current_row['low'] < period_data_reset.iloc[i-1]['low'] and 
                        current_row['low'] < period_data_reset.iloc[i-2]['low'] and
                        current_row['low'] < period_data_reset.iloc[i+1]['low'] and 
                        current_row['low'] < period_data_reset.iloc[i+2]['low']):
                        
                        potential_levels.append({
                            'price': float(current_row['low']), 
                            'volume': float(current_row['volume']), 
                            'type': 'support',
                            'timestamp': original_idx,
                            'period': period
                        })
                        
                except (IndexError, KeyError) as e:
                    continue
        # 3. 价格聚类分析
        def cluster_price_levels(levels, tolerance):
            if not levels:
                return []
                
            # 按价格排序
            sorted_levels = sorted(levels, key=lambda x: x['price'])
            clusters = []
            current_cluster = [sorted_levels[0]]
            
            for level in sorted_levels[1:]:
                # 检查是否在容差范围内
                if abs(level['price'] - current_cluster[-1]['price']) / max(current_cluster[-1]['price'], 0.001) <= tolerance:
                    current_cluster.append(level)
                else:
                    clusters.append(current_cluster)
                    current_cluster = [level]
            
            clusters.append(current_cluster)
            
            # 计算每个聚类的权重和代表价格
            clustered_levels = []
            for cluster in clusters:
                if len(cluster) >= 2:  # 至少需2个点才认为是有效聚类
                    total_volume = sum(l['volume'] for l in cluster)
                    if total_volume > 0:
                        avg_price = sum(l['price'] * l['volume'] for l in cluster) / total_volume
                    else:
                        avg_price = sum(l['price'] for l in cluster) / len(cluster)
                    
                    strength = len(cluster) + (total_volume / max(avg_volume, 1))
                    
                    clustered_levels.append({
                        'price': avg_price,
                        'strength': strength,
                        'count': len(cluster),
                        'total_volume': total_volume,
                        'type': max(set(l['type'] for l in cluster), key=lambda x: sum(1 for l in cluster if l['type'] == x))
                    })
            
            return sorted(clustered_levels, key=lambda x: x['strength'], reverse=True)
        
        # 分别聚类支撑和阻力
        support_levels = [l for l in potential_levels if l['type'] == 'support' and l['price'] < current_price]
        resistance_levels = [l for l in potential_levels if l['type'] == 'resistance' and l['price'] > current_price]
        
        clustered_supports = cluster_price_levels(support_levels, cluster_tolerance)
        clustered_resistances = cluster_price_levels(resistance_levels, cluster_tolerance)
        
        # 4. 历史有效性验证
        def validate_level_effectiveness(price_level, data, tolerance=0.01):
            """验证支撑/阻力位的历史有效性"""
            touches = 0
            bounces = 0
            
            try:
                for i in range(len(data)):
                    row = data.iloc[i]
                    # 检查是否触及该价格水平
                    if abs(row['low'] - price_level) / max(price_level, 0.001) <= tolerance or \
                       abs(row['high'] - price_level) / max(price_level, 0.001) <= tolerance:
                        touches += 1
                        
                        # 检查是否反弹（简化版）
                        if i < len(data) - 1:
                            next_row = data.iloc[i + 1]
                            if (row['low'] <= price_level <= row['high'] and 
                                abs(next_row['close'] - price_level) > abs(row['close'] - price_level)):
                                bounces += 1
            except Exception as e:
                logger.warning(f"有效性验证异常: {e}")
            
            effectiveness = bounces / max(touches, 1) if touches > 0 else 0
            return effectiveness, touches, bounces
        # 验证并评分所有水平
        validated_supports = []
        for support in clustered_supports[:5]:  # 只验证前5个最强的
            effectiveness, touches, bounces = validate_level_effectiveness(support['price'], data)
            support['effectiveness'] = effectiveness
            support['touches'] = touches
            support['bounces'] = bounces
            support['final_score'] = support['strength'] * (1 + effectiveness)
            validated_supports.append(support)
        
        validated_resistances = []
        for resistance in clustered_resistances[:5]:
            effectiveness, touches, bounces = validate_level_effectiveness(resistance['price'], data)
            resistance['effectiveness'] = effectiveness
            resistance['touches'] = touches
            resistance['bounces'] = bounces
            resistance['final_score'] = resistance['strength'] * (1 + effectiveness)
            validated_resistances.append(resistance)
        
        # 排序并返回结果
        validated_supports.sort(key=lambda x: x['final_score'], reverse=True)
        validated_resistances.sort(key=lambda x: x['final_score'], reverse=True)
        
        return {
            'current_price': current_price,
            'supports': {
                'primary': validated_supports[0] if validated_supports else None,
                'secondary': validated_supports[1] if len(validated_supports) > 1 else None,
                'all': validated_supports
            },
            'resistances': {
                'primary': validated_resistances[0] if validated_resistances else None,
                'secondary': validated_resistances[1] if len(validated_resistances) > 1 else None,
                'all': validated_resistances
            },
            'analysis_metadata': {
                'total_potential_levels': len(potential_levels),
                'avg_volume': avg_volume,
                'lookback_periods': lookback_periods
            }
        }
    
    except Exception as e:
        logger.error(f"支撑阻力位计算异常: {str(e)}")
        return {
            'current_price': data.iloc[-1]['close'] if not data.empty else 0,
            'supports': {'primary': None, 'secondary': None, 'all': []},
            'resistances': {'primary': None, 'secondary': None, 'all': []},
            'analysis_metadata': {'total_potential_levels': 0, 'avg_volume': 0, 'lookback_periods': lookback_periods}
        }

def get_enhanced_technical_analysis_data(data, support_resistance_analysis):
    """
    增强版技术分析数据提供者 - 无建议
    为Agent提供全面增强技术数据用于自主决策
    """
    try:
        if data.empty:
            logger.warning("数据为空，返回默认值")
            return {"error": "Empty data provided"}
        
        latest = data.iloc[-1]
        prev = data.iloc[-2] if len(data) > 1 else latest
        current_price = float(latest['close'])
        
        # 获取支撑阻力信息
        primary_support = support_resistance_analysis.get('supports', {}).get('primary')
        primary_resistance = support_resistance_analysis.get('resistances', {}).get('primary')
        
        support_level = float(primary_support['price']) if primary_support else current_price * 0.95
        resistance_level = float(primary_resistance['price']) if primary_resistance else current_price * 1.05
        
        # 安全获取数值的函数
        def safe_get(series_or_value, default=0):
            try:
                if hasattr(series_or_value, 'iloc'):
                    return float(series_or_value.iloc[-1] if not series_or_value.empty else default)
                return float(series_or_value) if series_or_value is not None else default
            except (TypeError, ValueError, IndexError):
                return default
        return {
            "current_price": current_price,
            "price_change": current_price - float(prev['close']),
            "price_change_pct": ((current_price - float(prev['close'])) / float(prev['close'])) * 100,
            
            "support_resistance": {
                "support_level": support_level,
                "resistance_level": resistance_level,
                "distance_to_support": abs(current_price - support_level) / max(support_level, 0.001),
                "distance_to_resistance": abs(current_price - resistance_level) / max(resistance_level, 0.001),
                "support_strength": safe_get(primary_support['final_score'] if primary_support else 0),
                "resistance_strength": safe_get(primary_resistance['final_score'] if primary_resistance else 0),
                "support_touches": safe_get(primary_support['touches'] if primary_support else 0),
                "resistance_touches": safe_get(primary_resistance['touches'] if primary_resistance else 0),
                "support_effectiveness": safe_get(primary_support['effectiveness'] if primary_support else 0),
                "resistance_effectiveness": safe_get(primary_resistance['effectiveness'] if primary_resistance else 0)
            },
            
            "macd": {
                "macd_line": safe_get(latest['macd']),
                "signal_line": safe_get(latest['signal']),
                "histogram": safe_get(latest['hist']),
                "macd_cross_bullish": bool(latest.get('macd_cross', False)),
                "macd_cross_bearish": bool(latest.get('signal_cross', False)),
                "macd_momentum": safe_get(latest.get('macd_momentum', 0)),
                "macd_divergence": safe_get(latest.get('macd_divergence', 0)),
                "macd_divergence_strength": safe_get(latest.get('macd_div_strength', 0)),
                "macd_divergence_type": str(latest.get('macd_div_type', ''))
            },
            
            "rsi": {
                "rsi_14": safe_get(latest['rsi'], 50),
                "rsi_7": safe_get(latest.get('rsi_fast', latest['rsi']), 50),
                "rsi_21": safe_get(latest.get('rsi_slow', latest['rsi']), 50),
                "rsi_divergence": safe_get(latest.get('rsi_divergence', 0)),
                "rsi_divergence_strength": safe_get(latest.get('rsi_div_strength', 0)),
                "rsi_divergence_type": str(latest.get('rsi_div_type', ''))
            },
            
            "bollinger": {
                "upper_band": safe_get(latest['bb_upper'], current_price * 1.02),
                "middle_band": safe_get(latest['bb_middle'], current_price),
                "lower_band": safe_get(latest['bb_lower'], current_price * 0.98),
                "bb_position": safe_get(latest['bb_position'], 0.5),
                "bb_width": safe_get(latest['bb_width'], 0.02),
                "bb_squeeze": bool(latest.get('bb_squeeze', False))
            },
            
            "volatility": {
                "atr": safe_get(latest['atr'], current_price * 0.02),
                "volatility_regime": str(latest.get('volatility_regime', 'Medium'))
            },
            
            "momentum": {
                "stoch_k": safe_get(latest['stoch_k'], 50),
                "stoch_d": safe_get(latest['stoch_d'], 50),
                "williams_r": safe_get(latest['williams_r'], -50)
            },
            
            "volume": {
                "current_volume": safe_get(latest['volume']),
                "volume_ma_20": safe_get(data['volume'].tail(20).mean()),
                "obv": safe_get(latest['obv']),
                "vwap": safe_get(latest['vwap'], current_price),
                "volume_roc": safe_get(latest['volume_roc']),
                "ad_line": safe_get(latest.get('ad_line', 0)),
                "cmf": safe_get(latest.get('cmf', 0)),
                "mfi": safe_get(latest.get('mfi', 50)),
                "pv_correlation": safe_get(latest.get('pv_correlation', 0)),
                "volume_weighted_price": safe_get(latest.get('volume_weighted_price', current_price)),
                "volume_deviation": safe_get(latest.get('volume_deviation', 0)),
                "large_volume_flow": safe_get(latest.get('large_volume_flow', 0)),
                "cumulative_large_flow": safe_get(latest.get('cumulative_large_flow', 0)),
                "volume_energy": safe_get(latest.get('volume_energy', 0)),
                "volume_momentum": safe_get(latest.get('volume_momentum', 1)),
                "wyckoff_ad": safe_get(latest.get('wyckoff_ad', 0)),
                "accumulation_signal": safe_get(latest.get('accumulation', 0)),
                "distribution_signal": safe_get(latest.get('distribution', 0))
            },
            
            "moving_averages": {
                "sma_10": safe_get(latest.get('sma_10', current_price)),
                "sma_20": safe_get(latest['sma_20'], current_price),
                "sma_50": safe_get(latest['sma_50'], current_price),
                "ema_10": safe_get(latest.get('ema_10', current_price)),
                "ema_20": safe_get(latest['ema_20'], current_price),
                "ema_50": safe_get(latest['ema_50'], current_price)
            },
            
            "trend": {
                "adx": safe_get(latest['adx'], 25),
                "plus_di": safe_get(latest['plus_di'], 25),
                "minus_di": safe_get(latest['minus_di'], 25)
            },
            
            "ichimoku": {
                "tenkan_sen": safe_get(latest['tenkan_sen'], current_price),
                "kijun_sen": safe_get(latest['kijun_sen'], current_price),
                "senkou_span_a": safe_get(latest['senkou_span_a'], current_price),
                "senkou_span_b": safe_get(latest['senkou_span_b'], current_price),
                "chikou_span": safe_get(latest['chikou_span'], current_price),
                "cloud_thickness": safe_get(latest['cloud_thickness']),
                "price_to_cloud": safe_get(latest['ichimoku_trend'])
            },
            
            "fibonacci": {
                "fib_236": safe_get(latest['fib_236'], current_price),
                "fib_382": safe_get(latest['fib_382'], current_price),
                "fib_500": safe_get(latest['fib_500'], current_price),
                "fib_618": safe_get(latest['fib_618'], current_price),
                "fib_786": safe_get(latest['fib_786'], current_price),
                "swing_high": safe_get(latest['swing_high'], current_price * 1.05),
                "swing_low": safe_get(latest['swing_low'], current_price * 0.95)
            },
            
            "additional": {
                "cci": safe_get(latest.get('cci', 0)),
                "mfi": safe_get(latest.get('mfi', 50))
            },
            
            "market_structure": {
                "support_resistance_quality": "high" if (primary_support and primary_support.get('effectiveness', 0) > 0.6) else "medium" if primary_support else "low",
                "volume_profile_signal": "bullish" if safe_get(latest.get('accumulation', 0)) > 0 else "bearish" if safe_get(latest.get('distribution', 0)) > 0 else "neutral",
                "institutional_flow": "buying" if safe_get(latest.get('cumulative_large_flow', 0)) > 5 else "selling" if safe_get(latest.get('cumulative_large_flow', 0)) < -5 else "neutral"
            }
        }
    
    except Exception as e:
        logger.error(f"技术分析数据生成异常: {str(e)}")
        return {"error": f"Technical analysis error: {str(e)}"}

def get_k_line_data(kline_type=3) -> Dict[str, Union[float, str, Dict]]:
    """
    获取并处理K线数据，提供纯技术分析数据供Agent自主决策
    Args:
        kline_type: K线类型
    Returns:
        Dict: 包含全面技术指标数据，无预设建议
    """
    try:
        # 验证输入参数
        if kline_type not in KLIN_TYPE_MAP:
            logger.error(f"不支持的K线类型: {kline_type}")
            return {"error": f"Unsupported kline_type: {kline_type}"}
        
        # 获取原始数据
        raw_data = get_cau_line_data(kline_type)
        
        if not raw_data or len(raw_data) < 10:
            logger.warning(f"数据不足，获取到 {len(raw_data) if raw_data else 0} 条记录")
            return {"error": "Insufficient data"}
        
        # 转换数据
        data = convert_data(raw_data)
        
        if data.empty:
            logger.warning("转换后的数据集为空")
            return {"error": "Empty processed data"}
        
        # 验证数据质量
        required_cols = ['open', 'high', 'low', 'close', 'volume']
        missing_cols = [col for col in required_cols if col not in data.columns]
        if missing_cols:
            logger.error(f"数据缺少必要列: {missing_cols}")
            return {"error": f"Missing required columns: {missing_cols}"}
        
        # 检查数据合理性
        if (data['high'] < data['low']).any():
            logger.warning("检测到不合理的价格数据：高价小于低价")
        
        if (data['volume'] < 0).any():
            logger.warning("检测到负数成交量")
            data['volume'] = data['volume'].abs()
        
        # 计算所有技术指标
        data = calculate_comprehensive_indicators(data)
        
        # 计算专业级支撑阻力位
        support_resistance_analysis = find_professional_support_resistance(data)
        
        # 获取技术分析数据
        technical_analysis = get_enhanced_technical_analysis_data(data, support_resistance_analysis)
        
        current_price = float(data.iloc[-1]['close'])
        
        logger.info(f"Symbol: {symbol}, Timeframe: {KLIN_TYPE_MAP.get(kline_type)}")
        logger.info(f"Current Price: {current_price}")
        
        primary_support = support_resistance_analysis['supports']['primary']
        primary_resistance = support_resistance_analysis['resistances']['primary']
        
        if primary_support:
            logger.info(f"Primary Support: {primary_support['price']:.2f} (Strength: {primary_support['final_score']:.2f})")
        if primary_resistance:
            logger.info(f"Primary Resistance: {primary_resistance['price']:.2f} (Strength: {primary_resistance['final_score']:.2f})")
        
        return {
            "symbol": symbol,
            "timeframe": KLIN_TYPE_MAP.get(kline_type),
            "data_points": len(data),
            "last_update": data.index[-1].isoformat() if hasattr(data.index[-1], 'isoformat') else str(data.index[-1]),
            "technical_analysis": technical_analysis,
            "raw_data_sample": {
                "latest_candle": {
                    "open": float(data.iloc[-1]['open']),
                    "high": float(data.iloc[-1]['high']),
                    "low": float(data.iloc[-1]['low']),
                    "close": float(data.iloc[-1]['close']),
                    "volume": float(data.iloc[-1]['volume'])
                }
            },
            "data_quality": {
                "completeness": len(data) / 80.0,  # 相对于预期80条数据
                "volume_consistency": bool((data['volume'] > 0).all()),
                "price_consistency": bool((data['high'] >= data['low']).all())
            }
        }

    except Exception as e:
        logger.error(f"数据处理异常: {str(e)}", exc_info=True)
        return {"error": f"Processing error: {str(e)}"}

# 向后兼容函数
def calculate_macd(data, fast_period=12, slow_period=26, signal_period=9):
    """向后兼容包装器"""
    return calculate_comprehensive_indicators(data)

def calculate_indicators(data):
    """计算指标的测试函数 - 更新为新版本"""
    data_with_indicators = calculate_comprehensive_indicators(data)
    support_resistance_analysis = find_professional_support_resistance(data_with_indicators)
    
    primary_support = support_resistance_analysis['supports']['primary']
    primary_resistance = support_resistance_analysis['resistances']['primary']
    
    return {
        "macd": data_with_indicators[['macd', 'signal', 'hist']].tail(1).to_dict('records')[0],
        "support": primary_support['price'] if primary_support else data_with_indicators.iloc[-1]['close'] * 0.95,
        "resistance": primary_resistance['price'] if primary_resistance else data_with_indicators.iloc[-1]['close'] * 1.05,
        "support_strength": primary_support['final_score'] if primary_support else 0,
        "resistance_strength": primary_resistance['final_score'] if primary_resistance else 0
    }

def get_order_history_data():
    """获取订单历史数据"""
    return get_trading_statistics()

def analysis_records():
    """获取分析记录"""
    return post_trade_analysis_records()

if __name__ == '__main__':
    print(get_k_line_data(3))