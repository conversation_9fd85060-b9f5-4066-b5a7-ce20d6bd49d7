import pandas as pd
import numpy as np
import logging
from typing import Dict, <PERSON><PERSON>
from datetime import datetime
import warnings
import json

# 忽略警告信息
warnings.filterwarnings("ignore")

# 配置基础日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# 尝试导入Qlib，如果不可用则使用备用方案
try:
    import qlib
    from qlib.data import D
    from qlib.contrib.model import TwoStageLGBModel
    from qlib.utils import init_instance_by_config

    QLIB_AVAILABLE = True
    logger.info("Qlib imported successfully")
except ImportError:
    QLIB_AVAILABLE = False
    logger.warning("Qlib not installed. Using fallback analysis methods.")

from .market_database_manager import get_market_data_by_symbol
# from market_database_manager import get_market_data_by_symbol


class QlibIntegrationAnalyzer:
    """
    Qlib集成分析器
    集成Qlib的数据处理和分析能力，替代原有的K线分析功能
    专注于使用现有模型进行分析，不需要重新训练
    """

    def __init__(self):
        self.is_qlib_initialized = False
        self.performance_metrics = {}
        # 复利策略与资金管理
        self.trading_history = []
        self.consecutive_wins = 0
        self.consecutive_losses = 0
        self.current_win_rate = 0.6  # 初始胜率
        self.compound_multiplier = 1.0  # 复利倍数
        self.last_performance_update = None
        # 智能机会捕获参数
        self.opportunity_sensitivity = 0.8  # 机会敏感度（0.5-1.0，越高越敏感）
        self.entry_precision_mode = True  # 精准入场模式
        self.adaptive_thresholds = True  # 自适应阈值
        self.max_compound_factor = 5.0  # 最大复利倍数

        # 初始化Qlib
        self._init_qlib()

    def _init_qlib(self):
        """初始化Qlib环境"""
        if not QLIB_AVAILABLE:
            logger.warning("Qlib is not available. Using fallback analysis methods.")
            return

        try:
            # 初始化Qlib（使用模拟数据或默认配置）
            # 在实际应用中，您可能需要配置真实的QLib数据源
            logger.info("Qlib is available but using local data analysis")
            self.is_qlib_initialized = True
        except Exception as e:
            logger.error(f"Failed to initialize Qlib: {str(e)}")
            self.is_qlib_initialized = False

    def get_qlib_analysis_data(self, kline_type=5) -> Dict:
        """
        获取Qlib分析数据
        替代原有的K线分析功能，使用现有模型进行分析
        """
        try:
            # 获取原始数据
            raw_data = self._get_raw_data(kline_type, limit=150)
            if not raw_data or len(raw_data) < 30:
                return {
                    "error": f"Insufficient data: only {len(raw_data) if raw_data else 0} records, need at least 30"}

            # 转换数据格式
            data = self._convert_data(raw_data)
            if len(data) < 30:
                return {"error": f"Insufficient converted data: {len(data)} records, need at least 30"}

            # 使用Qlib风格的分析方法
            data = self._calculate_comprehensive_indicators(data)

            # 评估信号质量
            signal_quality = self._evaluate_signal_quality(data)

            # 提取关键信息
            current_price = float(data.iloc[-1]['close'])
            key_levels = self._find_smart_levels(data)
            market_state = self._classify_market_state(data)

            # 使用Qlib风格的特征工程
            qlib_style_features = self._extract_qlib_style_features(data)

            return {
                "symbol": "BTCUSDT",
                "timeframe": self._get_timeframe_name(kline_type),
                "current_price": current_price,
                "signal_quality": signal_quality,
                "key_levels": key_levels,
                "market_state": market_state,
                "qlib_style_features": qlib_style_features,
                "indicators": self._extract_all_indicators(data),
                "last_update": data.index[-1].isoformat() if hasattr(data.index[-1], 'isoformat') else str(
                    data.index[-1])
            }

        except Exception as e:
            logger.error(f"Qlib分析异常: {str(e)}")
            return {"error": f"Analysis error: {str(e)}"}

    def get_multi_timeframe_analysis(self) -> Dict:
        """
        多时间框架综合分析
        """
        try:
            # 主要时间框架
            primary_timeframes = {
                '1H': 5,
                '4H': 7,
                '1D': 8
            }

            # 辅助时间框架
            auxiliary_timeframes = {
                '15m': 3,
                '30m': 4
            }

            analysis_results = {}

            # 分析所有时间框架
            all_timeframes = {**primary_timeframes, **auxiliary_timeframes}
            for tf_name, tf_code in all_timeframes.items():
                result = self.get_qlib_analysis_data(tf_code)
                if 'error' not in result:
                    analysis_results[tf_name] = result

            if not analysis_results:
                return {"error": "No valid timeframe data available"}

            # 跨时间框架机会质量评估
            opportunity_assessment = self._assess_cross_timeframe_opportunities(analysis_results)

            # 添加综合市场评估
            market_overview = self._generate_market_overview(analysis_results)

            return {
                "timeframe_data": analysis_results,
                "opportunity_assessment": opportunity_assessment,
                "market_overview": market_overview,
                "analysis_timestamp": datetime.now().isoformat()
            }

        except Exception as e:
            logger.error(f"多时间框架分析异常: {str(e)}")
            return {"error": f"Multi-timeframe analysis error: {str(e)}"}

    def _generate_market_overview(self, timeframe_data: Dict) -> Dict:
        """生成市场概览"""
        try:
            overview = {
                "primary_trend": "neutral",
                "market_sentiment": "neutral",
                "volatility_level": "normal",
                "overall_signal_quality": "WEAK",
                "consensus_strength": 0.0
            }

            # 收集主要时间框架的数据
            primary_timeframes = ['1D', '4H', '1H']
            primary_signals = []

            for tf in primary_timeframes:
                if tf in timeframe_data:
                    tf_data = timeframe_data[tf]
                    signal_quality = tf_data.get('signal_quality', {})
                    market_state = tf_data.get('market_state', {})

                    primary_signals.append({
                        'timeframe': tf,
                        'signal_quality': signal_quality.get('quality', 'WEAK'),
                        'confidence': signal_quality.get('confidence', 0.3),
                        'trend_state': market_state.get('trend_state', 'unknown'),
                        'volatility_state': market_state.get('volatility_state', 'unknown')
                    })

            if not primary_signals:
                return overview

            # 确定主要趋势
            trend_votes = {'bullish': 0, 'bearish': 0, 'neutral': 0}
            quality_scores = []

            for signal in primary_signals:
                # 基于信号质量和趋势状态投票
                quality = signal['signal_quality']
                trend_state = signal['trend_state']

                # 质量权重
                quality_weight = {'ALPHA': 3, 'BETA': 2, 'GAMMA': 1, 'WEAK': 0.5}.get(quality, 0.5)

                # 趋势投票
                if 'bullish' in trend_state:
                    trend_votes['bullish'] += quality_weight
                elif 'bearish' in trend_state:
                    trend_votes['bearish'] += quality_weight
                else:
                    trend_votes['neutral'] += quality_weight

                quality_scores.append(signal['confidence'] * quality_weight)

            # 确定主要趋势
            main_trend = max(trend_votes, key=trend_votes.get)
            if trend_votes[main_trend] > sum(trend_votes.values()) * 0.6:  # 60%阈值
                overview['primary_trend'] = main_trend

            # 市场情绪
            bullish_signals = sum(1 for s in primary_signals if s['signal_quality'] in ['ALPHA', 'BETA'])
            bearish_signals = sum(1 for s in primary_signals if s['signal_quality'] in ['WEAK'])

            if bullish_signals >= 2:
                overview['market_sentiment'] = 'bullish'
            elif bearish_signals >= 2:
                overview['market_sentiment'] = 'bearish'
            else:
                overview['market_sentiment'] = 'neutral'

            # 波动率水平
            volatility_votes = {'high': 0, 'normal': 0, 'low': 0}
            for signal in primary_signals:
                vol_state = signal['volatility_state']
                quality_weight = {'ALPHA': 1.0, 'BETA': 0.8, 'GAMMA': 0.6, 'WEAK': 0.4}.get(signal['signal_quality'],
                                                                                            0.4)

                if 'high' in vol_state:
                    volatility_votes['high'] += quality_weight
                elif 'low' in vol_state:
                    volatility_votes['low'] += quality_weight
                else:
                    volatility_votes['normal'] += quality_weight

            overview['volatility_level'] = max(volatility_votes, key=volatility_votes.get)

            # 总体信号质量
            if quality_scores:
                avg_quality = sum(quality_scores) / len(quality_scores)
                if avg_quality >= 0.7:
                    overview['overall_signal_quality'] = 'ALPHA'
                elif avg_quality >= 0.5:
                    overview['overall_signal_quality'] = 'BETA'
                elif avg_quality >= 0.3:
                    overview['overall_signal_quality'] = 'GAMMA'
                else:
                    overview['overall_signal_quality'] = 'WEAK'

            # 共识强度
            total_votes = sum(trend_votes.values())
            if total_votes > 0:
                overview['consensus_strength'] = trend_votes[main_trend] / total_votes

            return overview

        except Exception as e:
            logger.error(f"市场概览生成异常: {str(e)}")
            return {
                "primary_trend": "neutral",
                "market_sentiment": "neutral",
                "volatility_level": "normal",
                "overall_signal_quality": "WEAK",
                "consensus_strength": 0.0
            }

    def _assess_cross_timeframe_opportunities(self, timeframe_data: Dict) -> Dict:
        """跨时间框架机会质量评估"""
        try:
            opportunities = []

            # 获取各时间框架的信号质量
            tf_signals = {}
            for tf_name, tf_data in timeframe_data.items():
                signal_quality = tf_data.get('signal_quality', {})
                market_state = tf_data.get('market_state', {})

                tf_signals[tf_name] = {
                    'quality': signal_quality.get('quality', 'WEAK'),
                    'confidence': signal_quality.get('confidence', 0.3),
                    'score': signal_quality.get('score', 0),
                    'trend_state': market_state.get('trend_state', 'unknown'),
                    'volatility_state': market_state.get('volatility_state', 'unknown')
                }

            # 寻找跨时间框架确认的机会
            primary_timeframes = ['1D', '4H', '1H']
            for direction in ['bullish', 'bearish']:
                confirmed_tfs = []
                total_confidence = 0
                total_score = 0

                for tf in primary_timeframes:
                    if tf in tf_signals:
                        # 简化方向判断逻辑
                        if tf_signals[tf]['quality'] in ['ALPHA', 'BETA']:
                            confirmed_tfs.append(tf)
                            total_confidence += tf_signals[tf]['confidence']
                            total_score += tf_signals[tf]['score']

                # 如果至少有2个主要时间框架确认
                if len(confirmed_tfs) >= 2:
                    avg_confidence = total_confidence / len(confirmed_tfs)
                    avg_score = total_score / len(confirmed_tfs)

                    # 确定机会质量
                    if len(confirmed_tfs) == 3 and avg_score >= 70:
                        opportunity_quality = "EXCEPTIONAL"
                    elif len(confirmed_tfs) == 3 and avg_score >= 60:
                        opportunity_quality = "HIGH"
                    elif len(confirmed_tfs) >= 2 and avg_score >= 60:
                        opportunity_quality = "MEDIUM"
                    else:
                        opportunity_quality = "LOW"

                    opportunities.append({
                        'direction': direction,
                        'quality': opportunity_quality,
                        'confirmed_timeframes': confirmed_tfs,
                        'confidence': avg_confidence,
                        'score': avg_score,
                        'timeframe_count': len(confirmed_tfs)
                    })

            # 选择最佳机会
            best_opportunity = None
            if opportunities:
                # 按质量和确认时间框架数量排序
                quality_rank = {"EXCEPTIONAL": 4, "HIGH": 3, "MEDIUM": 2, "LOW": 1}
                opportunities.sort(key=lambda x: (quality_rank.get(x['quality'], 0), x['timeframe_count'], x['score']),
                                   reverse=True)
                best_opportunity = opportunities[0]

            return {
                'best_opportunity': best_opportunity,
                'all_opportunities': opportunities,
                'market_consensus': self._calculate_market_consensus(tf_signals),
                'timeframe_alignment_score': self._calculate_alignment_score(tf_signals)
            }

        except Exception as e:
            logger.error(f"机会评估异常: {str(e)}")
            return {'error': f"Opportunity assessment error: {str(e)}"}

    def _calculate_market_consensus(self, tf_signals: Dict) -> Dict:
        """计算市场共识度"""
        try:
            # 基于信号质量和趋势状态计算共识
            bullish_count = 0
            bearish_count = 0
            total_count = 0

            quality_weights = {'ALPHA': 1.0, 'BETA': 0.8, 'GAMMA': 0.6, 'WEAK': 0.3}

            for tf_name, signal in tf_signals.items():
                quality = signal['quality']
                weight = quality_weights.get(quality, 0.3)

                # 简化的趋势判断
                if quality in ['ALPHA', 'BETA']:
                    bullish_count += weight
                elif quality == 'WEAK':
                    bearish_count += weight * 0.5  # 弱信号权重较低

                total_count += weight

            if total_count > 0:
                bullish_strength = bullish_count / total_count
                bearish_strength = bearish_count / total_count

                if bullish_strength > 0.6:
                    consensus = 'bullish'
                    strength = bullish_strength
                elif bearish_strength > 0.6:
                    consensus = 'bearish'
                    strength = bearish_strength
                else:
                    consensus = 'neutral'
                    strength = 0.5
            else:
                consensus = 'neutral'
                strength = 0.5

            return {'consensus': consensus, 'strength': strength}
        except Exception as e:
            logger.error(f"市场共识计算异常: {str(e)}")
            return {'consensus': 'neutral', 'strength': 0.5}

    def _calculate_alignment_score(self, tf_signals: Dict) -> float:
        """计算时间框架对齐评分"""
        try:
            if not tf_signals:
                return 0.0

            # 主要时间框架权重
            weights = {'1D': 0.4, '4H': 0.3, '1H': 0.2, '30m': 0.05, '15m': 0.05}

            total_score = 0
            total_weight = 0

            quality_scores = {'ALPHA': 1.0, 'BETA': 0.8, 'GAMMA': 0.6, 'WEAK': 0.3}

            for tf_name, signal in tf_signals.items():
                if tf_name in weights:
                    quality_score = quality_scores.get(signal['quality'], 0.3)
                    score = quality_score * weights[tf_name]
                    total_score += score
                    total_weight += weights[tf_name]

            return total_score / total_weight if total_weight > 0 else 0.0
        except Exception as e:
            logger.error(f"对齐评分计算异常: {str(e)}")
            return 0.0

    def _get_timeframe_name(self, kline_type: int) -> str:
        """获取时间框架名称"""
        KLIN_TYPE_MAP = {
            1: "1m", 2: "5m", 3: "15m", 4: "30m", 5: "1H",
            6: "2H", 7: "4H", 8: "1D", 9: "1W", 10: "1M"
        }
        return KLIN_TYPE_MAP.get(kline_type, "Unknown")

    def _get_raw_data(self, kline_type, limit=150):
        """获取原始数据"""
        time_type = self._get_timeframe_name(kline_type)
        if not time_type:
            return []

        return sorted(get_market_data_by_symbol(type=time_type, limit=limit), key=lambda x: x.open_time)

    def _convert_data(self, raw_data):
        """转换数据格式"""
        df = pd.DataFrame([{
            'time': pd.to_datetime(int(k.open_time), unit='ms', utc=True).tz_convert('Asia/Shanghai'),
            'timestamp': k.open_time,
            'open': float(k.open),
            'high': float(k.high),
            'low': float(k.low),
            'close': float(k.close),
            'volume': float(k.volume)
        } for k in raw_data])

        df.set_index('time', inplace=True)
        return df

    def _calculate_comprehensive_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """
        使用Qlib风格的方法计算综合技术指标
        """
        # 1. 移动平均线系统
        data = self._calculate_moving_averages(data)

        # 2. 趋势强度指标
        data = self._calculate_trend_indicators(data)

        # 3. 动量指标
        data = self._calculate_momentum_indicators(data)

        # 4. 成交量指标
        data = self._calculate_volume_indicators(data)

        # 5. 波动率指标
        data = self._calculate_volatility_indicators(data)

        # 6. 价格结构指标
        data = self._calculate_price_structure_indicators(data)

        return data

    def _calculate_moving_averages(self, data: pd.DataFrame) -> pd.DataFrame:
        """移动平均线系统"""
        # 使用Qlib风格的周期设置
        periods = [5, 10, 15, 20, 30, 50, 100]
        for period in periods:
            data[f'sma_{period}'] = data['close'].rolling(window=period).mean()
            data[f'ema_{period}'] = data['close'].ewm(span=period, adjust=False).mean()

        # 计算均线排列关系
        if 'ema_5' in data.columns and 'ema_10' in data.columns and 'ema_20' in data.columns:
            data['ma_alignment'] = (
                                           (data['ema_5'] > data['ema_10']) &
                                           (data['ema_10'] > data['ema_20'])
                                   ).astype(int) - (
                                           (data['ema_5'] < data['ema_10']) &
                                           (data['ema_10'] < data['ema_20'])
                                   ).astype(int)
        else:
            data['ma_alignment'] = 0

        return data

    def _calculate_trend_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """趋势强度指标"""
        data['trend_strength'] = 0.0

        # 基于多个周期的价格变化计算趋势强度
        trend_periods = [3, 5, 8, 13, 21]
        trend_scores = []

        for period in trend_periods:
            if len(data) > period:
                price_change = data['close'].pct_change(period)
                # 标准化趋势分数
                trend_score = np.tanh(price_change * 10)  # 使用tanh函数限制范围
                trend_scores.append(trend_score * (1 / period))  # 权重递减

        if trend_scores:
            data['trend_strength'] = sum(trend_scores)

        return data

    def _calculate_momentum_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """动量指标"""
        # RSI - 多个周期
        for period in [7, 14, 21]:
            data[f'rsi_{period}'] = self._calculate_rsi(data['close'], period)

        # MACD
        data['macd'], data['macd_signal'], data['macd_histogram'] = self._calculate_macd(data['close'])

        # 威廉指标
        data['williams_r'] = self._calculate_williams_r(data, 14)

        # 随机指标
        data['stoch_k'] = self._calculate_stochastic(data, 14)
        data['stoch_d'] = data['stoch_k'].rolling(window=3).mean()

        return data

    def _calculate_volume_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """成交量指标"""
        # 成交量移动平均
        for period in [5, 10, 20]:
            data[f'volume_ma_{period}'] = data['volume'].rolling(window=period).mean()

        # 成交量比率
        data['volume_ratio'] = data['volume'] / data['volume_ma_20']

        # 量价关系
        data['price_volume_trend'] = (data['close'].pct_change() * data['volume']).rolling(window=20).sum()

        return data

    def _calculate_volatility_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """波动率指标"""
        # ATR
        data['atr_14'] = self._calculate_atr(data, 14)

        # 布林带
        bb_window = 20
        bb_std = data['close'].rolling(window=bb_window).std()
        bb_ma = data['close'].rolling(window=bb_window).mean()
        data['bb_upper'] = bb_ma + (bb_std * 2)
        data['bb_lower'] = bb_ma - (bb_std * 2)
        data['bb_width'] = (data['bb_upper'] - data['bb_lower']) / bb_ma
        data['bb_position'] = (data['close'] - data['bb_lower']) / (data['bb_upper'] - data['bb_lower'])

        # 真实波动率
        data['true_range'] = self._calculate_true_range(data)
        data['volatility_ratio'] = data['true_range'] / data['close']

        return data

    def _calculate_price_structure_indicators(self, data: pd.DataFrame) -> pd.DataFrame:
        """价格结构指标"""
        # 支撑阻力位
        data['pivot_point'] = (data['high'] + data['low'] + data['close']) / 3
        data['r1'] = 2 * data['pivot_point'] - data['low']
        data['s1'] = 2 * data['pivot_point'] - data['high']

        # 价格位置
        data['price_position'] = (data['close'] - data['low'].rolling(window=20).min()) / \
                                 (data['high'].rolling(window=20).max() - data['low'].rolling(window=20).min())

        return data

    def _extract_qlib_style_features(self, data: pd.DataFrame) -> Dict:
        """
        提取Qlib风格的特征
        """
        latest = data.iloc[-1]

        features = {}

        # 基础价格特征
        features['close'] = float(latest['close'])
        features['open'] = float(latest['open'])
        features['high'] = float(latest['high'])
        features['low'] = float(latest['low'])
        features['volume'] = float(latest['volume'])

        # 收益率特征
        if len(data) > 1:
            features['return_1'] = float((latest['close'] / data.iloc[-2]['close']) - 1)
        if len(data) > 5:
            features['return_5'] = float((latest['close'] / data.iloc[-5]['close']) - 1)
        if len(data) > 10:
            features['return_10'] = float((latest['close'] / data.iloc[-10]['close']) - 1)

        # 技术指标特征
        for col in ['rsi_14', 'rsi_21', 'macd_histogram', 'volume_ratio', 'bb_position', 'trend_strength']:
            if col in data.columns and pd.notna(latest[col]):
                features[col] = float(latest[col])

        # 归一化特征
        features['normalized_close'] = float((latest['close'] - data['close'].rolling(20).mean().iloc[-1]) /
                                             data['close'].rolling(20).std().iloc[-1])

        return features

    def _extract_all_indicators(self, data: pd.DataFrame) -> Dict:
        """提取所有指标数据"""
        latest = data.iloc[-1]

        indicators = {}

        # 移动平均线
        ma_columns = [col for col in data.columns if col.startswith(('sma_', 'ema_'))]
        for col in ma_columns:
            if pd.notna(latest[col]):
                indicators[col] = float(latest[col])

        # 趋势指标
        trend_columns = ['trend_strength', 'ma_alignment']
        for col in trend_columns:
            if col in data.columns and pd.notna(latest[col]):
                indicators[col] = float(latest[col])

        # 动量指标
        momentum_columns = [col for col in data.columns if col.startswith(('rsi_', 'macd', 'stoch_', 'williams_r'))]
        for col in momentum_columns:
            if pd.notna(latest[col]):
                indicators[col] = float(latest[col])

        # 成交量指标
        volume_columns = [col for col in data.columns if col.startswith(('volume_', 'price_volume_trend'))]
        for col in volume_columns:
            if pd.notna(latest[col]):
                indicators[col] = float(latest[col])

        # 波动率指标
        volatility_columns = [col for col in data.columns if
                              col.startswith(('atr_', 'bb_', 'true_range', 'volatility_ratio'))]
        for col in volatility_columns:
            if pd.notna(latest[col]):
                indicators[col] = float(latest[col])

        # 价格结构指标
        structure_columns = ['pivot_point', 'r1', 's1', 'price_position']
        for col in structure_columns:
            if col in data.columns and pd.notna(latest[col]):
                indicators[col] = float(latest[col])

        return indicators

    def _evaluate_signal_quality(self, data: pd.DataFrame) -> Dict:
        """
        评估信号质量（使用现有模型的方法）
        """
        try:
            latest = data.iloc[-1]

            # 综合评分计算
            total_score = 0
            score_components = {}

            # 1. 趋势强度评分 (0-30分)
            trend_strength = latest.get('trend_strength', 0)
            if trend_strength is not None and pd.notna(trend_strength):
                trend_score = min(30, max(0, abs(trend_strength) * 30))
                total_score += trend_score
                score_components['trend_score'] = round(trend_score, 2)

            # 2. 动量指标评分 (0-25分)
            momentum_score = 0
            rsi_14 = latest.get('rsi_14', 50)
            if rsi_14 is not None and pd.notna(rsi_14):
                if 30 <= rsi_14 <= 70:  # 正常区间
                    momentum_score += 10
                elif rsi_14 < 20 or rsi_14 > 80:  # 极端值
                    momentum_score += 5

            macd_hist = latest.get('macd_histogram', 0)
            if macd_hist is not None and pd.notna(macd_hist):
                if macd_hist > 0:
                    momentum_score += 8
                elif macd_hist < 0:
                    momentum_score += 8

            total_score += min(25, momentum_score)
            score_components['momentum_score'] = round(min(25, momentum_score), 2)

            # 3. 成交量确认评分 (0-20分)
            volume_score = 0
            volume_ratio = latest.get('volume_ratio', 1.0)
            if volume_ratio is not None and pd.notna(volume_ratio):
                if volume_ratio > 1.5:
                    volume_score = 20
                elif volume_ratio > 1.2:
                    volume_score = 15
                elif volume_ratio > 0.8:
                    volume_score = 10

            total_score += volume_score
            score_components['volume_score'] = volume_score

            # 4. 波动率环境评分 (0-15分)
            volatility_score = 0
            bb_width = latest.get('bb_width', 0)
            if bb_width is not None and pd.notna(bb_width):
                if 0.02 <= bb_width <= 0.08:
                    volatility_score = 15
                elif bb_width < 0.02:
                    volatility_score = 10
                elif bb_width > 0.08:
                    volatility_score = 5

            total_score += volatility_score
            score_components['volatility_score'] = volatility_score

            # 5. 均线系统评分 (0-10分)
            ma_alignment = latest.get('ma_alignment', 0)
            ma_score = abs(ma_alignment) * 5
            total_score += min(10, ma_score)
            score_components['ma_score'] = round(min(10, ma_score), 2)

            # 确定信号质量等级
            if total_score >= 80:
                quality = "ALPHA"
                confidence = min(0.95, 0.7 + (total_score - 80) * 0.005)
                estimated_win_rate = min(0.85, 0.75 + (total_score - 80) * 0.002)
            elif total_score >= 60:
                quality = "BETA"
                confidence = min(0.85, 0.55 + (total_score - 60) * 0.01)
                estimated_win_rate = min(0.75, 0.6 + (total_score - 60) * 0.005)
            elif total_score >= 40:
                quality = "GAMMA"
                confidence = min(0.75, 0.4 + (total_score - 40) * 0.01)
                estimated_win_rate = min(0.65, 0.5 + (total_score - 40) * 0.005)
            else:
                quality = "WEAK"
                confidence = max(0.3, 0.2 + total_score * 0.01)
                estimated_win_rate = max(0.4, 0.35 + total_score * 0.005)

            return {
                'quality': quality,
                'score': round(total_score, 2),
                'confidence': round(confidence, 4),
                'estimated_win_rate': round(estimated_win_rate, 4),
                'components': score_components,
                'risk_reward_ratio': round(2.0 + (total_score / 100) * 3.0, 2)  # 基于信号质量的动态风险回报比
            }

        except Exception as e:
            logger.error(f"信号质量评估异常: {str(e)}")
            return {
                'quality': 'WEAK',
                'score': 30,
                'confidence': 0.3,
                'estimated_win_rate': 0.4,
                'risk_reward_ratio': 1.5
            }

    def _find_smart_levels(self, data: pd.DataFrame) -> Dict:
        """智能寻找关键价格水平"""
        try:
            if data.empty or len(data) < 20:
                return {"support": None, "resistance": None, "key_levels": []}

            current_price = float(data.iloc[-1]['close'])

            # 寻找近期高低点
            highs = []
            lows = []

            lookback = min(30, len(data) // 2)
            recent_data = data.tail(lookback)

            for i in range(2, len(recent_data) - 2):
                if (recent_data.iloc[i]['high'] > recent_data.iloc[i - 1]['high'] and
                        recent_data.iloc[i]['high'] > recent_data.iloc[i + 1]['high'] and
                        recent_data.iloc[i]['high'] > recent_data.iloc[i - 2]['high'] and
                        recent_data.iloc[i]['high'] > recent_data.iloc[i + 2]['high']):
                    highs.append(float(recent_data.iloc[i]['high']))

                if (recent_data.iloc[i]['low'] < recent_data.iloc[i - 1]['low'] and
                        recent_data.iloc[i]['low'] < recent_data.iloc[i + 1]['low'] and
                        recent_data.iloc[i]['low'] < recent_data.iloc[i - 2]['low'] and
                        recent_data.iloc[i]['low'] < recent_data.iloc[i + 2]['low']):
                    lows.append(float(recent_data.iloc[i]['low']))

            resistance_levels = [h for h in highs if h > current_price]
            support_levels = [l for l in lows if l < current_price]

            nearest_resistance = min(resistance_levels) if resistance_levels else None
            nearest_support = max(support_levels) if support_levels else None

            return {
                "support": nearest_support,
                "resistance": nearest_resistance,
                "key_levels": sorted(set(highs + lows)),
                "current_price": current_price
            }

        except Exception as e:
            logger.error(f"关键位计算异常: {str(e)}")
            return {"support": None, "resistance": None, "key_levels": [], "current_price": 0}

    def _classify_market_state(self, data: pd.DataFrame) -> Dict:
        """市场状态分类"""
        latest = data.iloc[-1]

        # 基于波动率分类
        if 'atr_14' in data.columns and pd.notna(latest['atr_14']):
            atr_ratio = latest['atr_14'] / data['atr_14'].rolling(window=20).mean().iloc[-1]
            if atr_ratio > 1.5:
                volatility_state = "high_volatility"
            elif atr_ratio < 0.7:
                volatility_state = "low_volatility"
            else:
                volatility_state = "normal_volatility"
        else:
            volatility_state = "unknown"

        # 基于趋势强度分类
        if 'trend_strength' in data.columns and pd.notna(latest['trend_strength']):
            if abs(latest['trend_strength']) > 0.5:
                trend_state = "strong_trend"
            elif abs(latest['trend_strength']) > 0.2:
                trend_state = "moderate_trend"
            else:
                trend_state = "weak_trend"
        else:
            trend_state = "unknown"

        return {
            "volatility_state": volatility_state,
            "trend_state": trend_state,
            "trend_strength_value": float(latest['trend_strength']) if 'trend_strength' in data.columns and pd.notna(
                latest['trend_strength']) else None
        }

    # 辅助计算函数
    def _calculate_rsi(self, prices: pd.Series, period: int = 14) -> pd.Series:
        """计算RSI指标"""
        try:
            delta = prices.diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
            rs = gain / loss
            rsi = 100 - (100 / (1 + rs))
            return rsi.fillna(50)
        except Exception as e:
            logger.error(f"RSI计算异常: {str(e)}")
            return pd.Series([50] * len(prices), index=prices.index)

    def _calculate_macd(self, prices: pd.Series, fast: int = 12, slow: int = 26, signal: int = 9) -> Tuple[
        pd.Series, pd.Series, pd.Series]:
        """计算MACD指标"""
        try:
            ema_fast = prices.ewm(span=fast).mean()
            ema_slow = prices.ewm(span=slow).mean()
            macd = ema_fast - ema_slow
            macd_signal = macd.ewm(span=signal).mean()
            macd_histogram = macd - macd_signal
            return macd.fillna(0), macd_signal.fillna(0), macd_histogram.fillna(0)
        except Exception as e:
            logger.error(f"MACD计算异常: {str(e)}")
            return pd.Series([0] * len(prices), index=prices.index), pd.Series([0] * len(prices),
                                                                               index=prices.index), pd.Series(
                [0] * len(prices), index=prices.index)

    def _calculate_williams_r(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算威廉指标"""
        try:
            high_max = data['high'].rolling(window=period).max()
            low_min = data['low'].rolling(window=period).min()
            wr = -100 * ((high_max - data['close']) / (high_max - low_min))
            return wr.fillna(-50)
        except Exception as e:
            logger.error(f"威廉指标计算异常: {str(e)}")
            return pd.Series([-50] * len(data), index=data.index)

    def _calculate_stochastic(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算随机指标"""
        try:
            low_min = data['low'].rolling(window=period).min()
            high_max = data['high'].rolling(window=period).max()
            k_percent = 100 * ((data['close'] - low_min) / (high_max - low_min))
            return k_percent.fillna(50)
        except Exception as e:
            logger.error(f"随机指标计算异常: {str(e)}")
            return pd.Series([50] * len(data), index=data.index)

    def _calculate_atr(self, data: pd.DataFrame, period: int = 14) -> pd.Series:
        """计算ATR指标"""
        try:
            high_low = data['high'] - data['low']
            high_close = np.abs(data['high'] - data['close'].shift())
            low_close = np.abs(data['low'] - data['close'].shift())

            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            true_range = ranges.max(axis=1)
            atr = true_range.rolling(window=period).mean()
            return atr.fillna(data['close'] * 0.02)
        except Exception as e:
            logger.error(f"ATR计算异常: {str(e)}")
            return pd.Series([data['close'].iloc[-1] * 0.02] * len(data), index=data.index)

    def _calculate_true_range(self, data: pd.DataFrame) -> pd.Series:
        """计算真实波动率"""
        try:
            high_low = data['high'] - data['low']
            high_close = np.abs(data['high'] - data['close'].shift())
            low_close = np.abs(data['low'] - data['close'].shift())

            ranges = pd.concat([high_low, high_close, low_close], axis=1)
            return ranges.max(axis=1).fillna(0)
        except Exception as e:
            logger.error(f"真实波动率计算异常: {str(e)}")
            return pd.Series([0] * len(data), index=data.index)

    def get_comprehensive_analysis(self) -> Dict:
        """
        获取综合分析报告
        包含市场概览、机会评估和详细的时间框架分析
        """
        try:
            # 获取多时间框架分析
            multi_tf_analysis = self.get_multi_timeframe_analysis()
            if 'error' in multi_tf_analysis:
                return multi_tf_analysis

            # 提取关键信息
            timeframe_data = multi_tf_analysis.get('timeframe_data', {})
            opportunity_assessment = multi_tf_analysis.get('opportunity_assessment', {})
            market_overview = multi_tf_analysis.get('market_overview', {})

            # 生成交易建议
            trading_recommendation = self._generate_trading_recommendation(
                market_overview, opportunity_assessment
            )

            # 生成风险管理建议
            risk_management = self._generate_risk_management_advice(
                market_overview, timeframe_data
            )

            return {
                "market_overview": market_overview,
                "opportunity_assessment": opportunity_assessment,
                "trading_recommendation": trading_recommendation,
                "risk_management": risk_management,
                "detailed_timeframe_analysis": self._generate_detailed_timeframe_report(timeframe_data),
                "analysis_timestamp": multi_tf_analysis.get('analysis_timestamp')
            }

        except Exception as e:
            logger.error(f"综合分析异常: {str(e)}")
            return {"error": f"Comprehensive analysis error: {str(e)}"}

    def _generate_trading_recommendation(self, market_overview: Dict, opportunity_assessment: Dict) -> Dict:
        """生成交易建议"""
        try:
            recommendation = {
                "action": "hold",  # hold, buy, sell
                "confidence": 0.0,
                "reasoning": "",
                "position_sizing": "normal"
            }

            primary_trend = market_overview.get('primary_trend', 'neutral')
            market_sentiment = market_overview.get('market_sentiment', 'neutral')
            signal_quality = market_overview.get('overall_signal_quality', 'WEAK')
            consensus_strength = market_overview.get('consensus_strength', 0.0)

            best_opportunity = opportunity_assessment.get('best_opportunity')

            # 基于主要趋势和信号质量生成建议
            if best_opportunity:
                direction = best_opportunity['direction']
                quality = best_opportunity['quality']
                confidence = best_opportunity['confidence']

                if quality in ['EXCEPTIONAL', 'HIGH'] and confidence > 0.7:
                    recommendation['action'] = direction
                    recommendation['confidence'] = min(0.9, confidence * consensus_strength)
                    recommendation['position_sizing'] = 'aggressive'
                    recommendation['reasoning'] = f"跨时间框架确认的高质量{direction}机会"
                elif quality == 'MEDIUM' and confidence > 0.5:
                    recommendation['action'] = direction
                    recommendation['confidence'] = confidence * consensus_strength * 0.8
                    recommendation['position_sizing'] = 'normal'
                    recommendation['reasoning'] = f"中等质量的{direction}机会"
                else:
                    recommendation['action'] = 'hold'
                    recommendation['confidence'] = 0.3
                    recommendation['reasoning'] = "机会质量不足或置信度较低"
            else:
                # 基于市场概览生成建议
                if primary_trend == 'bullish' and signal_quality in ['ALPHA', 'BETA']:
                    recommendation['action'] = 'buy'
                    recommendation['confidence'] = 0.7 * consensus_strength
                    recommendation['reasoning'] = "主要趋势看涨且信号质量较高"
                elif primary_trend == 'bearish' and signal_quality in ['ALPHA', 'BETA']:
                    recommendation['action'] = 'sell'
                    recommendation['confidence'] = 0.7 * consensus_strength
                    recommendation['reasoning'] = "主要趋势看跌且信号质量较高"
                else:
                    recommendation['action'] = 'hold'
                    recommendation['confidence'] = 0.3
                    recommendation['reasoning'] = "缺乏明确的方向性信号"

            return recommendation

        except Exception as e:
            logger.error(f"交易建议生成异常: {str(e)}")
            return {
                "action": "hold",
                "confidence": 0.0,
                "reasoning": f"建议生成异常: {str(e)}",
                "position_sizing": "conservative"
            }

    def _generate_risk_management_advice(self, market_overview: Dict, timeframe_data: Dict) -> Dict:
        """生成风险管理建议"""
        try:
            risk_advice = {
                "volatility_adjustment": "normal",
                "stop_loss_strategy": "standard",
                "position_sizing": "normal",
                "leverage_usage": "moderate"
            }

            volatility_level = market_overview.get('volatility_level', 'normal')
            signal_quality = market_overview.get('overall_signal_quality', 'WEAK')

            # 基于波动率调整
            if volatility_level == 'high':
                risk_advice['volatility_adjustment'] = 'conservative'
                risk_advice['stop_loss_strategy'] = 'tight'
                risk_advice['position_sizing'] = 'reduced'
                risk_advice['leverage_usage'] = 'low'
            elif volatility_level == 'low':
                risk_advice['volatility_adjustment'] = 'aggressive'
                risk_advice['stop_loss_strategy'] = 'normal'
                risk_advice['position_sizing'] = 'increased'
                risk_advice['leverage_usage'] = 'moderate'

            # 基于信号质量调整
            if signal_quality == 'ALPHA':
                risk_advice['position_sizing'] = 'increased' if risk_advice[
                                                                    'position_sizing'] != 'reduced' else 'normal'
            elif signal_quality == 'WEAK':
                risk_advice['position_sizing'] = 'reduced'
                risk_advice['leverage_usage'] = 'low'

            return risk_advice

        except Exception as e:
            logger.error(f"风险管理建议生成异常: {str(e)}")
            return {
                "volatility_adjustment": "conservative",
                "stop_loss_strategy": "tight",
                "position_sizing": "reduced",
                "leverage_usage": "low"
            }

    def _generate_detailed_timeframe_report(self, timeframe_data: Dict) -> Dict:
        """生成详细的时间框架报告"""
        try:
            detailed_report = {}

            for tf_name, tf_data in timeframe_data.items():
                if 'error' in tf_data:
                    detailed_report[tf_name] = {'error': tf_data['error']}
                    continue

                # 提取关键信息
                signal_quality = tf_data.get('signal_quality', {})
                market_state = tf_data.get('market_state', {})
                indicators = tf_data.get('indicators', {})
                key_levels = tf_data.get('key_levels', {})

                detailed_report[tf_name] = {
                    'current_price': tf_data.get('current_price'),
                    'signal_quality': {
                        'level': signal_quality.get('quality', 'WEAK'),
                        'score': signal_quality.get('score', 0),
                        'confidence': signal_quality.get('confidence', 0.0),
                        'risk_reward': signal_quality.get('risk_reward_ratio', 1.0)
                    },
                    'market_state': {
                        'trend': market_state.get('trend_state', 'unknown'),
                        'volatility': market_state.get('volatility_state', 'unknown'),
                        'trend_strength': market_state.get('trend_strength_value', 0.0)
                    },
                    'key_levels': {
                        'support': key_levels.get('support'),
                        'resistance': key_levels.get('resistance'),
                        'pivot': key_levels.get('current_price')
                    },
                    'key_indicators': {
                        'rsi_14': indicators.get('rsi_14'),
                        'macd_histogram': indicators.get('macd_histogram'),
                        'volume_ratio': indicators.get('volume_ratio'),
                        'trend_strength': indicators.get('trend_strength'),
                        'bb_position': indicators.get('bb_position')
                    }
                }

            return detailed_report

        except Exception as e:
            logger.error(f"详细时间框架报告生成异常: {str(e)}")
            return {}


# 创建全局实例
analyzer = QlibIntegrationAnalyzer()


# 兼容性函数
def get_qlib_analysis_data(kline_type=5) -> Dict:
    """获取Qlib分析数据 - 兼容性接口"""
    return analyzer.get_qlib_analysis_data(kline_type)


def get_multi_timeframe_analysis() -> Dict:
    """获取多时间框架分析数据 - 兼容性接口"""
    return analyzer.get_multi_timeframe_analysis()


def get_comprehensive_analysis() -> Dict:
    """获取综合分析报告 - 新增接口"""
    return analyzer.get_comprehensive_analysis()


if __name__ == '__main__':
    # 测试代码
    print("=== Qlib集成分析器测试 ===")

    # 测试1H数据
    # result = get_qlib_analysis_data(5)  # 1H数据
    # TypeError: dump() missing 1 required positional argument: 'fp'
    # print(f"1H 测试结果: {json.dumps(result , indent=4)}")
    # if 'error' not in result:
    #     print(f"\n1H 分析结果:")
    #     print(f"当前价格: {result.get('current_price')}")
    #     print(f"时间框架: {result.get('timeframe')}")
    #     print(f"信号质量: {result.get('signal_quality', {})}")
    #     print(f"市场状态: {result.get('market_state', {})}")
    #     print(f"Qlib风格特征数: {len(result.get('qlib_style_features', {}))}")
    #     print(f"指标数: {len(result.get('indicators', {}))}")
    # else:
    #     print(f"1H 分析错误: {result.get('error')}")
    #
    # # 测试多时间框架分析
    # print("\n=== 多时间框架分析测试 ===")
    # multi_result = get_multi_timeframe_analysis()
    # if 'error' not in multi_result:
    #     timeframe_data = multi_result.get('timeframe_data', {})
    #     print(f"可用时间框架: {list(timeframe_data.keys())}")
    #
    #     for tf, data in timeframe_data.items():
    #         if 'error' not in data:
    #             print(f"\n{tf} 框架:")
    #             print(f"  当前价格: {data.get('current_price')}")
    #             signal_quality = data.get('signal_quality', {})
    #             print(f"  信号质量: {signal_quality.get('quality', 'N/A')} (评分: {signal_quality.get('score', 0)})")
    #             market_state = data.get('market_state', {})
    #             print(f"  市场状态: {market_state}")
    # else:
    #     print(f"多时间框架分析错误: {multi_result.get('error')}")
    #
    # # 测试综合分析
    # print("\n=== 综合分析测试 ===")
    comprehensive_result = get_comprehensive_analysis()
    print(f" comprehensive_result 测试结果: {json.dumps(comprehensive_result, indent=4 , ensure_ascii=False)}")
    # if 'error' not in comprehensive_result:
    #     market_overview = comprehensive_result.get('market_overview', {})
    #     print(f"市场概览:")
    #     print(f"  主要趋势: {market_overview.get('primary_trend', 'N/A')}")
    #     print(f"  市场情绪: {market_overview.get('market_sentiment', 'N/A')}")
    #     print(f"  波动率水平: {market_overview.get('volatility_level', 'N/A')}")
    #     print(f"  总体信号质量: {market_overview.get('overall_signal_quality', 'N/A')}")
    #
    #     recommendation = comprehensive_result.get('trading_recommendation', {})
    #     print(f"\n交易建议:")
    #     print(f"  动作: {recommendation.get('action', 'N/A')}")
    #     print(f"  置信度: {recommendation.get('confidence', 0.0):.2f}")
    #     print(f"  理由: {recommendation.get('reasoning', 'N/A')}")
    # else:
    #     print(f"综合分析错误: {comprehensive_result.get('error')}")
