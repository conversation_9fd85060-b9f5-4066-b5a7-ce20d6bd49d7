from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker, scoped_session
from contextlib import contextmanager
import os
import json
import yaml
from typing import Dict, Any, Optional, List
from datetime import datetime
from .trading_models import Base, Strategy, Signal, Trade, Position, AccountBalance
class DatabaseManager:
    def __init__(self, config_path: str = None):
        """初始化数据库管理器
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认路径
        """
        self.config = self._load_config(config_path)
        self.engine = self._create_engine()
        self.Session = scoped_session(sessionmaker(
            bind=self.engine,
            expire_on_commit=False  # 防止提交后对象过期
        ))
        
    def _load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """加载配置文件
        
        Args:
            config_path: 配置文件路径，如果为None则使用默认路径
            
        Returns:
            Dict[str, Any]: 配置信息
        """
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), '../config/trader_config.yaml')
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
                print(config)
            return config
        except Exception as e:
            raise Exception(f"加载配置文件失败: {str(e)}")
    
    def _create_engine(self):
        """创建数据库引擎"""
        db_config = self.config['database']
        db_url = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['name']}"
        return create_engine(db_url)
    
    def init_db(self):
        """初始化数据库表"""
        Base.metadata.create_all(self.engine)
    
    @contextmanager
    def session_scope(self):
        """提供数据库会话的上下文管理器"""
        session = self.Session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
    
    def save_strategy(self, name: str, strategy_type: str, description: str, parameters: Dict[str, Any]) -> Strategy:
        """保存策略"""
        with self.session_scope() as session:
            strategy = Strategy(
                name=name,
                type=strategy_type,
                description=description,
                parameters=json.dumps(parameters)
            )
            session.add(strategy)
            session.flush()
            session.refresh(strategy)
            return strategy
    
    def save_signal(self, strategy_id: int, trading_pair: str, signal_type: str, 
                   signal_strength: float, price: float, metadata: Dict[str, Any] = None) -> Signal:
        """保存交易信号"""
        with self.session_scope() as session:
            signal = Signal(
                strategy_id=strategy_id,
                trading_pair=trading_pair,
                signal_type=signal_type,
                signal_strength=signal_strength,
                price=price,
                meta_data=json.dumps(metadata) if metadata else None
            )
            session.add(signal)
            session.flush()
            session.refresh(signal)
            return signal
    
    def save_trade(self, strategy_id: int, signal_id: Optional[int], trading_pair: str,
                  order_id: str, side: str, order_type: str, price: float, size: float,
                  leverage: int, status: str, metadata: Dict[str, Any] = None) -> Trade:
        """保存交易记录"""
        with self.session_scope() as session:
            trade = Trade(
                strategy_id=strategy_id,
                signal_id=signal_id,
                trading_pair=trading_pair,
                order_id=order_id,
                side=side,
                order_type=order_type,
                price=price,
                size=size,
                leverage=leverage,
                status=status,
                open_time=datetime.now(),
                meta_data=json.dumps(metadata) if metadata else None
            )
            session.add(trade)
            session.flush()
            session.refresh(trade)
            return trade
    
    def update_trade(self, trade_id: int, status: str = None, close_time: datetime = None,
                    pnl: float = None, fee: float = None, metadata: Dict[str, Any] = None) -> Trade:
        """更新交易记录"""
        with self.session_scope() as session:
            trade = session.query(Trade).filter_by(id=trade_id).first()
            if trade:
                if status:
                    trade.status = status
                if close_time:
                    trade.close_time = close_time
                if pnl is not None:
                    trade.pnl = pnl
                if fee is not None:
                    trade.fee = fee
                if metadata:
                    trade.metadata = json.dumps(metadata)
                session.flush()
            return trade
    
    def save_position(self, trading_pair: str, side: str, size: float, entry_price: float,
                     current_price: float, leverage: int, margin: float, unrealized_pnl: float,
                     metadata: Dict[str, Any] = None) -> Position:
        """保存持仓记录"""
        with self.session_scope() as session:
            position = Position(
                trading_pair=trading_pair,
                side=side,
                size=size,
                entry_price=entry_price,
                current_price=current_price,
                leverage=leverage,
                margin=margin,
                unrealized_pnl=unrealized_pnl,
                meta_data=json.dumps(metadata) if metadata else None
            )
            session.add(position)
            session.flush()
            session.refresh(position)
            return position
    
    def save_account_balance(self, trading_pair: str, balance: float, available: float,
                           margin: float, unrealized_pnl: float, metadata: Dict[str, Any] = None) -> AccountBalance:
        """保存账户余额记录"""
        with self.session_scope() as session:
            account_balance = AccountBalance(
                trading_pair=trading_pair,
                balance=balance,
                available=available,
                margin=margin,
                unrealized_pnl=unrealized_pnl,
                meta_data=json.dumps(metadata) if metadata else None
            )
            session.add(account_balance)
            session.flush()
            session.refresh(account_balance)
            return account_balance
    
    def get_strategy_performance(self, strategy_id: int) -> Dict[str, Any]:
        """获取策略表现统计"""
        with self.session_scope() as session:
            trades = session.query(Trade).filter_by(strategy_id=strategy_id).all()
            
            total_trades = len(trades)
            winning_trades = len([t for t in trades if t.pnl and t.pnl > 0])
            total_pnl = sum(t.pnl or 0 for t in trades)
            total_fee = sum(t.fee or 0 for t in trades)
            
            return {
                'total_trades': total_trades,
                'winning_trades': winning_trades,
                'win_rate': winning_trades / total_trades if total_trades > 0 else 0,
                'total_pnl': total_pnl,
                'total_fee': total_fee,
                'net_pnl': total_pnl - total_fee
            }
    
    def get_strategy_signals(self, strategy_id: int, limit: int = 100) -> list:
        """获取策略信号历史"""
        with self.session_scope() as session:
            signals = session.query(Signal)\
                .filter_by(strategy_id=strategy_id)\
                .order_by(Signal.timestamp.desc())\
                .limit(limit)\
                .all()
            return signals
    
    def get_historical_prices(self, trading_pair: str, start_time: datetime) -> List[Dict[str, Any]]:
        """获取历史价格数据
        
        Args:
            trading_pair: 交易对
            start_time: 开始时间
            
        Returns:
            List[Dict[str, Any]]: 历史价格数据列表
        """
        try:
            # 从数据库中查询历史价格数据
            query = """
                SELECT price, timestamp
                FROM price_history
                WHERE trading_pair = :trading_pair
                AND timestamp >= :start_time
                ORDER BY timestamp ASC
            """
            result = self.Session.execute(
                query,
                {
                    "trading_pair": trading_pair,
                    "start_time": start_time
                }
            )
            
            # 转换结果为字典列表
            prices = []
            for row in result:
                prices.append({
                    "price": float(row.price),
                    "timestamp": row.timestamp
                })
                
            return prices
            
        except Exception as e:
            print(f"获取历史价格数据失败: {str(e)}")
            return []

if __name__ == '__main__':
    db_manager = DatabaseManager()
    db_manager.init_db()
    
    # 创建测试策略
    # print("创建测试策略")
    # strategy = db_manager.save_strategy(
    #     name='测试策略',
    #     strategy_type='trend_following',
    #     description='这是一个测试策略',
    #     parameters={'param1': 'value1', 'param2': 'value2'}
    # )
    # print(f"创建策略成功: {strategy.id}")
    #
    # # 创建测试信号
    # signal = db_manager.save_signal(
    #     strategy_id=strategy.id,
    #     trading_pair='BTCUSDT',
    #     signal_type='buy',
    #     signal_strength=0.8,
    #     price=10000,
    #     metadata={'test': 'signal data'}
    # )
    # print(f"创建信号成功: {signal.id}")
    #
    # # 测试保存交易记录
    # trade = db_manager.save_trade(
    #     strategy_id=strategy.id,
    #     signal_id=signal.id,
    #     trading_pair='BTCUSDT',
    #     order_id='1234567890',
    #     side='buy',
    #     order_type='limit',
    #     price=10000,
    #     size=0.1,
    #     leverage=125,
    #     status='filled',
    #     metadata={'test': 'data'}
    # )
    # print(f"保存交易记录成功: {trade.id}")
    
    # 测试保存持仓记录
    position = db_manager.save_position(
        trading_pair='BTCUSDT',
        side='long',
        size=0.1,
        entry_price=10000,
        current_price=10100,
        leverage=125,
        margin=1000,
        unrealized_pnl=100,
        metadata={'test': 'data'}
    )
    print(f"保存持仓记录成功: {position.id}")
    
    # 测试保存账户余额
    balance = db_manager.save_account_balance(
        trading_pair='BTCUSDT',
        balance=10000,
        available=9000,
        margin=1000,
        unrealized_pnl=100,
        metadata={'test': 'data'}
    )
    print(f"保存账户余额成功: {balance.id}")
    
    # # 测试获取策略表现
    # performance = db_manager.get_strategy_performance(strategy.id)
    # print(f"策略表现: {performance}")
    #
    # # 测试获取策略信号
    # signals = db_manager.get_strategy_signals(strategy.id)
    # print(f"策略信号数量: {len(signals)}")