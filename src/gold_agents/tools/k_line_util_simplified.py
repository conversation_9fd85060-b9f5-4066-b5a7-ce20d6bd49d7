import pandas as pd
import numpy as np
import logging
from typing import Dict, Union
from .market_database_manager import get_market_data_by_symbol, get_trading_statistics, post_trade_analysis_records,event_data

# 配置基础日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

KLIN_TYPE_MAP = {
    1: "1m",
    2: "5m",
    3: "15m",
    4: "30m",
    5: "1H",
    6: "2H",
    7: "4H",
    8: "1D",
    9: "1W",
    10: "1M"
}

symbol = "BTCUSDT"

def get_cau_line_data(kline_type=3):
    """获取K线原始数据"""
    time_type = KLIN_TYPE_MAP.get(kline_type)
    return sorted(get_market_data_by_symbol(type=time_type, limit=80), key=lambda x: x.open_time)

def convert_data(raw_data):
    """转换原始数据为DataFrame"""
    df = pd.DataFrame([{
        'time': pd.to_datetime(int(k.open_time), unit='ms', utc=True).tz_convert('Asia/Shanghai'),
        'timestamp': k.open_time,
        'open': float(k.open),
        'high': float(k.high),
        'low': float(k.low),
        'close': float(k.close),
        'volume': float(k.volume),
        'quote_asset_volume': float(k.quote_asset_volume)
    } for k in raw_data])

    df.set_index('time', inplace=True)
    return df

def calculate_essential_indicators(data, timeframe="1H"):
    """
    计算核心技术指标 - 优化版
    根据时间框架动态调整参数，增强异动检测能力
    """
    if not isinstance(data, pd.DataFrame):
        raise ValueError("Input data must be a pandas DataFrame")

    required_columns = ['open', 'high', 'low', 'close', 'volume']
    missing_cols = [col for col in required_columns if col not in data.columns]
    if missing_cols:
        raise KeyError(f"Missing required columns: {missing_cols}")

    # 根据时间框架动态调整参数
    if timeframe in ["15m", "30m"]:
        # 短周期参数 - 更敏感
        sma_period = 10
        ema_fast = 8
        ema_slow = 21
        rsi_period = 9
        bb_period = 15
        atr_period = 10
        volume_period = 10
    elif timeframe == "1H":
        # 1小时参数 - 平衡
        sma_period = 20
        ema_fast = 12
        ema_slow = 26
        rsi_period = 14
        bb_period = 20
        atr_period = 14
        volume_period = 20
    else:
        # 长周期参数 - 更稳定
        sma_period = 30
        ema_fast = 12
        ema_slow = 26
        rsi_period = 21
        bb_period = 25
        atr_period = 20
        volume_period = 30

    # 1. 多重移动平均线系统
    data['sma_short'] = data['close'].rolling(window=sma_period//2).mean()
    data['sma_20'] = data['close'].rolling(window=sma_period).mean()
    data['sma_50'] = data['close'].rolling(window=sma_period*2).mean()
    data['ema_12'] = data['close'].ewm(span=ema_fast, adjust=False).mean()
    data['ema_26'] = data['close'].ewm(span=ema_slow, adjust=False).mean()

    # 2. 增强MACD指标
    data['macd'] = data['ema_12'] - data['ema_26']
    data['signal'] = data['macd'].ewm(span=9, adjust=False).mean()
    data['hist'] = data['macd'] - data['signal']
    
    # MACD背离检测
    data['macd_momentum'] = data['macd'].diff()
    data['price_momentum'] = data['close'].diff()
    
    # MACD信号增强
    data['macd_bullish'] = (data['macd'] > data['signal']) & (data['macd'].shift(1) <= data['signal'].shift(1))
    data['macd_bearish'] = (data['macd'] < data['signal']) & (data['macd'].shift(1) >= data['signal'].shift(1))
    data['macd_divergence'] = ((data['macd_momentum'] > 0) & (data['price_momentum'] < 0)) | \
                             ((data['macd_momentum'] < 0) & (data['price_momentum'] > 0))

    # 3. 动态RSI指标
    def calculate_rsi(prices, period=rsi_period):
        delta = prices.diff()
        gain = (delta.where(delta > 0, 0)).rolling(window=period).mean()
        loss = (-delta.where(delta < 0, 0)).rolling(window=period).mean()
        rs = gain / loss.replace(0, np.finfo(float).eps)
        rsi = 100 - (100 / (1 + rs))
        return rsi.fillna(50)

    data['rsi'] = calculate_rsi(data['close'])
    data['rsi_ma'] = data['rsi'].rolling(window=5).mean()
    
    # 动态RSI阈值
    rsi_std = data['rsi'].rolling(window=20).std()
    data['rsi_upper'] = 70 + rsi_std * 0.5
    data['rsi_lower'] = 30 - rsi_std * 0.5
    
    data['rsi_overbought'] = data['rsi'] > data['rsi_upper']
    data['rsi_oversold'] = data['rsi'] < data['rsi_lower']
    data['rsi_divergence'] = abs(data['rsi'] - data['rsi_ma']) > rsi_std

    # 4. 增强布林带
    data['bb_middle'] = data['close'].rolling(window=bb_period).mean()
    bb_std = data['close'].rolling(window=bb_period).std()
    data['bb_upper'] = data['bb_middle'] + (bb_std * 2)
    data['bb_lower'] = data['bb_middle'] - (bb_std * 2)
    data['bb_width'] = (data['bb_upper'] - data['bb_lower']) / data['bb_middle']
    data['bb_squeeze'] = data['bb_width'] < data['bb_width'].rolling(window=20).quantile(0.2)
    data['bb_expansion'] = data['bb_width'] > data['bb_width'].rolling(window=20).quantile(0.8)

    # 5. 增强成交量分析
    data['volume_ma'] = data['volume'].rolling(window=volume_period).mean()
    data['volume_std'] = data['volume'].rolling(window=volume_period).std()
    data['volume_spike'] = data['volume'] > (data['volume_ma'] + data['volume_std'] * 2)
    data['volume_dry'] = data['volume'] < (data['volume_ma'] - data['volume_std'])
    
    # 成交量价格确认
    data['volume_price_confirm'] = ((data['close'] > data['close'].shift(1)) & data['volume_spike']) | \
                                  ((data['close'] < data['close'].shift(1)) & data['volume_spike'])

    # 6. 增强ATR波动率
    tr1 = data['high'] - data['low']
    tr2 = abs(data['high'] - data['close'].shift(1))
    tr3 = abs(data['low'] - data['close'].shift(1))
    tr = pd.concat([tr1, tr2, tr3], axis=1).max(axis=1)
    data['atr'] = tr.rolling(window=atr_period).mean().fillna(0)
    data['atr_pct'] = (data['atr'] / data['close']) * 100
    
    # 异动检测
    atr_ma = data['atr'].rolling(window=20).mean()
    data['volatility_spike'] = data['atr'] > atr_ma * 1.5
    data['volatility_compression'] = data['atr'] < atr_ma * 0.7

    # 7. 趋势强度分析
    data['trend_up'] = data['close'] > data['sma_20']
    data['trend_strong'] = (data['close'] > data['ema_12']) & (data['ema_12'] > data['ema_26']) & \
                          (data['sma_20'] > data['sma_50'])
    data['trend_weak'] = abs(data['ema_12'] - data['ema_26']) < data['atr'] * 0.5

    # 8. 异动预警系统
    data['anomaly_score'] = 0
    data.loc[data['volume_spike'], 'anomaly_score'] += 2
    data.loc[data['volatility_spike'], 'anomaly_score'] += 2
    data.loc[data['bb_expansion'], 'anomaly_score'] += 1
    data.loc[data['macd_divergence'], 'anomaly_score'] += 1
    data.loc[data['rsi_divergence'], 'anomaly_score'] += 1
    
    # 异动等级
    data['anomaly_level'] = 'normal'
    data.loc[data['anomaly_score'] >= 3, 'anomaly_level'] = 'moderate'
    data.loc[data['anomaly_score'] >= 5, 'anomaly_level'] = 'high'
    data.loc[data['anomaly_score'] >= 7, 'anomaly_level'] = 'extreme'

    return data

def find_key_levels(data, lookback=20):
    """
    寻找关键支撑阻力位 - 简化版
    只关注最重要的价格水平
    """
    try:
        if data.empty or len(data) < 10:
            return {
                'current_price': 0,
                'support': None,
                'resistance': None,
                'key_levels': []
            }

        current_price = float(data.iloc[-1]['close'])
        recent_data = data.tail(lookback)

        # 寻找近期高低点
        highs = []
        lows = []

        for i in range(2, len(recent_data) - 2):
            # 局部高点
            if (recent_data.iloc[i]['high'] > recent_data.iloc[i-1]['high'] and
                    recent_data.iloc[i]['high'] > recent_data.iloc[i+1]['high']):
                highs.append(recent_data.iloc[i]['high'])

            # 局部低点
            if (recent_data.iloc[i]['low'] < recent_data.iloc[i-1]['low'] and
                    recent_data.iloc[i]['low'] < recent_data.iloc[i+1]['low']):
                lows.append(recent_data.iloc[i]['low'])

        # 找到最近的支撑和阻力
        resistance_levels = [h for h in highs if h > current_price]
        support_levels = [l for l in lows if l < current_price]

        nearest_resistance = min(resistance_levels) if resistance_levels else None
        nearest_support = max(support_levels) if support_levels else None

        return {
            'current_price': current_price,
            'support': nearest_support,
            'resistance': nearest_resistance,
            'key_levels': sorted(set(highs + lows))
        }

    except Exception as e:
        logger.error(f"关键位计算异常: {str(e)}")
        return {
            'current_price': float(data.iloc[-1]['close']) if not data.empty else 0,
            'support': None,
            'resistance': None,
            'key_levels': []
        }

def get_market_signals(data):
    """
    生成市场信号 - 简化版
    只提供最直接的交易信号
    """
    try:
        if data.empty:
            return {}

        latest = data.iloc[-1]
        prev = data.iloc[-2] if len(data) > 1 else latest

        # 趋势信号
        trend_signal = "neutral"
        if latest['trend_strong']:
            trend_signal = "bullish"
        elif latest['close'] < latest['sma_20'] and latest['ema_12'] < latest['ema_26']:
            trend_signal = "bearish"

        # MACD信号
        macd_signal = "neutral"
        if latest['macd_bullish']:
            macd_signal = "bullish"
        elif latest['macd_bearish']:
            macd_signal = "bearish"

        # RSI信号
        rsi_signal = "neutral"
        if latest['rsi_oversold']:
            rsi_signal = "bullish"
        elif latest['rsi_overbought']:
            rsi_signal = "bearish"

        # 成交量确认
        volume_confirmation = latest['volume_spike']

        # 综合信号强度
        bullish_signals = sum([
            trend_signal == "bullish",
            macd_signal == "bullish",
            rsi_signal == "bullish"
        ])

        bearish_signals = sum([
            trend_signal == "bearish",
            macd_signal == "bearish",
            rsi_signal == "bearish"
        ])

        # 信号质量评估
        signal_quality = "GAMMA"  # 默认
        if bullish_signals >= 2 or bearish_signals >= 2:
            signal_quality = "BETA"
        if (bullish_signals >= 2 or bearish_signals >= 2) and volume_confirmation:
            signal_quality = "ALPHA"

        return {
            'trend_signal': trend_signal,
            'macd_signal': macd_signal,
            'rsi_signal': rsi_signal,
            'volume_confirmation': volume_confirmation,
            'signal_quality': signal_quality,
            'bullish_signals': bullish_signals,
            'bearish_signals': bearish_signals,
            'current_rsi': float(latest['rsi']),
            'current_macd': float(latest['macd']),
            'atr_value': float(latest['atr'])
        }

    except Exception as e:
        logger.error(f"信号生成异常: {str(e)}")
        return {}

def get_k_line_data(kline_type=3) -> Dict[str, Union[float, str, Dict]]:
    """
    获取并处理K线数据 - 简化版
    只提供核心技术分析数据
    """
    try:
        # 验证输入参数
        if kline_type not in KLIN_TYPE_MAP:
            logger.error(f"不支持的K线类型: {kline_type}")
            return {"error": f"Unsupported kline_type: {kline_type}"}

        # 获取原始数据
        raw_data = get_cau_line_data(kline_type)

        if not raw_data or len(raw_data) < 10:
            logger.warning(f"数据不足，获取到 {len(raw_data) if raw_data else 0} 条记录")
            return {"error": "Insufficient data"}

        # 转换数据
        data = convert_data(raw_data)

        if data.empty:
            logger.warning("转换后的数据集为空")
            return {"error": "Empty processed data"}

        # 计算核心指标
        data = calculate_essential_indicators(data)

        # 计算关键位
        key_levels = find_key_levels(data)

        # 生成市场信号
        market_signals = get_market_signals(data)

        current_price = float(data.iloc[-1]['close'])

        logger.info(f"Symbol: {symbol}, Timeframe: {KLIN_TYPE_MAP.get(kline_type)}")
        logger.info(f"Current Price: {current_price}")

        return {
            "symbol": symbol,
            "timeframe": KLIN_TYPE_MAP.get(kline_type),
            "data_points": len(data),
            "last_update": data.index[-1].isoformat() if hasattr(data.index[-1], 'isoformat') else str(data.index[-1]),
            "current_price": current_price,
            "key_levels": key_levels,
            "market_signals": market_signals,
            "latest_candle": {
                "open": float(data.iloc[-1]['open']),
                "high": float(data.iloc[-1]['high']),
                "low": float(data.iloc[-1]['low']),
                "close": float(data.iloc[-1]['close']),
                "volume": float(data.iloc[-1]['volume'])
            },
            "technical_indicators": {
                "sma_20": float(data.iloc[-1]['sma_20']),
                "ema_12": float(data.iloc[-1]['ema_12']),
                "ema_26": float(data.iloc[-1]['ema_26']),
                "macd": float(data.iloc[-1]['macd']),
                "signal": float(data.iloc[-1]['signal']),
                "rsi": float(data.iloc[-1]['rsi']),
                "bb_upper": float(data.iloc[-1]['bb_upper']),
                "bb_lower": float(data.iloc[-1]['bb_lower']),
                "atr": float(data.iloc[-1]['atr']),
                "volume_ma": float(data.iloc[-1]['volume_ma'])
            }
        }

    except Exception as e:
        logger.error(f"数据处理异常: {str(e)}", exc_info=True)
        return {"error": f"Processing error: {str(e)}"}

def get_order_history_data():
    """获取订单历史数据"""
    return get_trading_statistics(days=7)

def analysis_records():
    """获取分析记录"""
    return post_trade_analysis_records()

def last_event_data():
    """获取分析记录"""
    return event_data()

def get_multi_timeframe_analysis():
    """
    获取多时间框架综合分析
    主要用于1H以上周期交易决策，15min/30min仅作参考和异动检测
    """
    try:
        # 定义时间框架：15min, 30min, 1H, 4H, 1D
        timeframes = {
            '15m': 3,   # 仅用于异动检测和精确时机
            '30m': 4,   # 过渡分析，减少噪声
            '1H': 5,    # 主要执行框架
            '4H': 7,    # 中期趋势
            '1D': 8     # 主趋势
        }
        
        analysis_results = {}
        
        for tf_name, tf_code in timeframes.items():
            result = get_k_line_data(tf_code)
            if 'error' not in result:
                analysis_results[tf_name] = result
        
        if not analysis_results:
            return {"error": "No valid timeframe data available"}
        
        # 综合分析
        confluence_analysis = {
            "primary_trend": analyze_primary_trend(analysis_results),
            "anomaly_alerts": analyze_anomaly_confluence(analysis_results),
            "entry_timing": analyze_entry_timing(analysis_results),
            "risk_assessment": analyze_multi_tf_risk(analysis_results)
        }
        
        return {
            "timeframe_data": analysis_results,
            "confluence_analysis": confluence_analysis,
            "trading_recommendation": generate_trading_recommendation(confluence_analysis)
        }
        
    except Exception as e:
        logger.error(f"多时间框架分析异常: {str(e)}")
        return {"error": f"Multi-timeframe analysis error: {str(e)}"}


def analyze_primary_trend(tf_data):
    """分析主要趋势汇合"""
    try:
        trend_scores = {}
        
        # 权重分配：1D最高，4H次之，1H执行
        weights = {'1D': 0.5, '4H': 0.3, '1H': 0.2}
        
        total_score = 0
        for tf, weight in weights.items():
            if tf in tf_data:
                signals = tf_data[tf]['market_signals']
                trend_strength = signals.get('trend_strength', 0)
                total_score += trend_strength * weight
                trend_scores[tf] = trend_strength
        
        # 趋势一致性检查
        consistency = len([s for s in trend_scores.values() if abs(s) >= 2])
        
        return {
            "weighted_trend_score": total_score,
            "individual_scores": trend_scores,
            "trend_consistency": consistency,
            "primary_direction": "bullish" if total_score > 1 else "bearish" if total_score < -1 else "neutral"
        }
        
    except Exception as e:
        return {"error": str(e)}


def analyze_anomaly_confluence(tf_data):
    """分析异动汇合情况"""
    try:
        anomaly_alerts = {}
        future_risks = {}
        
        # 检查各时间框架异动情况
        for tf in ['15m', '30m', '1H', '4H']:
            if tf in tf_data:
                signals = tf_data[tf]['market_signals']
                anomaly_alerts[tf] = signals.get('anomaly_alert', 'normal')
                future_risks[tf] = signals.get('future_anomaly_risk', 0)
        
        # 异动风险评估
        max_risk = max(future_risks.values()) if future_risks else 0
        risk_consensus = sum(1 for r in future_risks.values() if r >= 2)
        
        return {
            "current_anomalies": anomaly_alerts,
            "future_risks": future_risks,
            "max_future_risk": max_risk,
            "risk_consensus_count": risk_consensus,
            "anomaly_recommendation": "high_alert" if max_risk >= 3 else "moderate_alert" if max_risk >= 2 else "normal"
        }
        
    except Exception as e:
        return {"error": str(e)}


def analyze_entry_timing(tf_data):
    """分析入场时机"""
    try:
        timing_signals = {}
        
        # 15min和30min用于精确时机
        if '15m' in tf_data and '30m' in tf_data:
            signals_15m = tf_data['15m']['market_signals']
            signals_30m = tf_data['30m']['market_signals']
            
            # 短周期确认
            short_tf_alignment = (
                signals_15m.get('primary_direction') == signals_30m.get('primary_direction')
                and signals_15m.get('primary_direction') != 'neutral'
            )
            
            timing_signals = {
                "15m_direction": signals_15m.get('primary_direction', 'neutral'),
                "30m_direction": signals_30m.get('primary_direction', 'neutral'),
                "short_tf_aligned": short_tf_alignment,
                "precision_entry_ready": short_tf_alignment and signals_15m.get('confidence_score', 0) >= 4
            }
        
        return timing_signals
        
    except Exception as e:
        return {"error": str(e)}


def analyze_multi_tf_risk(tf_data):
    """多时间框架风险评估"""
    try:
        risk_factors = {}
        
        # 收集各时间框架风险指标
        for tf in ['1H', '4H', '1D']:
            if tf in tf_data:
                signals = tf_data[tf]['market_signals']
                anomaly_detection = tf_data[tf].get('anomaly_detection', {})
                risk_factors[tf] = {
                    "atr_pct": signals.get('atr_pct', 0),
                    "volatility_spike": anomaly_detection.get('volatility_spike', False),
                    "divergence": signals.get('divergence_detected', False)
                }
        
        # 综合风险评估
        avg_volatility = sum(rf.get('atr_pct', 0) for rf in risk_factors.values()) / len(risk_factors) if risk_factors else 0
        volatility_spikes = sum(1 for rf in risk_factors.values() if rf.get('volatility_spike', False))
        divergence_count = sum(1 for rf in risk_factors.values() if rf.get('divergence', False))
        
        return {
            "average_volatility": avg_volatility,
            "volatility_spike_count": volatility_spikes,
            "divergence_count": divergence_count,
            "risk_level": "high" if avg_volatility > 3 or volatility_spikes >= 2 else "medium" if avg_volatility > 2 else "low"
        }
        
    except Exception as e:
        return {"error": str(e)}


def generate_trading_recommendation(confluence_analysis):
    """生成交易建议"""
    try:
        trend = confluence_analysis.get('primary_trend', {})
        anomaly = confluence_analysis.get('anomaly_alerts', {})
        timing = confluence_analysis.get('entry_timing', {})
        risk = confluence_analysis.get('risk_assessment', {})
        
        # 基于汇合分析生成建议
        recommendation = {
            "action": "wait",
            "confidence": "low",
            "timeframe_focus": "1H",
            "risk_level": risk.get('risk_level', 'medium')
        }
        
        # 趋势强度检查
        trend_score = trend.get('weighted_trend_score', 0)
        trend_consistency = trend.get('trend_consistency', 0)
        
        if abs(trend_score) >= 2 and trend_consistency >= 2:
            recommendation["action"] = "long" if trend_score > 0 else "short"
            recommendation["confidence"] = "high" if trend_consistency >= 3 else "medium"
        
        # 异动风险调整
        if anomaly.get('max_future_risk', 0) >= 3:
            recommendation["action"] = "wait"
            recommendation["confidence"] = "low"
            recommendation["reason"] = "High anomaly risk detected"
        
        # 精确时机确认
        if timing.get('precision_entry_ready', False):
            recommendation["timeframe_focus"] = "15m_precision"
        
        return recommendation
        
    except Exception as e:
        return {"error": str(e)}


if __name__ == '__main__':
    # 测试代码
    result = get_k_line_data(5)  # 1H数据
    print(result)
    
    # 测试多时间框架分析
    multi_tf_result = get_multi_timeframe_analysis()
    print("Multi-timeframe analysis:", multi_tf_result)
