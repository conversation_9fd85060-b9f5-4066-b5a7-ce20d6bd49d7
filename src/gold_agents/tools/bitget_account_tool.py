from typing import Dict, Any, Type
from datetime import datetime
from pybitget import Client
from crewai.tools import BaseTool
from pydantic import BaseModel, Field, PrivateAttr
import yaml
import os
from .db_manager import DatabaseManager
from .k_line_util import get_order_history_data
# 修改导入逻辑：显式处理导入错误

class BitgetAccountToolSchema(BaseModel):
    """Bitget账户工具的输入参数模式"""
    trading_pair: str = Field(..., description="交易对，可选值：BTC")

class BitgetAccountTool(BaseTool):
    """Bitget账户信息工具类"""
    name: str = "获取Bitget账户信息的工具"
    description: str = """
    这是一个用于获取Bitget账户信息的工具。
    主要功能：
    1. 获取账户余额信息
    2. 获取持仓信息
    3. 历史订单成交统计信息(每天订单数量、盈亏情况、胜率、盈亏比、总利润、手续费等关键交易指标)
    
    使用示例：
    tool.run(trading_pair="BTC")
    """
    args_schema: Type[BaseModel] = BitgetAccountToolSchema

    # 使用私有属性存储内部状态
    _config: Dict[str, Any] = PrivateAttr(default_factory=dict)
    _environment: str = PrivateAttr(default="test")
    _is_test: bool = PrivateAttr(default=True)
    _margin_coin: str = PrivateAttr(default="SUSDT")
    _client: Client = PrivateAttr(default=None)
    _db: DatabaseManager = PrivateAttr(default=None)
    _product_type: str = PrivateAttr(default="SUMCBL")

    def __init__(self, **kwargs: Any) -> None:
        """初始化工具"""
        super().__init__(**kwargs)
        self._load_config()
        self._init_environment()
        self._init_client()
        self._init_database()

    def _load_config(self) -> None:
        """加载配置文件"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), '../config/trader_config.yaml')
            with open(config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f)
        except Exception as e:
            raise Exception(f"加载配置文件失败: {str(e)}")

    def _init_environment(self) -> None:
        """初始化环境配置"""
        self._environment = self._config['environment']
        if self._environment not in ['test', 'prod']:
            raise ValueError("环境配置错误，必须是 'test' 或 'prod'")
        self._is_test = self._environment == 'test'
        self._margin_coin = "SUSDT" if self._is_test else "USDT"
        self._product_type = "SUMCBL" if self._is_test else "UMCBL"

    def _init_client(self) -> None:
        """初始化API客户端"""
        api_config = self._config['api']
        if not all([api_config['api_key'], api_config['api_secret'], api_config['passphrase']]):
            raise ValueError("API配置不完整，请检查配置文件")
        
        try:
            self._client = Client(
                api_key=api_config['api_key'],
                api_secret_key=api_config['api_secret'],
                passphrase=api_config['passphrase']
            )
        except Exception as e:
            raise Exception(f"初始化API客户端失败: {str(e)}")

    def _init_database(self) -> None:
        """初始化数据库管理器"""
        if DatabaseManager is None:
            raise RuntimeError("数据库管理器未正确初始化")
        self._db = DatabaseManager()

    def _run(self, **kwargs: Any) -> str:
        """执行工具的主要逻辑"""
        try:
            trading_pair = kwargs.get('trading_pair', 'BTC')
            
            # 获取账户信息
            account_info = self._get_account_info()
            
            # 获取持仓信息
            position_info = self._get_position_info(trading_pair)

            #历史订单成交信息
            history_data = get_order_history_data()
            
            result = {
                "status": "success",
                "account": account_info,
                "position": position_info,
                "order_transaction_data":history_data,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            return str(result)
        except Exception as e:
            error_result = {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            return str(error_result)

    def _get_account_info(self) -> Dict[str, Any]:
        """获取账户信息"""
        try:
            result = self._client.mix_get_accounts(productType=self._product_type)
            print("===============获取账户信息==================")
            print(result)
            if result and 'data' in result:
                for account in result['data']:
                    if account.get('marginCoin') == self._margin_coin:
                        account_info = {
                            "balance": float(account.get('available', 0)),
                            "position": float(account.get('total', 0)),
                            "unrealized_pnl": float(account.get('unrealizedPL', 0))
                        }
                        return account_info
            return self._get_empty_account_info()
        except Exception as e:
            raise Exception(f"获取账户信息失败: {str(e)}")

    def _get_position_info(self, trading_pair: str) -> Dict[str, Any]:
        """获取持仓信息"""
        try:
            # 获取所有持仓信息
            result = self._client.mix_get_all_positions(
                productType=self._product_type,
                marginCoin=self._margin_coin
            )
            print("===============获取所有持仓信息==================")
            print(f"产品类型: {self._product_type}")
            print(f"保证金币种: {self._margin_coin}")
            print(result)

            if result and 'data' in result and result['data']:
                # 找到对应交易对的持仓
                for position in result['data']:
                    if position.get('symbol', '').startswith(trading_pair):
                        position_info = self._format_position_info(position)
                        return position_info
            return self._get_empty_position_info()
        except Exception as e:
            raise Exception(f"获取持仓信息失败: {str(e)}")

    def _format_position_info(self, position: Dict[str, Any]) -> Dict[str, Any]:
        """格式化持仓信息"""
        return {
            "position_id": position.get('positionId'),
            "side": position.get('holdSide'),
            "size": float(position.get('total', 0)),
            "entry_price": float(position.get('averageOpenPrice', 0)),
            "unrealized_pnl": float(position.get('unrealizedPL', 0)),
            "leverage": float(position.get('leverage', 0)),
            "margin": float(position.get('margin', 0)),
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }





    def _get_empty_account_info(self) -> Dict[str, Any]:
        """获取空账户信息"""
        return {
            "balance": 0.0,
            "position": 0.0,
            "unrealized_pnl": 0.0
        }

    def _get_empty_position_info(self) -> Dict[str, Any]:
        """获取空持仓信息"""
        return {
            "position_id": None,
            "side": None,
            "size": 0.0,
            "entry_price": 0.0,
            "unrealized_pnl": 0.0,
            "leverage": 0.0,
            "margin": 0.0,
            "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }

if __name__ == '__main__':
    # 测试工具
    tool = BitgetAccountTool()
    
    # 测试获取账户信息
    account_info = tool.run(trading_pair="BTC")
    print("账户信息:", account_info) 