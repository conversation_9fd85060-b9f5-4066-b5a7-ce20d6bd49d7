from typing import Any, Optional, Type

from crewai.tools import BaseTool
from pydantic import BaseModel, Field
# from .k_line_util import get_k_line_data,get_order_history_data,analysis_records
from .k_line_util_simplified import get_order_history_data,analysis_records,last_event_data
from .k_line_util_agent_optimized import get_k_line_data ,get_multi_timeframe_analysis
# 导入新的Qlib集成工具
from .qlib_integration_tool import get_qlib_analysis_data, get_comprehensive_analysis


class KlineDataToolSchema(BaseModel):
    # k_line_cycle: int = Field(..., description="K线周期类型 ")
    pass


class KlineKlineDataTool(BaseTool):
    name: str = "合约BTCUSDT合约K线数据获取工具"
    description: str = ("用于获取合约BTCUSDT的历史K线数据。"
                        "目前主要支持获取15min、1h、4h、1D的K线数据。"
                        "还包含多周期综合分析数据，主要用于1H以上周期交易决策，15min/30min仅作参考和异动检测"
                        "集成Qlib分析功能，提供更强大的AI驱动分析能力，使用现有模型进行分析")
    args_schema: Type[BaseModel] = KlineDataToolSchema
    # k_line_cycle: Optional[int] = 3

    def __init__(self,  **kwargs: Any) -> None:
        super().__init__(**kwargs)


    def _run(self,**kwargs: Any) -> str:
        try:
            # 获取传统K线分析数据
            kine_1h_Data=get_k_line_data(5)
            kine_4h_Data=get_k_line_data(7)
            kine_1d_Data=get_k_line_data(8)
            multi_timeframe_analysis_data=get_multi_timeframe_analysis()
            
            # 使用Qlib集成分析（替代部分功能）
            qlib_1h_analysis = get_qlib_analysis_data(5)
            qlib_4h_analysis = get_qlib_analysis_data(7)
            qlib_1d_analysis = get_qlib_analysis_data(8)
            
            # 获取Qlib综合分析报告
            qlib_comprehensive_analysis = get_comprehensive_analysis()
            
            return {
                # 保留原有数据以确保兼容性
                "kine_1h_Data":kine_1h_Data,
                "kine_4h_Data":kine_4h_Data,
                "kine_1d_Data":kine_1d_Data,
                "multi_timeframe_analysis_data":multi_timeframe_analysis_data,
                
                # 添加Qlib分析数据
                "qlib_1h_analysis": qlib_1h_analysis,
                "qlib_4h_analysis": qlib_4h_analysis,
                "qlib_1d_analysis": qlib_1d_analysis,
                
                # 添加Qlib综合分析报告
                "qlib_comprehensive_analysis": qlib_comprehensive_analysis,
                
                # 综合分析结果
                # "comprehensive_analysis": self._combine_analyses(
                #     kine_1h_Data, kine_4h_Data, kine_1d_Data,
                #     qlib_1h_analysis, qlib_4h_analysis, qlib_1d_analysis
                # )
            }
        except ModuleNotFoundError:
            return "获取K线数据失败"
        except Exception as e:
            return f"获取合约BTCUSDT K线数据时出错: {e}"
    
    def _combine_analyses(self, 
                         traditional_1h: dict, traditional_4h: dict, traditional_1d: dict,
                         qlib_1h: dict, qlib_4h: dict, qlib_1d: dict) -> dict:
        """
        综合传统分析和Qlib分析结果
        
        Args:
            traditional_1h, traditional_4h, traditional_1d: 传统分析结果
            qlib_1h, qlib_4h, qlib_1d: Qlib分析结果
            
        Returns:
            dict: 综合分析结果
        """
        try:
            # 提取关键信号
            combined_signals = {
                "timestamp": qlib_1h.get("last_update", ""),
                "current_price": qlib_1h.get("current_price", 0),
                "timeframe_signals": {}
            }
            
            # 整合各时间框架的信号
            for tf_name, traditional_data, qlib_data in [
                ("1h", traditional_1h, qlib_1h),
                ("4h", traditional_4h, qlib_4h),
                ("1d", traditional_1d, qlib_1d)
            ]:
                timeframe_signal = {
                    "traditional_signal": traditional_data.get("indicators", {}).get("signal_quality", "WEAK") 
                        if not "error" in traditional_data else "ERROR",
                    "qlib_signal": qlib_data.get("signal_quality", {}).get("quality", "WEAK") 
                        if not "error" in qlib_data else "ERROR",
                    "price": qlib_data.get("current_price", 0),
                    "trend_state": qlib_data.get("market_state", {}).get("trend_state", "unknown"),
                    "volatility_state": qlib_data.get("market_state", {}).get("volatility_state", "unknown")
                }
                
                # 综合信号质量（取较高质量）
                traditional_quality = timeframe_signal["traditional_signal"]
                qlib_quality = timeframe_signal["qlib_signal"]
                
                # 定义信号质量等级
                quality_levels = {"WEAK": 1, "GAMMA": 2, "BETA": 3, "ALPHA": 4, "ERROR": 0}
                if quality_levels.get(qlib_quality, 0) > quality_levels.get(traditional_quality, 0):
                    timeframe_signal["combined_signal"] = qlib_quality
                else:
                    timeframe_signal["combined_signal"] = traditional_quality
                
                combined_signals["timeframe_signals"][tf_name] = timeframe_signal
            
            # 总体市场状态
            combined_signals["overall_market_state"] = self._assess_overall_market_state(combined_signals)
            
            return combined_signals
            
        except Exception as e:
            return {"error": f"综合分析失败: {str(e)}"}
    
    def _assess_overall_market_state(self, combined_signals: dict) -> dict:
        """
        评估总体市场状态
        
        Args:
            combined_signals: 综合信号数据
            
        Returns:
            dict: 总体市场状态评估
        """
        try:
            tf_signals = combined_signals.get("timeframe_signals", {})
            
            # 收集趋势信息
            trends = [signal.get("trend_state", "unknown") for signal in tf_signals.values()]
            volatilities = [signal.get("volatility_state", "unknown") for signal in tf_signals.values()]
            
            # 简单多数投票确定趋势
            trend_counts = {}
            for trend in trends:
                trend_counts[trend] = trend_counts.get(trend, 0) + 1
            overall_trend = max(trend_counts, key=trend_counts.get) if trend_counts else "unknown"
            
            # 简单多数投票确定波动率状态
            volatility_counts = {}
            for vol in volatilities:
                volatility_counts[vol] = volatility_counts.get(vol, 0) + 1
            overall_volatility = max(volatility_counts, key=volatility_counts.get) if volatility_counts else "unknown"
            
            # 信号质量评估
            signal_qualities = [signal.get("combined_signal", "WEAK") for signal in tf_signals.values()]
            quality_levels = {"WEAK": 1, "GAMMA": 2, "BETA": 3, "ALPHA": 4}
            
            # 计算平均信号质量
            total_quality = sum(quality_levels.get(q, 1) for q in signal_qualities)
            avg_quality_level = total_quality / len(signal_qualities) if signal_qualities else 1
            
            # 确定总体信号质量
            if avg_quality_level >= 3.5:
                overall_quality = "ALPHA"
            elif avg_quality_level >= 2.5:
                overall_quality = "BETA"
            elif avg_quality_level >= 1.5:
                overall_quality = "GAMMA"
            else:
                overall_quality = "WEAK"
            
            return {
                "overall_trend": overall_trend,
                "overall_volatility": overall_volatility,
                "overall_signal_quality": overall_quality,
                "signal_quality_score": round(avg_quality_level, 2),
                "recommendation": self._get_market_recommendation(overall_trend, overall_volatility, overall_quality)
            }
            
        except Exception as e:
            return {"error": f"总体市场状态评估失败: {str(e)}"}
    
    def _get_market_recommendation(self, trend: str, volatility: str, quality: str) -> str:
        """
        根据市场状态获取推荐
        
        Args:
            trend: 趋势状态
            volatility: 波动率状态
            quality: 信号质量
            
        Returns:
            str: 市场推荐
        """
        # 根据不同组合给出推荐
        if quality == "ALPHA":
            if trend == "strong_trend":
                return "强烈建议趋势跟踪"
            else:
                return "高质量信号，建议关注"
        elif quality == "BETA":
            if volatility == "normal_volatility":
                return "正常波动，可适度参与"
            else:
                return "中等质量信号，注意风险"
        elif quality == "GAMMA":
            return "基础信号，谨慎参与"
        else:
            return "信号较弱，建议观望"

# 保持其他类不变
class Kline15minDataToolSchema(BaseModel):
    k_line_cycle: int = Field(..., description="K线周期类型 ")


class Kline15minKlineDataTool(BaseTool):
    name: str = "合约BTCUSDT_15min_K线数据获取工具"
    description: str = ("用于获取合约BTCUSDT的历史K线数据。"
                        "目前主要支持获取15minK线数据。"
                        "默认获取最近交易日的15min K线数据。")
    args_schema: Type[BaseModel] = Kline15minDataToolSchema
    k_line_cycle: Optional[int] = 3

    def __init__(self, k_line_cycle: Optional[int] = 3, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        self.k_line_cycle = k_line_cycle

    def _run(self, k_line_cycle: int = 3) -> str:
        try:
            return get_k_line_data(3)
        except ModuleNotFoundError:
            return "获取K线数据失败"
        except Exception as e:
            return f"获取合约BTCUSDT K线数据时出错: {e}"


class Kline1hKlineDataToolSchema(BaseModel):
    k_line_cycle: int = Field(..., description="K线周期类型 ")


class Kline1hKlineDataTool(BaseTool):
    name: str = "合约BTCUSDT_1h_K线数据获取工具"
    description: str = ("用于获取合约BTCUSDT的历史K线数据。"
                        "目前主要支持获取 1h K线数据。" )
    args_schema: Type[BaseModel] = Kline1hKlineDataToolSchema
    k_line_cycle: Optional[int] = 5

    def __init__(self, k_line_cycle: Optional[int] = 5, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        self.k_line_cycle = k_line_cycle

    def _run(self, k_line_cycle: int = 5) -> str:
        try:
            # 同时返回传统分析和Qlib分析
            traditional_data = get_k_line_data(5)
            qlib_data = get_qlib_analysis_data(5)
            
            return {
                "traditional_analysis": traditional_data,
                "qlib_analysis": qlib_data
            }
        except ModuleNotFoundError:
            return "获取K线数据失败"
        except Exception as e:
            return f"获取合约BTCUSDT K线数据时出错: {e}"



class Kline4hKlineDataToolSchema(BaseModel):
    k_line_cycle: int = Field(..., description="K线周期类型 ")


class Kline4hKlineDataTool(BaseTool):
    name: str = "合约BTCUSDT_4h_K线数据获取工具"
    description: str = ("用于获取合约BTCUSDT的历史K线数据。"
                        "目前主要支持获取4h K线数据。")
    args_schema: Type[BaseModel] = Kline4hKlineDataToolSchema
    k_line_cycle: Optional[int] = 7

    def __init__(self, k_line_cycle: Optional[int] = 7, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        self.k_line_cycle = k_line_cycle

    def _run(self, k_line_cycle: int = 7) -> str:
        try:
            # 同时返回传统分析和Qlib分析
            traditional_data = get_k_line_data(7)
            qlib_data = get_qlib_analysis_data(7)
            
            return {
                "traditional_analysis": traditional_data,
                "qlib_analysis": qlib_data
            }
        except ModuleNotFoundError:
            return "获取K线数据失败"
        except Exception as e:
            return f"获取合约BTCUSDT K线数据时出错: {e}"


class Kline1dKlineDataToolSchema(BaseModel):
    k_line_cycle: int = Field(..., description="K线周期类型 ")


class Kline1dKlineDataTool(BaseTool):
    name: str = "合约BTCUSDTK_1d_线数据获取工具"
    description: str = ("用于获取合约BTCUSDT的历史K线数据。"
                        "目前主要支持获取1d K线数据。" )
    args_schema: Type[BaseModel] = Kline1dKlineDataToolSchema
    k_line_cycle: Optional[int] = 8

    def __init__(self, k_line_cycle: Optional[int] = 8, **kwargs: Any) -> None:
        super().__init__(**kwargs)
        self.k_line_cycle = k_line_cycle

    def _run(self, k_line_cycle: int = 8) -> str:
        try:
            # 同时返回传统分析和Qlib分析
            traditional_data = get_k_line_data(8)
            qlib_data = get_qlib_analysis_data(8)
            
            return {
                "traditional_analysis": traditional_data,
                "qlib_analysis": qlib_data
            }
        except ModuleNotFoundError:
            return "获取K线数据失败"
        except Exception as e:
            return f"获取合约BTCUSDT K线数据时出错: {e}"


class OrderHistoryDataToolSchema(BaseModel):
    # k_line_cycle: int = Field(..., description="统计天数，默认30天")
    pass


class OrderHistoryDataTool(BaseTool):
    name: str = "订单历史统计数据、前一日交易复盘总结信息获取工具"
    description: str = ("1、获取交易团队成交的订单历史统计数据，统计维度包括每天订单数量、盈亏情况、胜率、盈亏比、总利润、手续费等关键交易指标，用于分析交易表现和优化策略决策。 2、获取前一日根据交易记录的复盘总结信息，优化策略决策 3、上一次的团队分析结果")
    args_schema: Type[BaseModel] = OrderHistoryDataToolSchema
    # k_line_cycle: Optional[int] = 3

    def __init__(self,  **kwargs: Any) -> None:
        super().__init__(**kwargs)

    def _run(self,**kwargs: Any) -> str:
        try:
            history_data = get_order_history_data()
            records=analysis_records()
            event_data = last_event_data()
            print("===============获取所有订单统计信息、复盘记录、上一次的团队分析结果==================")
            print(history_data)
            return {"daily_trading_situation_statistics":history_data,"transaction_review_information":records ,"last_agent_analysis_results":event_data}
        except ModuleNotFoundError:
            return "获取K线数据失败"
        except Exception as e:
            return f"获取合约BTCUSDT K线数据时出错: {e}"


if __name__ == '__main__':
    tool = OrderHistoryDataTool()
    data_daily = tool._run()
    print(data_daily)