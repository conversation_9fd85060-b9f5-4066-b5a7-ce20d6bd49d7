from sqlalchemy import create_engine, Column, String, Text, BigInteger, text
from sqlalchemy.orm import declarative_base, sessionmaker
from typing import List, Dict



DATABASE_URI = 'mysql+pymysql://Mysql5.7:a78d04a8027589c3@43.156.238.66:6221/Mysql5.7'

engine = create_engine(DATABASE_URI)
session_factory = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

class MarketData(Base):
    __tablename__ = 'market_data'

    id = Column(BigInteger, primary_key=True, autoincrement=True)
    symbol = Column(String(255))
    type = Column(String(255))
    open_time = Column(String(255))
    open = Column(String(255))
    high = Column(String(255))
    low = Column(String(255))
    close = Column(String(255))
    volume = Column(String(255))
    quote_asset_volume = Column(String(255))

def init_db():
    """
    创建数据库表
    """
    Base.metadata.create_all(bind=engine)

def data_exists(session, data_type: str, open_time: str) -> bool:
    # print(f"data_type {data_type}  open_time {open_time}")
    data = session.query(MarketData).filter(
        MarketData.type == data_type,
        MarketData.open_time == open_time
    ).first()
    return data  is not None

def batch_insert_market_data(data_list: List[Dict]):
    """
    将市场数据列表批量存储到数据库中

    参数:
        data_list (List[Dict]): 包含市场数据字典的列表
    """
    db = session_factory()
    try:
        for data in data_list:
            # // 如果数据已经存在，则跳过
            if data_exists(db, data["type"], data["open_time"]):
                # print(f"数据 {data['open_time']} 已存在，跳过插入。")
                continue
            # // 创建一个新的数据对象
            print(f"正在插入数据 {data['open_time']}")
            db_data = MarketData(
                # id=data["id"],
                symbol=data["symbol"],
                type=data["type"],
                open_time=data["open_time"],
                open=data["open"],
                high=data["high"],
                low=data["low"],
                close=data["close"],
                volume=data["volume"],
                quote_asset_volume=data["quote_asset_volume"]
            )
            db.add(db_data)
        
        db.commit()
    except Exception as e:
        db.rollback()
        print(f"Error storing market data in database: {e}")
    finally:
        db.close()

def get_market_data_by_symbol(symbol: str ="BTCUSDT",type :str ="15m", limit=100):
    """
    根据symbol查询市场数据，按id倒序排列，并限制返回的记录数量

    参数:
        symbol (str): 要查询的symbol值
        limit (int): 返回的记录数量，默认为100

    返回:
        List[MarketData]: 市场数据记录列表
    """
    db = session_factory()
    try:
        # 查询指定symbol的数据，并按id倒序排列，限制返回的记录数量
        return db.query(MarketData).filter(MarketData.symbol == symbol ,MarketData.type == type).order_by(MarketData.id.desc()).limit(limit).all()
    finally:
        db.close()

def get_trading_statistics(days: int = 30) -> List[Dict]:
    """
    获取交易统计信息
    
    参数:
        days (int): 统计天数，默认30天
        
    返回:
        List[Dict]: 每日交易统计数据列表
    """
    db = session_factory()
    try:
        query = """
            SELECT 
                DATE(fill_time) as trade_date, 
                COUNT(*) as total_orders, 
                COUNT(CASE WHEN pnl > 0 THEN 1 END) as profit_orders, 
                COUNT(CASE WHEN pnl < 0 THEN 1 END) as loss_orders, 
                COUNT(CASE WHEN pnl = 0 THEN 1 END) as breakeven_orders, 
                ROUND(SUM(pnl),2) as total_pnl, 
                ROUND(SUM(CASE WHEN pnl > 0 THEN pnl ELSE 0 END),2) as total_profit, 
                ROUND(SUM(CASE WHEN pnl < 0 THEN pnl ELSE 0 END),2) as total_loss, 
                ROUND(AVG(pnl) ,2) as avg_pnl, 
                ROUND( 
                    COUNT(CASE WHEN pnl > 0 THEN 1 END) * 100.0 / 
                    NULLIF(COUNT(CASE WHEN pnl != 0 THEN 1 END), 0), 
                    2 
                ) as win_rate_percent, 
                ROUND( 
                    ABS(AVG(CASE WHEN pnl > 0 THEN pnl END)) / 
                    NULLIF(ABS(AVG(CASE WHEN pnl < 0 THEN pnl END)), 0), 
                    2 
                ) as profit_loss_ratio, 
                SUM(fill_size) as total_volume, 
                AVG(fill_size) as avg_order_size, 
                AVG(fill_price) as avg_fill_price, 
                MIN(fill_price) as min_fill_price, 
                MAX(fill_price) as max_fill_price, 
                ROUND(SUM(fill_fee),2) as total_fees, 
                ROUND(AVG(fill_fee),2) as avg_fee_per_order, 
                COUNT(CASE WHEN side = 'buy' THEN 1 END) as buy_orders, 
                COUNT(CASE WHEN side = 'sell' THEN 1 END) as sell_orders, 
                COUNT(CASE WHEN order_type = 'market' THEN 1 END) as market_orders, 
                COUNT(CASE WHEN order_type = 'limit' THEN 1 END) as limit_orders, 
                MAX(pnl) as max_profit, 
                MIN(pnl) as max_loss, 
                AVG(pnl_ratio) as avg_pnl_ratio, 
                MAX(pnl_ratio) as max_pnl_ratio, 
                MIN(pnl_ratio) as min_pnl_ratio 
            FROM order_records 
            WHERE fill_time IS NOT NULL   
                AND DATE(fill_time) >= DATE_SUB(CURDATE(), INTERVAL :days DAY) 
            GROUP BY DATE(fill_time) 
            ORDER BY trade_date DESC
        """
        
        result = db.execute(text(query), {"days": days})
        
        # 转换结果为字典列表
        statistics = []
        for row in result:
            statistics.append({
                "trade_date": row[0].strftime("%Y-%m-%d") if row[0] else None,
                "total_orders": row[1],
                "profit_orders": row[2],
                "loss_orders": row[3],
                "breakeven_orders": row[4],
                "total_pnl": float(row[5]) if row[5] else 0.0,
                "total_profit": float(row[6]) if row[6] else 0.0,
                "total_loss": float(row[7]) if row[7] else 0.0,
                "avg_pnl": float(row[8]) if row[8] else 0.0,
                "win_rate_percent": float(row[9]) if row[9] else 0.0,
                "profit_loss_ratio": float(row[10]) if row[10] else 0.0,
                "total_volume": float(row[11]) if row[11] else 0.0,
                "avg_order_size": float(row[12]) if row[12] else 0.0,
                "avg_fill_price": float(row[13]) if row[13] else 0.0,
                "min_fill_price": float(row[14]) if row[14] else 0.0,
                "max_fill_price": float(row[15]) if row[15] else 0.0,
                "total_fees": float(row[16]) if row[16] else 0.0,
                "avg_fee_per_order": float(row[17]) if row[17] else 0.0,
                "buy_orders": row[18],
                "sell_orders": row[19],
                "market_orders": row[20],
                "limit_orders": row[21],
                "max_profit": float(row[22]) if row[22] else 0.0,
                "max_loss": float(row[23]) if row[23] else 0.0,
                "avg_pnl_ratio": float(row[24]) if row[24] else 0.0,
                "max_pnl_ratio": float(row[25]) if row[25] else 0.0,
                "min_pnl_ratio": float(row[26]) if row[26] else 0.0
            })
            
        return statistics
        
    except Exception as e:
        print(f"获取交易统计信息失败: {str(e)}")
        return []
    finally:
        db.close()

def post_trade_analysis_records():
    """
    获取交易统计信息

    参数:
        days (int): 统计天数，默认30天

    返回:
        List[Dict]: 每日交易统计数据列表
    """
    db = session_factory()
    try:
        query = """
                SELECT improvement_suggestions from   post_trade_analysis_records ORDER BY id  desc  limit 1 \
                """

        result = db.execute(text(query)).scalar()

        return result

    except Exception as e:
        print(f"获取交易统计信息失败: {str(e)}")
        return []
    finally:
        db.close()



def event_data():
    """
    上一次的分析处理结果

    """
    db = session_factory()
    try:
        query = """
                SELECT el.event_data
                FROM crewai_execution_logs el
                         JOIN (
                    SELECT session_id
                    FROM crewai_execution_logs
                    ORDER BY Id DESC
                        LIMIT 1
                ) latest_session ON el.session_id = latest_session.session_id WHERE event_type =  'CREW_COMPLETED'  \
                """

        result = db.execute(text(query)).scalar()

        return result

    except Exception as e:
        print(f"获取交易统计信息失败: {str(e)}")
        return []
    finally:
        db.close()


if __name__ == '__main__':
    # k_line = get_market_kline_by_technical(type='1D',limit=50)
    print("")
    ta = post_trade_analysis_records()
    print(str(ta))



