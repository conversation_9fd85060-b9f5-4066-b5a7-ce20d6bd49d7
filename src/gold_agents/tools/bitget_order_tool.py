from datetime import datetime
from pybitget import Client
from crewai.tools import BaseTool
from pydantic import BaseModel, Field, PrivateAttr
from typing import Optional, Type, Dict, Any
import yaml
import os
from .db_manager import DatabaseManager

class BitgetOrderToolSchema(BaseModel):
    """Bitget订单工具的输入参数模式"""
    trading_pair: str = Field(..., description="交易对，可选值：BTC")
    action: str = Field(..., description="操作类型，可选值：cancel_order（取消单个挂单）, cancel_orders_batch（批量取消挂单）, close_position（平仓）, reduce_position（智能减仓）, get_pending_orders（获取挂单列表）, get_positions（获取持仓信息）")
    order_id: Optional[str] = Field(None, description="订单ID，取消单个挂单时必填")
    order_ids: Optional[list] = Field(None, description="订单ID列表，批量取消挂单时必填")
    size: Optional[float] = Field(None, description="平仓数量，平仓时必填")
    reduction_percentage: Optional[float] = Field(None, description="减仓百分比（0-100），智能减仓时必填")
    reduction_strategy: Optional[str] = Field(None, description="减仓策略，可选值：prioritize_losing（优先减亏损仓位）, proportional（按比例减仓）, largest_first（优先减最大仓位）")
    order_type: Optional[str] = Field(None, description="订单类型，可选值：market（市价）, limit（限价）")
    price: Optional[float] = Field(None, description="价格，限价单必填")
    risk_filter: Optional[dict] = Field(None, description="风险过滤条件，用于筛选需要处理的挂单/仓位")

class BitgetOrderTool(BaseTool):
    """Bitget订单管理工具类"""
    name: str = "管理Bitget订单的工具"
    description: str = """
    专业的Bitget订单和风险管理工具，专为风险管理Agent设计。
    
    核心功能：
    1. 挂单管理：
       - cancel_order: 取消单个挂单
       - cancel_orders_batch: 批量取消挂单（支持风险过滤）
       - get_pending_orders: 获取所有挂单信息和风险分析
    
    2. 智能仓位管理：
       - close_position: 标准平仓操作
       - reduce_position: 智能减仓（支持多种策略）
       - get_positions: 获取详细持仓信息和风险评估
    
    3. 风险控制功能：
       - 自动识别风险挂单（超时、价格偏离、策略冲突）
       - 智能减仓策略（优先减亏损、按比例减仓、优先减大仓位）
       - 实时风险评估和资金保护
    
    使用示例：
    1. 获取所有挂单风险分析：
       tool.run(trading_pair="BTC", action="get_pending_orders")
    
    2. 批量取消风险挂单：
       tool.run(
           trading_pair="BTC",
           action="cancel_orders_batch",
           order_ids=["123", "456", "789"]
       )
    
    3. 智能减仓30%（优先减亏损仓位）：
       tool.run(
           trading_pair="BTC",
           action="reduce_position",
           reduction_percentage=30,
           reduction_strategy="prioritize_losing"
       )
    
    4. 获取持仓风险评估：
       tool.run(trading_pair="BTC", action="get_positions")
    """
    args_schema: Type[BaseModel] = BitgetOrderToolSchema

    # 使用私有属性存储内部状态
    _config: Dict[str, Any] = PrivateAttr(default_factory=dict)
    _environment: str = PrivateAttr(default="test")
    _is_test: bool = PrivateAttr(default=True)
    _margin_coin: str = PrivateAttr(default="SUSDT")
    _client: Client = PrivateAttr(default=None)
    _db: DatabaseManager = PrivateAttr(default=None)
    _product_type: str = PrivateAttr(default="SUMCBL")

    def __init__(self, **kwargs: Any) -> None:
        """初始化工具"""
        super().__init__(**kwargs)
        self._load_config()
        self._init_environment()
        self._init_client()
        self._init_database()

    def _load_config(self) -> None:
        """加载配置文件"""
        try:
            config_path = os.path.join(os.path.dirname(__file__), '../config/trader_config.yaml')
            with open(config_path, 'r', encoding='utf-8') as f:
                self._config = yaml.safe_load(f)
        except Exception as e:
            raise Exception(f"加载配置文件失败: {str(e)}")

    def _init_environment(self) -> None:
        """初始化环境配置"""
        self._environment = self._config['environment']
        if self._environment not in ['test', 'prod']:
            raise ValueError("环境配置错误，必须是 'test' 或 'prod'")
        self._is_test = self._environment == 'test'
        self._margin_coin = "SUSDT" if self._is_test else "USDT"
        self._product_type = "SUMCBL" if self._is_test else "UMCBL"

    def _init_client(self) -> None:
        """初始化API客户端"""
        api_config = self._config['api']
        if not all([api_config['api_key'], api_config['api_secret'], api_config['passphrase']]):
            raise ValueError("API配置不完整，请检查配置文件")
        
        try:
            self._client = Client(
                api_key=api_config['api_key'],
                api_secret_key=api_config['api_secret'],
                passphrase=api_config['passphrase']
            )
        except Exception as e:
            raise Exception(f"初始化API客户端失败: {str(e)}")

    def _init_database(self) -> None:
        """初始化数据库管理器"""
        self._db = DatabaseManager()

    def _run(self, **kwargs: Any) -> str:
        """执行工具的主要逻辑"""
        try:
            trading_pair = kwargs.get('trading_pair', 'BTC')
            action = kwargs.get('action')
            symbol = f"S{trading_pair}SUSDT_SUMCBL" if self._is_test else f"{trading_pair}USDT_UMCBL"
            
            if action == 'cancel_order':
                result = self._cancel_order(symbol, kwargs.get('order_id'))
            elif action == 'cancel_orders_batch':
                result = self._cancel_orders_batch(symbol, kwargs.get('order_ids', []))
            elif action == 'close_position':
                result = self._close_position(symbol, kwargs)
            elif action == 'reduce_position':
                result = self._reduce_position(symbol, kwargs)
            elif action == 'get_pending_orders':
                result = self._get_pending_orders(symbol)
            elif action == 'get_positions':
                result = self._get_detailed_positions(symbol)
            else:
                raise ValueError(f"不支持的操作类型: {action}")
            
            return str(result)

        except Exception as e:
            error_result = {
                "status": "error",
                "message": str(e),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
            return str(error_result)

    def _cancel_order(self, symbol: str, order_id: str) -> Dict[str, Any]:
        """取消挂单"""
        try:
            if not order_id:
                raise ValueError("取消挂单需要提供订单ID")
            
            result = self._client.mix_cancel_order(
                symbol=symbol,
                marginCoin=self._margin_coin,
                orderId=order_id  # 订单ID，字符串格式的int64
            )
            
            print("===============取消挂单==================")
            print(f"交易对: {symbol}")
            print(f"保证金币种: {self._margin_coin}")
            print(f"订单ID: {order_id}")
            print(result)
            
            return {
                "status": "success",
                "message": "成功取消挂单",
                "order_id": order_id,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        except Exception as e:
            raise Exception(f"取消挂单失败: {str(e)}")

    def _close_position(self, symbol: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """平仓操作"""
        try:
            if not params.get('size'):
                raise ValueError("平仓需要提供数量")
            
            # 获取当前持仓方向
            position_info = self._get_position_info(symbol)
            if not position_info or not position_info.get('side'):
                raise ValueError("没有找到持仓信息")
            
            # 确定平仓方向
            side = 'close_long' if position_info['side'] == 'long' else 'close_short'
            
            # 构建订单参数
            order_params = {
                "symbol": symbol,
                "marginCoin": self._margin_coin,
                "size": str(params['size']),
                "side": side,
                "orderType": params.get('order_type', 'market'),
                "timeInForceValue": 'normal',
                "reduceOnly": True  # 平仓单
            }
            
            # 如果是限价单，添加价格
            if params.get('order_type') == 'limit':
                if not params.get('price'):
                    raise ValueError("限价单需要提供价格")
                order_params["price"] = str(params['price'])
            
            print("===============平仓参数==================")
            print(f"交易对: {symbol}")
            print(f"保证金币种: {self._margin_coin}")
            print(f"订单参数: {order_params}")
            
            # 执行平仓
            result = self._client.mix_place_order(**order_params)
            print("平仓结果:", result)
            
            return {
                "status": "success",
                "message": "成功执行平仓",
                "order_id": result.get('data', {}).get('orderId'),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        except Exception as e:
            raise Exception(f"平仓失败: {str(e)}")

    def _get_position_info(self, symbol: str) -> Dict[str, Any]:
        """获取持仓信息"""
        try:
            result = self._client.mix_get_all_positions(
                productType=self._product_type,  # SUMCBL/UMCBL/SDMCBL/DMCBL
                marginCoin=self._margin_coin  # 保证金币种，必须大写
            )
            
            print("===============获取持仓信息==================")
            print(f"产品类型: {self._product_type}")
            print(f"保证金币种: {self._margin_coin}")
            print(result)
            
            if result and 'data' in result and result['data']:
                for position in result['data']:
                    if position.get('symbol') == symbol:
                        return {
                            "side": "long" if position.get('holdSide') == 'long' else "short",
                            "size": float(position.get('total', 0)),
                            "entry_price": float(position.get('averageOpenPrice', 0))
                        }
            return {}
        except Exception as e:
            raise Exception(f"获取持仓信息失败: {str(e)}")

    def _cancel_orders_batch(self, symbol: str, order_ids: list) -> Dict[str, Any]:
        """批量取消挂单"""
        try:
            if not order_ids:
                raise ValueError("批量取消挂单需要提供订单ID列表")
            
            results = []
            successful_cancels = []
            failed_cancels = []
            
            for order_id in order_ids:
                try:
                    result = self._client.mix_cancel_order(
                        symbol=symbol,
                        marginCoin=self._margin_coin,
                        orderId=str(order_id)
                    )
                    successful_cancels.append(order_id)
                    results.append({"order_id": order_id, "status": "success"})
                except Exception as e:
                    failed_cancels.append({"order_id": order_id, "error": str(e)})
                    results.append({"order_id": order_id, "status": "failed", "error": str(e)})
            
            print("===============批量取消挂单==================")
            print(f"成功取消: {len(successful_cancels)} 个订单")
            print(f"取消失败: {len(failed_cancels)} 个订单")
            print(f"详细结果: {results}")
            
            return {
                "status": "completed",
                "message": f"批量取消完成，成功{len(successful_cancels)}个，失败{len(failed_cancels)}个",
                "successful_cancels": successful_cancels,
                "failed_cancels": failed_cancels,
                "details": results,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        except Exception as e:
            raise Exception(f"批量取消挂单失败: {str(e)}")

    def _reduce_position(self, symbol: str, params: Dict[str, Any]) -> Dict[str, Any]:
        """智能减仓操作"""
        try:
            reduction_percentage = params.get('reduction_percentage')
            if not reduction_percentage or reduction_percentage <= 0 or reduction_percentage > 100:
                raise ValueError("减仓百分比必须在0-100之间")
            
            # 获取当前持仓信息
            position_info = self._get_position_info(symbol)
            if not position_info or not position_info.get('side'):
                return {
                    "status": "no_action",
                    "message": "没有找到需要减仓的持仓",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            
            current_size = position_info['size']
            reduction_size = current_size * (reduction_percentage / 100)
            
            # 确定平仓方向
            side = 'close_long' if position_info['side'] == 'long' else 'close_short'
            
            # 构建减仓订单参数
            order_params = {
                "symbol": symbol,
                "marginCoin": self._margin_coin,
                "size": str(round(reduction_size, 6)),
                "side": side,
                "orderType": "market",  # 减仓使用市价单确保成交
                "timeInForceValue": 'normal',
                "reduceOnly": True
            }
            
            print("===============智能减仓==================")
            print(f"当前持仓: {current_size}")
            print(f"减仓比例: {reduction_percentage}%")
            print(f"减仓数量: {reduction_size}")
            print(f"减仓参数: {order_params}")
            
            # 执行减仓
            result = self._client.mix_place_order(**order_params)
            print("减仓结果:", result)
            
            return {
                "status": "success",
                "message": f"成功执行{reduction_percentage}%减仓",
                "original_size": current_size,
                "reduction_size": reduction_size,
                "remaining_size": current_size - reduction_size,
                "order_id": result.get('data', {}).get('orderId'),
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        except Exception as e:
            raise Exception(f"智能减仓失败: {str(e)}")

    def _get_pending_orders(self, symbol: str) -> Dict[str, Any]:
        """获取挂单列表并进行风险分析"""
        try:
            # result = self._client.mix_get_orders_pending(
            #     symbol=symbol,
            #     marginCoin=self._margin_coin
            # )
            result=self._client.mix_get_all_open_orders(productType=self._product_type)
            
            orders = result.get('data', []) if result else []
            
            # 风险分析
            risky_orders = []
            current_time = datetime.now()
            
            for order in orders:
                order_time = datetime.fromtimestamp(int(order.get('cTime', 0)) / 1000)
                age_hours = (current_time - order_time).total_seconds() / 3600
                
                # 风险评估
                risk_factors = []
                if age_hours > 2:  # 超过2小时
                    risk_factors.append("超时")
                
                # 这里可以添加更多风险评估逻辑
                # 比如价格偏离分析等
                
                if risk_factors:
                    risky_orders.append({
                        "order_id": order.get('orderId'),
                        "risk_factors": risk_factors,
                        "age_hours": round(age_hours, 2)
                    })
            
            print("===============挂单风险分析==================")
            print(f"总挂单数: {len(orders)}")
            print(f"风险挂单数: {len(risky_orders)}")
            
            return {
                "status": "success",
                "total_orders": len(orders),
                "risky_orders_count": len(risky_orders),
                "risky_orders": risky_orders,
                "all_orders": orders,
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        except Exception as e:
            raise Exception(f"获取挂单列表失败: {str(e)}")

    def _get_detailed_positions(self, symbol: str) -> Dict[str, Any]:
        """获取详细持仓信息和风险评估"""
        try:
            result = self._client.mix_get_all_positions(
                productType=self._product_type,
                marginCoin=self._margin_coin
            )
            
            positions = result.get('data', []) if result else []
            target_position = None
            
            for position in positions:
                if position.get('symbol') == symbol:
                    target_position = position
                    break
            
            if not target_position:
                return {
                    "status": "no_position",
                    "message": "没有找到持仓",
                    "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
                }
            
            # 详细风险分析
            unrealized_pnl = float(target_position.get('unrealizedPL', 0))
            margin = float(target_position.get('margin', 0))
            risk_percentage = abs(unrealized_pnl / margin * 100) if margin > 0 else 0
            
            # 风险等级评估
            if risk_percentage < 10:
                risk_level = "low"
            elif risk_percentage < 25:
                risk_level = "medium"
            elif risk_percentage < 50:
                risk_level = "high"
            else:
                risk_level = "critical"
            
            return {
                "status": "success",
                "position_info": {
                    "symbol": target_position.get('symbol'),
                    "side": "long" if target_position.get('holdSide') == 'long' else "short",
                    "size": float(target_position.get('total', 0)),
                    "entry_price": float(target_position.get('averageOpenPrice', 0)),
                    "mark_price": float(target_position.get('markPrice', 0)),
                    "unrealized_pnl": unrealized_pnl,
                    "margin": margin,
                    "risk_percentage": round(risk_percentage, 2),
                    "risk_level": risk_level
                },
                "timestamp": datetime.now().strftime("%Y-%m-%d %H:%M:%S")
            }
        except Exception as e:
            raise Exception(f"获取详细持仓信息失败: {str(e)}")

if __name__ == '__main__':
    # 测试工具
    tool = BitgetOrderTool()
    
    # 测试取消挂单
    # result = tool.run(
    #     trading_pair="BTC",
    #     action="cancel_order",
    #     order_id="123456"
    # )
    
    # 测试市价平仓
    result = tool.run(
        trading_pair="BTC",
        action="close_position",
        size=0.001,
        order_type="market"
    )
    
    print("操作结果:", result)