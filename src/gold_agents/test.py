from  main import main
from pybitget import Client

if __name__ == "__main__":
    main()
#     clent = Client(
#                 api_key='bg_44025411ed4ba2c0032ac4e2dff2b18e',
#                 api_secret_key='d30fa4f8e6d80462659ed7e525d489020413a4e9d11cd16a3c8f6ceeccd87500',
#                 passphrase='**********'
#             )
#     result = clent.mix_get_all_positions(
#                 productType='SUMCBL',
#                 marginCoin='SUSDT'
#             )
#     print("持仓")
#     print(result)
#     print("--------------------")
#     acc = clent.mix_get_accounts(productType='SUMCBL')
#     print("账户")
#     print(acc)