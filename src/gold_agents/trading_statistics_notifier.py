"""交易统计推送器 - 定期推送交易统计数据到企业微信"""

import requests
import json
from datetime import datetime, timedelta
from typing import Dict, Any, List
from sqlalchemy import text
from order_mapper import get_order_mapper
from pybitget import logger
from pybitget import Client
# 企业微信机器人配置
WECHAT_ROBOT_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=22f9bc8d-bc61-45f2-beac-d996dbe425d3'


original_funds_data =  {'code': '00000', 'msg': 'success', 'requestTime': 1750749043384, 'data': [{'marginCoin': 'SUSDT', 'locked': '0', 'available': '2846.76449272', 'crossMaxAvailable': '2667.63903562', 'fixedMaxAvailable': '2667.63903562', 'maxTransferOut': '2630.97807185', 'equity': '2883.4254565', 'usdtEquity': '2883.425456504798', 'btcEquity': '0.027321608938', 'crossRiskRate': '0.020623715925', 'unrealizedPL': '36.660963776969', 'bonus': '0', 'crossedUnrealizedPL': None, 'isolatedUnrealizedPL': None}]}

class TradingStatisticsNotifier:
    """交易统计推送器"""

    def __init__(self):
        self.order_mapper = get_order_mapper()

    def get_daily_statistics(self, days: int = 30) -> List[Dict[str, Any]]:
        """获取每日交易统计数据"""
        try:
            with self.order_mapper.session_scope() as session:
                # 执行统计SQL查询
                sql = """
                      SELECT
                          DATE(fill_time) as trade_date,


                          COUNT(*) as total_orders,


                          COUNT(CASE WHEN pnl > 0 THEN 1 END) as profit_orders,
                          COUNT(CASE WHEN pnl < 0 THEN 1 END) as loss_orders,
                          COUNT(CASE WHEN pnl = 0 THEN 1 END) as breakeven_orders,


                          ROUND(SUM(pnl),2) as total_pnl,
                          ROUND(SUM(CASE WHEN pnl > 0 THEN pnl ELSE 0 END),2)  as total_profit,
                          ROUND(SUM(CASE WHEN pnl < 0 THEN pnl ELSE 0 END),2) as total_loss,
                          ROUND(AVG(pnl) ,2)as avg_pnl,


                          ROUND(
                          COUNT(CASE WHEN pnl > 0 THEN 1 END) * 100.0 /
                          NULLIF(COUNT(CASE WHEN pnl != 0 THEN 1 END), 0),
                          2
                          ) as win_rate_percent,


                          ROUND(
                          ABS(AVG(CASE WHEN pnl > 0 THEN pnl END)) /
                          NULLIF(ABS(AVG(CASE WHEN pnl < 0 THEN pnl END)), 0),
                          2
                          ) as profit_loss_ratio,


                          SUM(fill_size) as total_volume,
                          AVG(fill_size) as avg_order_size,


                          AVG(fill_price) as avg_fill_price,
                          MIN(fill_price) as min_fill_price,
                          MAX(fill_price) as max_fill_price,


                          ROUND(SUM(fill_fee),2) as total_fees,
                          ROUND(AVG(fill_fee),2) as avg_fee_per_order,


                          COUNT(CASE WHEN side = 'buy' THEN 1 END) as buy_orders,
                          COUNT(CASE WHEN side = 'sell' THEN 1 END) as sell_orders,


                          COUNT(CASE WHEN order_type = 'market' THEN 1 END) as market_orders,
                          COUNT(CASE WHEN order_type = 'limit' THEN 1 END) as limit_orders,


                          MAX(pnl) as max_profit,
                          MIN(pnl) as max_loss,


                          AVG(pnl_ratio) as avg_pnl_ratio,
                          MAX(pnl_ratio) as max_pnl_ratio,
                          MIN(pnl_ratio) as min_pnl_ratio

                      FROM order_records
                      WHERE fill_time IS NOT NULL
                        AND DATE(fill_time) >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
                      GROUP BY DATE(fill_time)
                      ORDER BY trade_date DESC \
                      """

                result = session.execute(text(sql), {'days': days})
                columns = result.keys()
                rows = result.fetchall()

                # 转换为字典列表
                statistics = []
                for row in rows:
                    stat_dict = dict(zip(columns, row))
                    statistics.append(stat_dict)

                return statistics

        except Exception as e:
            logger.error(f"获取交易统计数据失败: {str(e)}")
            return []

    def format_statistics_message(self, stats: List[Dict[str, Any]], report_type: str = "daily") -> str:
        """格式化统计消息"""
        if not stats:
            return "📊 暂无交易统计数据"

        # 获取最新一天的数据作为今日统计
        today_stats = stats[0] if stats else None

        # 获取资金对比信息
        funds_comparison = self.get_funds_comparison()

        if report_type == "daily" and today_stats:
            # 每日统计报告
            message = f"""# 📊 每日交易统计报告
**日期**: {today_stats.get('trade_date', 'N/A')}
**时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📈 基础数据
- **总订单数**: {today_stats.get('total_orders', 0)}
- **盈利单数**: <font color="info">{today_stats.get('profit_orders', 0)}</font>
- **亏损单数**: <font color="warning">{today_stats.get('loss_orders', 0)}</font>
- **平手单数**: {today_stats.get('breakeven_orders', 0)}

## 💰 盈亏分析
- **总盈亏**: <font color="{'info' if (today_stats.get('total_pnl', 0) or 0) >= 0 else 'warning'}">{today_stats.get('total_pnl', 0)} USDT</font>
- **总盈利**: <font color="info">{today_stats.get('total_profit', 0)} USDT</font>
- **总亏损**: <font color="warning">{today_stats.get('total_loss', 0)} USDT</font>
- **平均盈亏**: {today_stats.get('avg_pnl', 0)} USDT
- **胜率**: <font color="{'info' if (today_stats.get('win_rate_percent', 0) or 0) >= 50 else 'warning'}">{today_stats.get('win_rate_percent', 0)}%</font>
- **盈亏比**: {today_stats.get('profit_loss_ratio', 0)}

## 📊 交易详情
- **总交易量**: {today_stats.get('total_volume', 0)}
- **平均订单大小**: {today_stats.get('avg_order_size', 0)}
- **总手续费**: {today_stats.get('total_fees', 0)} USDT
- **买单/卖单**: {today_stats.get('buy_orders', 0)}/{today_stats.get('sell_orders', 0)}
- **市价/限价**: {today_stats.get('market_orders', 0)}/{today_stats.get('limit_orders', 0)}

## 🎯 极值数据
- **最大盈利**: <font color="info">{today_stats.get('max_profit', 0)} USDT</font>
- **最大亏损**: <font color="warning">{today_stats.get('max_loss', 0)} USDT</font>
- **平均成交价**: {today_stats.get('avg_fill_price', 0)}

{funds_comparison}
"""
        else:
            # 周期性汇总报告
            total_pnl = sum([s.get('total_pnl', 0) or 0 for s in stats])
            total_orders = sum([s.get('total_orders', 0) or 0 for s in stats])
            total_profit_orders = sum([s.get('profit_orders', 0) or 0 for s in stats])
            total_loss_orders = sum([s.get('loss_orders', 0) or 0 for s in stats])
            overall_win_rate = (total_profit_orders * 100.0 / (total_profit_orders + total_loss_orders)) if (total_profit_orders + total_loss_orders) > 0 else 0

            message = f"""# 📊 交易统计汇总报告
**统计周期**: 最近{len(stats)}天
**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📈 汇总数据
- **总交易天数**: {len(stats)}
- **总订单数**: {total_orders}
- **总盈亏**: <font color="{'info' if total_pnl >= 0 else 'warning'}">{total_pnl:.2f} USDT</font>
- **整体胜率**: <font color="{'info' if overall_win_rate >= 50 else 'warning'}">{overall_win_rate:.2f}%</font>
- **盈利天数**: {len([s for s in stats if (s.get('total_pnl', 0) or 0) > 0])}
- **亏损天数**: {len([s for s in stats if (s.get('total_pnl', 0) or 0) < 0])}

## 📊 每日详细数据

```
    日期        订单  盈利  亏损  总盈亏(USDT)  胜率(%)  盈亏比   手续费(USDT)
"""

            # 添加每日详细数据表格
            for stat in stats:
                date = stat.get('trade_date', 'N/A')
                total_orders_day = stat.get('total_orders', 0) or 0
                profit_orders = stat.get('profit_orders', 0) or 0
                loss_orders = stat.get('loss_orders', 0) or 0
                pnl = stat.get('total_pnl', 0) or 0
                win_rate = stat.get('win_rate_percent', 0) or 0
                profit_loss_ratio = stat.get('profit_loss_ratio', 0) or 0
                total_fees = stat.get('total_fees', 0) or 0

                # 格式化数据，保持对齐
                pnl_str = f"{pnl:+8.2f}" if pnl != 0 else "    0.00"
                win_rate_str = f"{win_rate:5.1f}%"
                ratio_str = f"{profit_loss_ratio:6.2f}" if profit_loss_ratio else "  --  "
                fees_str = f"{total_fees:7.2f}"

                message += f"{date}  {total_orders_day:4d}  {profit_orders:4d}  {loss_orders:4d}  {pnl_str:>10}    {win_rate_str:>7}  {ratio_str:>6}   {fees_str:>11}\n"

            message += "```\n"

            # 添加资金对比信息到汇总报告
            message += f"\n{funds_comparison}\n"

        return message

    def send_statistics_notification(self, message: str) -> bool:
        """发送统计通知到企业微信"""
        try:
            # 构建企业微信消息
            wechat_message = {
                "msgtype": "markdown",
                "markdown": {
                    "content": message
                }
            }

            # 发送消息
            headers = {'Content-Type': 'application/json'}
            response = requests.post(WECHAT_ROBOT_URL, headers=headers,
                                     data=json.dumps(wechat_message), timeout=10)

            if response.status_code == 200:
                logger.info("交易统计通知发送成功")
                return True
            else:
                logger.error(f"交易统计通知发送失败: {response.status_code} - {response.text}")
                return False

        except Exception as e:
            logger.error(f"发送交易统计通知失败: {str(e)}")
            return False



    def send_manual_report(self, days: int = 7, report_type: str = "manual"):
        """手动发送统计报告"""
        logger.info(f"手动生成{days}天交易统计报告...")
        stats = self.get_daily_statistics(days=days)
        if stats:
            message = self.format_statistics_message(stats, report_type)
            return self.send_statistics_notification(message)
        else:
            logger.info(f"最近{days}天暂无交易数据")
            return False


    def get_acount_info(self):
        """获取当前账户信息"""
        try:
            clent = Client(
                api_key='bg_44025411ed4ba2c0032ac4e2dff2b18e',
                api_secret_key='d30fa4f8e6d80462659ed7e525d489020413a4e9d11cd16a3c8f6ceeccd87500',
                passphrase='**********'
            )
            acc = clent.mix_get_accounts(productType='SUMCBL')
            return acc
        except Exception as e:
            logger.error(f"获取账户信息失败: {str(e)}")
            return None

    def get_funds_comparison(self) -> str:
        """获取资金对比信息"""
        try:
            # 获取当前实时资金
            current_account = self.get_acount_info()

            if not current_account or current_account.get('code') != '00000':
                return "## 💰 资金对比\n- **状态**: 无法获取当前资金信息"

            current_data = current_account.get('data', [{}])[0]
            original_data = original_funds_data.get('data', [{}])[0]

            # 提取关键资金数据
            original_equity = float(original_data.get('usdtEquity', 0))
            current_equity = float(current_data.get('usdtEquity', 0))

            original_available = float(original_data.get('available', 0))
            current_available = float(current_data.get('available', 0))

            original_unrealized = float(original_data.get('unrealizedPL', 0))
            current_unrealized = float(current_data.get('unrealizedPL', 0))

            # 计算变化
            equity_change = current_equity - original_equity
            available_change = current_available - original_available
            unrealized_change = current_unrealized - original_unrealized

            # 计算收益率
            equity_return_rate = (equity_change / original_equity * 100) if original_equity > 0 else 0

            # 格式化消息
            equity_color = "info" if equity_change >= 0 else "warning"
            available_color = "info" if available_change >= 0 else "warning"
            unrealized_color = "info" if current_unrealized >= 0 else "warning"

            comparison_message = f"""## 💰 资金对比分析
### 📊 总权益对比
- **原始权益**: {original_equity:.2f} USDT
- **当前权益**: {current_equity:.2f} USDT
- **权益变化**: <font color="{equity_color}">{equity_change:+.2f} USDT ({equity_return_rate:+.2f}%)</font>

### 💵 可用资金对比
- **原始可用**: {original_available:.2f} USDT
- **当前可用**: {current_available:.2f} USDT
- **可用变化**: <font color="{available_color}">{available_change:+.2f} USDT</font>

### 📈 未实现盈亏对比
- **原始未实现**: {original_unrealized:.2f} USDT
- **当前未实现**: <font color="{unrealized_color}">{current_unrealized:.2f} USDT</font>
- **未实现变化**: {unrealized_change:+.2f} USDT

### 🎯 总体表现
- **总盈亏**: <font color="{equity_color}">{equity_change:+.2f} USDT</font>
- **收益率**: <font color="{equity_color}">{equity_return_rate:+.2f}%</font>
- **风险率**: {float(current_data.get('crossRiskRate', 0)) * 100:.4f}%"""

            return comparison_message

        except Exception as e:
            logger.error(f"获取资金对比信息失败: {str(e)}")
            return "## 💰 资金对比\n- **状态**: 资金对比计算失败"


# 全局统计推送器实例
_statistics_notifier_instance = None

def get_statistics_notifier() -> TradingStatisticsNotifier:
    """获取全局统计推送器实例"""
    global _statistics_notifier_instance
    if _statistics_notifier_instance is None:
        _statistics_notifier_instance = TradingStatisticsNotifier()
    return _statistics_notifier_instance


if __name__ == '__main__':
    # 测试统计推送功能
    notifier = get_statistics_notifier()

    # 发送测试报告
    print("发送测试统计报告...")
    success = notifier.send_manual_report(days=7)
    print(f"发送结果: {'成功' if success else '失败'}")