"""订单数据库映射器 - 管理订单相关的数据库操作"""

from sqlalchemy import create_engine, Column, String, Text, Float, Integer, DateTime, Boolean, JSON
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker, scoped_session
from contextlib import contextmanager
from datetime import datetime
from typing import Dict, Any, Optional, List
import json
import yaml
import os

Base = declarative_base()

class OrderRecord(Base):
    """订单记录表"""
    __tablename__ = 'order_records'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    order_id = Column(String(100), nullable=False, unique=True)  # 交易所订单ID
    client_order_id = Column(String(100))  # 客户端订单ID
    
    # 基本订单信息
    inst_id = Column(String(20), nullable=False)  # 交易对
    side = Column(String(10), nullable=False)  # buy/sell
    order_type = Column(String(20))  # market/limit/stop等
    size = Column(Float, nullable=False)  # 订单数量
    price = Column(Float)  # 订单价格
    
    # 订单状态
    status = Column(String(20), nullable=False)  # new/filled/partial_filled/cancelled等
    fill_price = Column(Float)  # 成交价格
    fill_size = Column(Float)  # 成交数量
    fill_fee = Column(Float)  # 手续费
    fill_fee_currency = Column(String(10))  # 手续费币种
    
    # 盈亏信息
    pnl = Column(Float, default=0.0)  # 盈亏
    pnl_ratio = Column(Float, default=0.0)  # 盈亏比例
    
    # 时间信息
    order_time = Column(DateTime)  # 下单时间
    fill_time = Column(DateTime)  # 成交时间
    update_time = Column(DateTime)  # 更新时间
    
    # 原始数据
    raw_data = Column(JSON)  # 原始订单数据
    
    # 系统信息
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)
    
    # 通知状态
    notification_sent = Column(Boolean, default=False)  # 是否已发送通知
    notification_time = Column(DateTime)  # 通知发送时间


class OrderMapper:
    """订单数据库映射器"""
    
    def __init__(self, config_path: str = None):
        """初始化订单映射器"""
        self.config = self._load_config(config_path)
        self.engine = self._create_engine()
        self.Session = scoped_session(sessionmaker(
            bind=self.engine,
            expire_on_commit=False
        ))
        
    def _load_config(self, config_path: Optional[str] = None) -> Dict[str, Any]:
        """加载配置文件"""
        if config_path is None:
            config_path = os.path.join(os.path.dirname(__file__), '../config/trader_config.yaml')
            
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            # 使用默认配置
            return {
                'database': {
                    'host': '*************',
                    'port': '6221',
                    'user': 'Mysql5.7',
                    'password': 'a78d04a8027589c3',
                    'name': 'Mysql5.7'
                }
            }
    
    def _create_engine(self):
        """创建数据库引擎"""
        db_config = self.config['database']
        db_url = f"mysql+pymysql://{db_config['user']}:{db_config['password']}@{db_config['host']}:{db_config['port']}/{db_config['name']}"
        return create_engine(db_url, echo=False, pool_pre_ping=True, pool_recycle=3600)
    
    def init_db(self):
        """初始化数据库表"""
        Base.metadata.create_all(self.engine)
        
    @contextmanager
    def session_scope(self):
        """提供数据库会话的上下文管理器"""
        session = self.Session()
        try:
            yield session
            session.commit()
        except Exception as e:
            session.rollback()
            raise e
        finally:
            session.close()
    
    def save_order(self, order_data: Dict[str, Any]) -> bool:
        """保存订单数据"""
        try:
            with self.session_scope() as session:
                # 检查订单是否已存在
                order_id = order_data.get('ordId', order_data.get('orderId'))
                if not order_id:
                    return False
                    
                existing_order = session.query(OrderRecord).filter_by(order_id=order_id).first()
                
                if existing_order:
                    # 更新现有订单
                    self._update_order_record(existing_order, order_data)
                else:
                    # 创建新订单记录
                    order_record = self._create_order_record(order_data)
                    session.add(order_record)
                    
                return True
                
        except Exception as e:
            print(f"保存订单数据失败: {str(e)}")
            return False
    
    def _create_order_record(self, order_data: Dict[str, Any]) -> OrderRecord:
        """创建订单记录"""
        return OrderRecord(
            order_id=order_data.get('ordId', order_data.get('orderId', '')),
            client_order_id=order_data.get('clOrdId', order_data.get('clientOid', '')),
            inst_id=order_data.get('instId', ''),
            side=order_data.get('side', ''),
            order_type=order_data.get('ordType', order_data.get('orderType', '')),
            size=float(order_data.get('sz', order_data.get('size', 0))),
            price=float(order_data.get('px', order_data.get('price', 0))) if order_data.get('px', order_data.get('price')) else None,
            status=order_data.get('status', ''),
            fill_price=float(order_data.get('fillPrice', 0)) if order_data.get('fillPrice') else None,
            fill_size=float(order_data.get('fillSize', 0)) if order_data.get('fillSize') else None,
            fill_fee=float(order_data.get('fillFee', 0)) if order_data.get('fillFee') else None,
            fill_fee_currency=order_data.get('fillFeeCcy', ''),
            pnl=float(order_data.get('pnl', 0)) if order_data.get('pnl') else 0.0,
            pnl_ratio=float(order_data.get('pnlRatio', 0)) if order_data.get('pnlRatio') else 0.0,
            order_time=self._parse_timestamp(order_data.get('cTime')),
            fill_time=self._parse_timestamp(order_data.get('fillTime')),
            update_time=self._parse_timestamp(order_data.get('uTime')),
            raw_data=order_data
        )
    
    def _update_order_record(self, order_record: OrderRecord, order_data: Dict[str, Any]):
        """更新订单记录"""
        order_record.status = order_data.get('status', order_record.status)
        order_record.fill_price = float(order_data.get('fillPrice', 0)) if order_data.get('fillPrice') else order_record.fill_price
        order_record.fill_size = float(order_data.get('fillSize', 0)) if order_data.get('fillSize') else order_record.fill_size
        order_record.fill_fee = float(order_data.get('fillFee', 0)) if order_data.get('fillFee') else order_record.fill_fee
        order_record.fill_fee_currency = order_data.get('fillFeeCcy', order_record.fill_fee_currency)
        order_record.pnl = float(order_data.get('pnl', 0)) if order_data.get('pnl') else order_record.pnl
        order_record.pnl_ratio = float(order_data.get('pnlRatio', 0)) if order_data.get('pnlRatio') else order_record.pnl_ratio
        order_record.fill_time = self._parse_timestamp(order_data.get('fillTime')) or order_record.fill_time
        order_record.update_time = self._parse_timestamp(order_data.get('uTime')) or order_record.update_time
        order_record.raw_data = order_data
        order_record.updated_at = datetime.now()
    
    def _parse_timestamp(self, timestamp_str: str) -> Optional[datetime]:
        """解析时间戳"""
        if not timestamp_str:
            return None
        try:
            # 假设时间戳是毫秒级别的
            timestamp = int(timestamp_str) / 1000
            return datetime.fromtimestamp(timestamp)
        except (ValueError, TypeError):
            return None
    
    def mark_notification_sent(self, order_id: str) -> bool:
        """标记通知已发送"""
        try:
            with self.session_scope() as session:
                order_record = session.query(OrderRecord).filter_by(order_id=order_id).first()
                if order_record:
                    order_record.notification_sent = True
                    order_record.notification_time = datetime.now()
                    return True
                return False
        except Exception as e:
            print(f"标记通知状态失败: {str(e)}")
            return False
    
    def get_order_by_id(self, order_id: str) -> Optional[OrderRecord]:
        """根据订单ID获取订单记录"""
        try:
            with self.session_scope() as session:
                return session.query(OrderRecord).filter_by(order_id=order_id).first()
        except Exception as e:
            print(f"获取订单记录失败: {str(e)}")
            return None
    
    def get_orders_by_status(self, status: str, limit: int = 100) -> List[OrderRecord]:
        """根据状态获取订单列表"""
        try:
            with self.session_scope() as session:
                return session.query(OrderRecord).filter_by(status=status).limit(limit).all()
        except Exception as e:
            print(f"获取订单列表失败: {str(e)}")
            return []
    
    def get_recent_orders(self, inst_id: str = None, limit: int = 50) -> List[OrderRecord]:
        """获取最近的订单记录"""
        try:
            with self.session_scope() as session:
                query = session.query(OrderRecord)
                if inst_id:
                    query = query.filter_by(inst_id=inst_id)
                return query.order_by(OrderRecord.created_at.desc()).limit(limit).all()
        except Exception as e:
            print(f"获取最近订单失败: {str(e)}")
            return []
    
    def get_order_statistics(self, inst_id: str = None, days: int = 7) -> Dict[str, Any]:
        """获取订单统计信息"""
        try:
            with self.session_scope() as session:
                from sqlalchemy import func
                from datetime import timedelta
                
                start_date = datetime.now() - timedelta(days=days)
                query = session.query(OrderRecord).filter(OrderRecord.created_at >= start_date)
                
                if inst_id:
                    query = query.filter_by(inst_id=inst_id)
                
                orders = query.all()
                
                total_orders = len(orders)
                filled_orders = len([o for o in orders if o.status == 'filled'])
                total_pnl = sum([o.pnl for o in orders if o.pnl])
                total_fees = sum([o.fill_fee for o in orders if o.fill_fee])
                
                return {
                    'total_orders': total_orders,
                    'filled_orders': filled_orders,
                    'fill_rate': filled_orders / total_orders if total_orders > 0 else 0,
                    'total_pnl': total_pnl,
                    'total_fees': total_fees,
                    'avg_pnl_per_order': total_pnl / filled_orders if filled_orders > 0 else 0
                }
        except Exception as e:
            print(f"获取订单统计失败: {str(e)}")
            return {}


# 全局订单映射器实例
_order_mapper_instance = None

def get_order_mapper() -> OrderMapper:
    """获取全局订单映射器实例"""
    global _order_mapper_instance
    if _order_mapper_instance is None:
        _order_mapper_instance = OrderMapper()
        _order_mapper_instance.init_db()
    return _order_mapper_instance