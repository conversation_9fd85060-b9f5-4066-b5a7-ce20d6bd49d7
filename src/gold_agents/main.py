#!/usr/bin/env python
import sys
import warnings
import os
from datetime import datetime
from dotenv import load_dotenv
import logging

from crew import GoldAgents

warnings.filterwarnings("ignore", category=SyntaxWarning, module="pysbd")

# Load environment variables
load_dotenv()

# 配置日志
def setup_main_logger():
    logger = logging.getLogger(__name__)
    logger.setLevel(logging.INFO)  # 设置日志级别为INFO

    # 创建控制台处理器
    console_handler = logging.StreamHandler(sys.stdout)
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logger.addHandler(console_handler)

    return logger

# 初始化日志记录器
logger = setup_main_logger()

# This main file is intended to be a way for you to run your
# crew locally, so refrain from adding unnecessary logic into this file.
# Replace with inputs you want to test with, it will automatically
# interpolate any tasks and agents information

def run():
    """
    Run function for crewai run command compatibility
    """
    return main()


def main():
    """
    Main function to run the Gold Trading crew
    """
    try:
        # 数据库配置


        # Create an instance of GoldAgents with db_config
        gold_agents = GoldAgents()

        # Get the crew
        crew = gold_agents.crew()

        # Kick off the crew
        result = crew.kickoff()

        logger.info("Gold Trading crew execution completed successfully!")
        logger.info(f"Result: {result}")

        return result

    except Exception as e:
        logger.error(f"An error occurred during gold trading crew execution: {e}", exc_info=True)
        raise


def train():
    """
    Train the crew for a given number of iterations.
    """
    inputs = {
        "topic": "Gold Trading Analysis"
    }
    try:


        GoldAgents().crew().train(n_iterations=int(sys.argv[1]), filename=sys.argv[2], inputs=inputs)

    except Exception as e:
        logger.error(f"An error occurred while training the crew: {e}", exc_info=True)
        raise Exception(f"An error occurred while training the crew: {e}")


def replay():
    """
    Replay the crew execution from a specific task.
    """
    try:
        # 数据库配置


        GoldAgents().crew().replay(task_id=sys.argv[1])

    except Exception as e:
        logger.error(f"An error occurred while replaying the crew: {e}", exc_info=True)
        raise Exception(f"An error occurred while replaying the crew: {e}")


def test():
    """
    Test the crew execution and returns the results.
    """
    inputs = {
        "topic": "Gold Trading Analysis"
    }
    try:


        GoldAgents().crew().test(n_iterations=int(sys.argv[1]), openai_model_name=sys.argv[2], inputs=inputs)

    except Exception as e:
        logger.error(f"An error occurred while testing the crew: {e}", exc_info=True)
        raise Exception(f"An error occurred while testing the crew: {e}")


if __name__ == "__main__":
    main()
