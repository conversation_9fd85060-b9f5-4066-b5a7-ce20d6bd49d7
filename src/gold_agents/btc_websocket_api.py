from pybitget.stream import BitgetWsClient, SubscribeReq
from pybitget import logger
import json
import sys
import os
import requests
import time
import threading
from datetime import datetime
import logging
from logging.handlers import RotatingFileHandler, TimedRotatingFileHandler

from price_monitor import process_websocket_message
from order_mapper import get_order_mapper
from pybitget.enums import *

api_key = "bg_44025411ed4ba2c0032ac4e2dff2b18e"
api_secret = "d30fa4f8e6d80462659ed7e525d489020413a4e9d11cd16a3c8f6ceeccd87500"
api_passphrase = "1137285095"

# 重连配置
MAX_RETRIES = 10
RETRY_DELAY = 10
HEARTBEAT_INTERVAL = 30

# 全局变量
client = None
is_running = False
last_message_time = time.time()
reconnect_lock = threading.Lock()

# 企业微信机器人配置
WECHAT_ROBOT_URL = 'https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=22f9bc8d-bc61-45f2-beac-d996dbe425d3'

# 日志分片配置
LOG_DIR = "logs"
LOG_MAX_SIZE = 10 * 1024 * 1024  # 10MB per log file
LOG_BACKUP_COUNT = 10  # Keep 10 backup files
LOG_ROTATION_INTERVAL = "midnight"  # Daily rotation
LOG_RETENTION_DAYS = 30  # Keep logs for 30 days

def setup_sharded_logging():
    """设置日志分片系统"""
    try:
        # 创建日志目录
        if not os.path.exists(LOG_DIR):
            os.makedirs(LOG_DIR)
        
        # 获取根logger
        root_logger = logging.getLogger()
        root_logger.setLevel(logging.INFO)
        
        # 清除现有的handlers
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)
        
        # 设置日志格式
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            datefmt='%Y-%m-%d %H:%M:%S'
        )
        
        # 1. 基于大小的轮转日志 - 主要日志
        size_handler = RotatingFileHandler(
            filename=os.path.join(LOG_DIR, 'btc_websocket.log'),
            maxBytes=LOG_MAX_SIZE,
            backupCount=LOG_BACKUP_COUNT,
            encoding='utf-8'
        )
        size_handler.setLevel(logging.INFO)
        size_handler.setFormatter(formatter)
        root_logger.addHandler(size_handler)
        
        # 2. 基于时间的轮转日志 - 每日归档
        time_handler = TimedRotatingFileHandler(
            filename=os.path.join(LOG_DIR, 'btc_websocket_daily.log'),
            when=LOG_ROTATION_INTERVAL,
            interval=1,
            backupCount=LOG_RETENTION_DAYS,
            encoding='utf-8'
        )
        time_handler.setLevel(logging.INFO)
        time_handler.setFormatter(formatter)
        time_handler.suffix = "%Y-%m-%d"
        root_logger.addHandler(time_handler)
        
        # 3. 错误日志单独记录
        error_handler = RotatingFileHandler(
            filename=os.path.join(LOG_DIR, 'btc_websocket_error.log'),
            maxBytes=LOG_MAX_SIZE,
            backupCount=LOG_BACKUP_COUNT,
            encoding='utf-8'
        )
        error_handler.setLevel(logging.ERROR)
        error_handler.setFormatter(formatter)
        root_logger.addHandler(error_handler)
        
        # 4. 订单日志单独记录
        order_logger = logging.getLogger('order')
        order_handler = RotatingFileHandler(
            filename=os.path.join(LOG_DIR, 'orders.log'),
            maxBytes=LOG_MAX_SIZE,
            backupCount=LOG_BACKUP_COUNT,
            encoding='utf-8'
        )
        order_handler.setLevel(logging.INFO)
        order_handler.setFormatter(formatter)
        order_logger.addHandler(order_handler)
        order_logger.setLevel(logging.INFO)
        order_logger.propagate = False  # 防止重复记录
        
        # 5. WebSocket连接日志
        ws_logger = logging.getLogger('websocket')
        ws_handler = RotatingFileHandler(
            filename=os.path.join(LOG_DIR, 'websocket_connection.log'),
            maxBytes=LOG_MAX_SIZE,
            backupCount=LOG_BACKUP_COUNT,
            encoding='utf-8'
        )
        ws_handler.setLevel(logging.INFO)
        ws_handler.setFormatter(formatter)
        ws_logger.addHandler(ws_handler)
        ws_logger.setLevel(logging.INFO)
        ws_logger.propagate = False
        
        # 6. 控制台输出（可选）
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        console_handler.setFormatter(logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s',
            datefmt='%H:%M:%S'
        ))
        root_logger.addHandler(console_handler)
        
        logger.info("日志分片系统初始化完成")
        logger.info(f"日志目录: {os.path.abspath(LOG_DIR)}")
        logger.info(f"单文件最大大小: {LOG_MAX_SIZE / 1024 / 1024:.1f}MB")
        logger.info(f"备份文件数量: {LOG_BACKUP_COUNT}")
        logger.info(f"日志保留天数: {LOG_RETENTION_DAYS}")
        
        return True
        
    except Exception as e:
        print(f"设置日志分片失败: {str(e)}")
        return False

def get_order_logger():
    """获取订单专用logger"""
    return logging.getLogger('order')

def get_websocket_logger():
    """获取WebSocket专用logger"""
    return logging.getLogger('websocket')

def cleanup_old_logs():
    """清理过期日志文件"""
    try:
        if not os.path.exists(LOG_DIR):
            return
        
        current_time = time.time()
        cutoff_time = current_time - (LOG_RETENTION_DAYS * 24 * 3600)
        
        for filename in os.listdir(LOG_DIR):
            filepath = os.path.join(LOG_DIR, filename)
            if os.path.isfile(filepath):
                file_time = os.path.getmtime(filepath)
                if file_time < cutoff_time:
                    try:
                        os.remove(filepath)
                        logger.info(f"已删除过期日志文件: {filename}")
                    except Exception as e:
                        logger.error(f"删除日志文件失败 {filename}: {str(e)}")
                        
    except Exception as e:
        logger.error(f"清理日志文件失败: {str(e)}")

def send_order_notification(order_data):
    """发送订单通知到企业微信"""
    try:
        # 解析订单数据
        inst_id = order_data.get('instId', 'Unknown')
        side = order_data.get('side', 'Unknown')
        size = order_data.get('sz', order_data.get('size', '0'))
        price = order_data.get('fillPrice', order_data.get('px', order_data.get('price', '0')))
        status = order_data.get('status', 'Unknown')
        order_id = order_data.get('ordId', order_data.get('orderId', 'Unknown'))
        pnl = order_data.get('pnl', '0')
        fill_fee = order_data.get('fillFee', '0')
        
        # 格式化价格和数量
        try:
            price_float = float(price)
            size_float = float(size)
            pnl_float = float(pnl)
            fee_float = float(fill_fee)
        except:
            price_float = 0
            size_float = 0
            pnl_float = 0
            fee_float = 0
        
        # 确定订单类型和状态的中文描述
        side_cn = "买入" if side == "buy" else "卖出"
        status_cn = {
            "filled": "已成交",
            "partial_filled": "部分成交", 
            "cancelled": "已取消",
            "new": "新订单"
        }.get(status, status)
        
        # 构建企业微信消息
        message = {
            "msgtype": "markdown",
            "markdown": {
                "content": (
                    f"# 📋 订单通知\n"
                    f"**时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n"
                    f"**交易对**: {inst_id}\n"
                    f"**订单ID**: {order_id}\n"
                    f"**操作**: <font color=\"{'info' if side == 'buy' else 'warning'}\">{side_cn}</font>\n"
                    f"**数量**: {size_float:.4f}\n"
                    f"**价格**: {price_float:.2f}\n"
                    f"**状态**: <font color=\"{'info' if status == 'filled' else 'warning'}\">{status_cn}</font>\n"
                    f"**盈亏**: <font color=\"{'info' if pnl_float >= 0 else 'warning'}\">{pnl_float:.2f} USDT</font>\n"
                    f"**手续费**: {fee_float:.4f} USDT\n\n"
                )
            }
        }
        
        # 发送消息
        headers = {'Content-Type': 'application/json'}
        response = requests.post(WECHAT_ROBOT_URL, headers=headers, 
                               data=json.dumps(message), timeout=10)
        
        if response.status_code == 200:
            logger.info(f"订单通知发送成功: {inst_id} {side_cn} {status_cn}")
            return True
        else:
            logger.error(f"订单通知发送失败: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        logger.error(f"发送订单通知失败: {str(e)}")
        return False

def create_client():
    """创建新的WebSocket客户端"""
    return BitgetWsClient(
        api_key=api_key,
        api_secret=api_secret,
        passphrase=api_passphrase,
        verbose=False  # 减少日志输出
    ).error_listener(handle_error).build()

def handle_error(error):
    """处理WebSocket错误"""
    global is_running
    ws_logger = get_websocket_logger()
    ws_logger.error(f"WebSocket错误: {str(error)}")
    logger.error(f"WebSocket错误: {str(error)}")
    is_running = False
    # 不在这里重连，让主循环处理

def on_message(message):
    """处理WebSocket消息"""
    global last_message_time
    last_message_time = time.time()
    
    try:
        if isinstance(message, str):
            message = json.loads(message)
        
        if not message.get("arg"):
            return
            
        channel = message.get("arg").get("channel")
        if channel == "candle1m":
            # 调用price_monitor处理K线数据
            try:
                process_websocket_message(message)
            except Exception as e:
                logger.error(f"处理price_monitor消息失败: {str(e)}")
        elif channel == "orders":
            order_logger = get_order_logger()
            order_logger.info(f"收到订单消息: {message}")
            logger.info(f"order：{message}")
            # 处理订单数据：保存到数据库并发送企业微信通知
            try:
                data = message.get('data', [])
                if data and isinstance(data, list):
                    order_mapper = get_order_mapper()
                    for order in data:
                        order_id = order.get('ordId', order.get('orderId', 'Unknown'))
                        inst_id = order.get('instId', 'Unknown')
                        side = order.get('side', 'Unknown')
                        status = order.get('status', 'Unknown')
                        
                        # 记录详细订单信息到专用日志
                        order_logger.info(f"处理订单: ID={order_id}, 交易对={inst_id}, 方向={side}, 状态={status}")
                        
                        # 保存订单数据到数据库
                        save_success = order_mapper.save_order(order)
                        if save_success:
                            order_logger.info(f"订单数据已保存到数据库: {order_id}")
                            logger.info(f"订单数据已保存到数据库: {order_id}")
                        else:
                            order_logger.error(f"订单数据保存失败: {order_id}")
                            logger.error(f"订单数据保存失败: {order_id}")
                        
                        # 发送所有类型订单的通知
                        # notification_success = send_order_notification(order)
                        # if notification_success:
                        #     标记通知已发送
                            # order_mapper.mark_notification_sent(order.get('ordId', order.get('orderId', '')))
                        
                        # order_logger.info(f"订单处理完成: {inst_id} {side} {status} - 数据库:{save_success} 通知:{notification_success}")
            except Exception as e:
                order_logger.error(f"处理订单数据失败: {str(e)}")
                logger.error(f"处理订单数据失败: {str(e)}")
    except Exception as e:
        logger.error(f"处理消息失败: {str(e)}")

def reconnect_websocket():
    """重连WebSocket"""
    global client, is_running
    
    ws_logger = get_websocket_logger()
    
    with reconnect_lock:
        if is_running:
            return True
            
        retry_count = 0
        while retry_count < MAX_RETRIES:
            try:
                ws_logger.info(f"尝试重连WebSocket (第{retry_count + 1}次)...")
                logger.info(f"尝试重连WebSocket (第{retry_count + 1}次)...")
                
                # 清理旧连接
                if client:
                    try:
                        client.close()
                    except:
                        pass
                    client = None
                
                # 等待一段时间再重连
                time.sleep(RETRY_DELAY)
                
                # 创建新连接
                client = create_client()
                
                # 订阅频道
                channels = [SubscribeReq("SUMCBL", "orders", "default")]
                client.subscribe(channels, on_message)
                
                is_running = True
                ws_logger.info("WebSocket重连成功")
                logger.info("WebSocket重连成功")
                return True
                
            except Exception as e:
                retry_count += 1
                ws_logger.error(f"重连失败 (第{retry_count}次): {str(e)}")
                logger.error(f"重连失败 (第{retry_count}次): {str(e)}")
                if retry_count < MAX_RETRIES:
                    time.sleep(RETRY_DELAY * retry_count)  # 指数退避
        
        ws_logger.error(f"重连失败，已达到最大重试次数 {MAX_RETRIES}")
        logger.error(f"重连失败，已达到最大重试次数 {MAX_RETRIES}")
        return False

def heartbeat_monitor():
    """心跳监控线程"""
    global last_message_time, is_running
    
    ws_logger = get_websocket_logger()
    
    while True:
        try:
            time.sleep(HEARTBEAT_INTERVAL)
            
            # if is_running:
            #     # 检查是否长时间没有收到消息
            #     if time.time() - last_message_time > HEARTBEAT_INTERVAL * 2:
            #         ws_logger.warning("长时间未收到消息，可能连接已断开")
            #         logger.warning("长时间未收到消息，可能连接已断开")
            #         is_running = False
            
            # 如果连接断开，尝试重连
            if not is_running:
                ws_logger.info("检测到连接断开，开始重连...")
                logger.info("检测到连接断开，开始重连...")
                reconnect_websocket()
            
            # 定期清理过期日志文件
            if time.time() % (24 * 3600) < HEARTBEAT_INTERVAL:  # 每天执行一次
                cleanup_old_logs()
                
        except Exception as e:
            ws_logger.error(f"心跳监控异常: {str(e)}")
            logger.error(f"心跳监控异常: {str(e)}")
            time.sleep(5)

def start_websocket():
    """启动WebSocket连接"""
    global client, is_running
    
    try:
        # 首先初始化日志分片系统
        if not setup_sharded_logging():
            print("警告: 日志分片系统初始化失败，使用默认日志配置")
        
        ws_logger = get_websocket_logger()
        ws_logger.info("正在启动WebSocket连接...")
        logger.info("正在启动WebSocket连接...")
        
        # 初始连接
        client = create_client()
        channels = [SubscribeReq("SUMCBL", "orders", "default")]
        client.subscribe(channels, on_message)
        
        is_running = True
        ws_logger.info("WebSocket连接已建立")
        logger.info("WebSocket连接已建立")
        
        # 启动心跳监控线程
        monitor_thread = threading.Thread(target=heartbeat_monitor, daemon=True)
        monitor_thread.start()
        ws_logger.info("心跳监控线程已启动")
        logger.info("心跳监控线程已启动")
        
        return True
        
    except Exception as e:
        logger.error(f"启动WebSocket失败: {str(e)}")
        return False

if __name__ == '__main__':
    # 测试数据
    # data = """ {"action":"snapshot","arg":{"instType":"USDT-FUTURES","channel":"orders","instId":"default"},"data":[{"accBaseVolume":"0.01","cTime":"1695718781129","clientOId":"1","feeDetail":[{"feeCoin":"USDT","fee":"-0.162003"}],"fillFee":"-0.162003","fillFeeCoin":"USDT","fillNotionalUsd":"270.005","fillPrice":"27000.5","baseVolume":"0.01","stpMode":"cancel_taker","fillTime":"1695718781146","force":"gtc","instId":"BTCUSDT","leverage":"20","marginCoin":"USDT","marginMode":"crossed","notionalUsd":"270","orderId":"1","orderType":"market","pnl":"0","posMode":"hedge_mode","posSide":"long","price":"0","priceAvg":"27000.5","reduceOnly":"no","side":"buy","size":"0.01","enterPointSource":"WEB","status":"filled","tradeScope":"T","tradeId":"1111111111","tradeSide":"open","presetStopSurplusPrice":"21.4","totalProfits":"11221.45","presetStopLossPrice":"21.5","cancelReason":"normal_cancel","uTime":"1695718781146"}],"ts":1695718781206} """
    
    # 启动WebSocket连接
    if start_websocket():
        logger.info("WebSocket服务已启动，支持自动重连")
        try:
            # 保持主线程运行
            while True:
                time.sleep(1)
        except KeyboardInterrupt:
            logger.info("收到退出信号，正在关闭WebSocket连接...")
            is_running = False
            if client:
                try:
                    client.close()
                except:
                    pass
            logger.info("WebSocket连接已关闭")
    else:
        logger.error("WebSocket服务启动失败")



