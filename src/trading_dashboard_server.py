"""交易统计仪表板后端服务"""

from flask import Flask, jsonify, render_template_string, request, send_from_directory
from flask_cors import CORS
from datetime import datetime, timedelta
from typing import Dict, Any, List
import json
import os
from sqlalchemy import text
from gold_agents.order_mapper import get_order_mapper
from pybitget import logger

app = Flask(__name__)
CORS(app)  # 允许跨域请求

class TradingDashboardServer:
    """交易仪表板服务器"""
    
    def __init__(self):
        self.order_mapper = get_order_mapper()
    
    def get_daily_statistics(self, days: int = 7) -> List[Dict[str, Any]]:
        """获取每日交易统计数据"""
        try:
            with self.order_mapper.session_scope() as session:
                sql = """
                SELECT 
                    DATE(fill_time) as trade_date, 
                    COUNT(*) as total_orders, 
                    COUNT(CASE WHEN pnl > 0 THEN 1 END) as profit_orders, 
                    COUNT(CASE WHEN pnl < 0 THEN 1 END) as loss_orders, 
                    COUNT(CASE WHEN pnl = 0 THEN 1 END) as breakeven_orders, 
                    ROUND(SUM(pnl),2) as total_pnl, 
                    ROUND(SUM(CASE WHEN pnl > 0 THEN pnl ELSE 0 END),2) as total_profit, 
                    ROUND(SUM(CASE WHEN pnl < 0 THEN pnl ELSE 0 END),2) as total_loss, 
                    ROUND(AVG(pnl), 2) as avg_pnl, 
                    ROUND( 
                        COUNT(CASE WHEN pnl > 0 THEN 1 END) * 100.0 / 
                        NULLIF(COUNT(CASE WHEN pnl != 0 THEN 1 END), 0), 
                        2 
                    ) as win_rate_percent, 
                    ROUND( 
                        ABS(AVG(CASE WHEN pnl > 0 THEN pnl END)) / 
                        NULLIF(ABS(AVG(CASE WHEN pnl < 0 THEN pnl END)), 0), 
                        2 
                    ) as profit_loss_ratio, 
                    SUM(fill_size) as total_volume, 
                    AVG(fill_size) as avg_order_size, 
                    AVG(fill_price) as avg_fill_price, 
                    MIN(fill_price) as min_fill_price, 
                    MAX(fill_price) as max_fill_price, 
                    ROUND(SUM(fill_fee),2) as total_fees, 
                    ROUND(AVG(fill_fee),2) as avg_fee_per_order, 
                    COUNT(CASE WHEN side = 'buy' THEN 1 END) as buy_orders, 
                    COUNT(CASE WHEN side = 'sell' THEN 1 END) as sell_orders, 
                    COUNT(CASE WHEN order_type = 'market' THEN 1 END) as market_orders, 
                    COUNT(CASE WHEN order_type = 'limit' THEN 1 END) as limit_orders, 
                    MAX(pnl) as max_profit, 
                    MIN(pnl) as max_loss, 
                    AVG(pnl_ratio) as avg_pnl_ratio, 
                    MAX(pnl_ratio) as max_pnl_ratio, 
                    MIN(pnl_ratio) as min_pnl_ratio 
                FROM order_records 
                WHERE fill_time IS NOT NULL   
                    AND DATE(fill_time) >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
                GROUP BY DATE(fill_time) 
                ORDER BY trade_date DESC
                """
                
                result = session.execute(text(sql), {'days': days})
                columns = result.keys()
                rows = result.fetchall()
                
                statistics = []
                for row in rows:
                    stat_dict = dict(zip(columns, row))
                    # 转换日期为字符串
                    if stat_dict.get('trade_date'):
                        stat_dict['trade_date'] = stat_dict['trade_date'].strftime('%Y-%m-%d')
                    statistics.append(stat_dict)
                    
                return statistics
                
        except Exception as e:
            logger.error(f"获取交易统计数据失败: {str(e)}")
            return []
    
    def get_hourly_statistics(self, days: int = 7) -> List[Dict[str, Any]]:
        """获取每小时交易统计数据"""
        try:
            with self.order_mapper.session_scope() as session:
                sql = """
                SELECT 
                    DATE(order_time) AS date,
                    SUM(IF(HOUR(order_time) = 0, 1, 0)) AS `0h`,
                    SUM(IF(HOUR(order_time) = 1, 1, 0)) AS `1h`,
                    SUM(IF(HOUR(order_time) = 2, 1, 0)) AS `2h`,
                    SUM(IF(HOUR(order_time) = 3, 1, 0)) AS `3h`,
                    SUM(IF(HOUR(order_time) = 4, 1, 0)) AS `4h`,
                    SUM(IF(HOUR(order_time) = 5, 1, 0)) AS `5h`,
                    SUM(IF(HOUR(order_time) = 6, 1, 0)) AS `6h`,
                    SUM(IF(HOUR(order_time) = 7, 1, 0)) AS `7h`,
                    SUM(IF(HOUR(order_time) = 8, 1, 0)) AS `8h`,
                    SUM(IF(HOUR(order_time) = 9, 1, 0)) AS `9h`,
                    SUM(IF(HOUR(order_time) = 10, 1, 0)) AS `10h`,
                    SUM(IF(HOUR(order_time) = 11, 1, 0)) AS `11h`,
                    SUM(IF(HOUR(order_time) = 12, 1, 0)) AS `12h`,
                    SUM(IF(HOUR(order_time) = 13, 1, 0)) AS `13h`,
                    SUM(IF(HOUR(order_time) = 14, 1, 0)) AS `14h`,
                    SUM(IF(HOUR(order_time) = 15, 1, 0)) AS `15h`,
                    SUM(IF(HOUR(order_time) = 16, 1, 0)) AS `16h`,
                    SUM(IF(HOUR(order_time) = 17, 1, 0)) AS `17h`,
                    SUM(IF(HOUR(order_time) = 18, 1, 0)) AS `18h`,
                    SUM(IF(HOUR(order_time) = 19, 1, 0)) AS `19h`,
                    SUM(IF(HOUR(order_time) = 20, 1, 0)) AS `20h`,
                    SUM(IF(HOUR(order_time) = 21, 1, 0)) AS `21h`,
                    SUM(IF(HOUR(order_time) = 22, 1, 0)) AS `22h`,
                    SUM(IF(HOUR(order_time) = 23, 1, 0)) AS `23h`
                FROM order_records
                WHERE 
                    order_time IS NOT NULL
                    AND DATE(order_time) >= DATE_SUB(CURDATE(), INTERVAL :days DAY)
                GROUP BY DATE(order_time)
                ORDER BY date DESC
                """
                
                result = session.execute(text(sql), {'days': days})
                columns = result.keys()
                rows = result.fetchall()
                
                statistics = []
                for row in rows:
                    stat_dict = dict(zip(columns, row))
                    # 转换日期为字符串
                    if stat_dict.get('date'):
                        stat_dict['date'] = stat_dict['date'].strftime('%Y-%m-%d')
                    statistics.append(stat_dict)
                    
                return statistics
                
        except Exception as e:
            logger.error(f"获取小时统计数据失败: {str(e)}")
            return []

# 创建服务器实例
dashboard_server = TradingDashboardServer()

@app.route('/')
def index():
    """主页面"""
    try:
        # 获取当前文件所在目录
        current_dir = os.path.dirname(os.path.abspath(__file__))
        html_path = os.path.join(current_dir, 'trading_dashboard.html')
        
        with open(html_path, 'r', encoding='utf-8') as f:
            html_content = f.read()
        return html_content
    except FileNotFoundError:
        return "Dashboard HTML file not found", 404

@app.route('/echarts.min.js')
def echarts_js():
    """提供 ECharts 静态文件"""
    try:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        return send_from_directory(current_dir, 'echarts.min.js')
    except FileNotFoundError:
        return "ECharts file not found", 404

@app.route('/static/<path:filename>')
def static_files(filename):
    """提供静态文件"""
    return send_from_directory('.', filename)

@app.route('/api/daily-stats')
def get_daily_stats():
    """获取每日统计数据API"""
    try:
        days = int(request.args.get('days', 7))
        stats = dashboard_server.get_daily_statistics(days)
        return jsonify({
            'success': True,
            'data': stats,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"API获取每日统计失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/hourly-stats')
def get_hourly_stats():
    """获取小时统计数据API"""
    try:
        days = int(request.args.get('days', 7))
        stats = dashboard_server.get_hourly_statistics(days)
        return jsonify({
            'success': True,
            'data': stats,
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"API获取小时统计失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

@app.route('/api/combined-stats')
def get_combined_stats():
    """获取综合统计数据API"""
    try:
        days = int(request.args.get('days', 7))
        daily_stats = dashboard_server.get_daily_statistics(days)
        hourly_stats = dashboard_server.get_hourly_statistics(days)
        
        return jsonify({
            'success': True,
            'data': {
                'dailyStats': daily_stats,
                'hourlyStats': hourly_stats
            },
            'timestamp': datetime.now().isoformat()
        })
    except Exception as e:
        logger.error(f"API获取综合统计失败: {str(e)}")
        return jsonify({
            'success': False,
            'error': str(e),
            'timestamp': datetime.now().isoformat()
        }), 500

if __name__ == '__main__':
    print("启动交易统计仪表板服务器...")
    print("访问地址: http://localhost:5000")
    app.run(host='0.0.0.0', port=5000, debug=True)