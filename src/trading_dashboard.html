<!DOCTYPE html>
<html lang="zh-CN">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>交易统计仪表板</title>
    <script src="/echarts.min.js"></script>
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
        }

        .header {
            text-align: center;
            margin-bottom: 30px;
            color: #333;
        }

        .chart-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin-bottom: 20px;
        }

        .chart-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
        }

        .chart-title {
            font-size: 18px;
            font-weight: bold;
            margin-bottom: 15px;
            color: #333;
            text-align: center;
        }

        .chart {
            width: 100%;
            height: 400px;
        }

        .full-width {
            grid-column: 1 / -1;
        }

        .stats-summary {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }

        .stat-item {
            text-align: center;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            font-size: 14px;
            color: #7f8c8d;
            margin-top: 5px;
        }

        .controls {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }

        button {
            background: #3498db;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 0 10px;
            font-size: 14px;
        }

        button:hover {
            background: #2980b9;
        }

        button.active {
            background: #e74c3c;
        }

        .range-btn {
            background: #95a5a6;
            margin: 0 5px;
            padding: 8px 16px;
            font-size: 12px;
        }

        .range-btn.active {
            background: #3498db;
        }

        .range-btn:hover {
            background: #2980b9;
        }

        .chart-controls {
            display: flex;
            justify-content: center;
            gap: 20px;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }

        .checkbox-label {
            display: flex;
            align-items: center;
            cursor: pointer;
            font-size: 14px;
            color: #333;
        }

        .checkbox-label input[type="checkbox"] {
            margin-right: 8px;
            transform: scale(1.2);
        }

        .checkbox-label span {
            user-select: none;
        }
    </style>
</head>

<body>
    <div class="container">
        <div class="header">
            <h1>交易统计仪表板</h1>
            <p>实时交易数据分析与可视化</p>
        </div>

        <div class="controls">
            <button onclick="refreshData()" id="refreshBtn">刷新数据</button>
            <button onclick="toggleAutoRefresh()" id="autoRefreshBtn">自动刷新: 关闭</button>
        </div>

        <div class="stats-summary">
            <div class="chart-title">今日交易概览</div>
            <div class="stats-grid" id="statsGrid">
                <!-- 统计数据将在这里动态生成 -->
            </div>
        </div>

        <div class="chart-container full-width">
            <div class="chart-title">每日盈亏趋势</div>
            <div class="chart-controls">
                <label class="checkbox-label">
                    <input type="checkbox" id="totalPnlCheck" checked onchange="updatePnlChartVisibility()">
                    <span>总盈亏</span>
                </label>
                <label class="checkbox-label">
                    <input type="checkbox" id="totalProfitCheck" onchange="updatePnlChartVisibility()">
                    <span>总盈利</span>
                </label>
                <label class="checkbox-label">
                    <input type="checkbox" id="totalLossCheck" onchange="updatePnlChartVisibility()">
                    <span>总亏损</span>
                </label>
            </div>
            <div id="pnlChart" class="chart"></div>
        </div>

        <div class="chart-container full-width">
            <div class="chart-title">24小时交易分布热力图</div>
            <div class="chart-controls">
                <label class="checkbox-label">
                    <span style="margin-right: 10px;">时间范围:</span>
                </label>
                <button onclick="changeDateRange(7)" id="range7" class="range-btn active">7天</button>
                <button onclick="changeDateRange(15)" id="range15" class="range-btn">15天</button>
                <button onclick="changeDateRange(30)" id="range30" class="range-btn">30天</button>
            </div>
            <div id="hourlyHeatmapChart" class="chart"></div>
        </div>




    </div>

    <script>
        // 全局变量
        let autoRefreshInterval = null;
        let isAutoRefresh = false;
        let currentDateRange = 7; // 默认7天

        // 初始化图表
        const pnlChart = echarts.init(document.getElementById('pnlChart'));
        const hourlyHeatmapChart = echarts.init(document.getElementById('hourlyHeatmapChart'));

        // 从API获取数据
        async function fetchDataFromAPI() {
            try {
                const response = await fetch(`/api/combined-stats?days=${currentDateRange}`);
                const result = await response.json();

                if (result.success) {
                    return result.data;
                } else {
                    console.error('API返回错误:', result.error);
                    return generateMockData(); // 如果API失败，使用模拟数据
                }
            } catch (error) {
                console.error('获取数据失败:', error);
                return generateMockData(); // 如果请求失败，使用模拟数据
            }
        }

        // 模拟数据生成函数（作为备用）
        function generateMockData() {
            const dailyStats = [];
            const hourlyStats = [];

            // 生成7天的每日统计数据
            for (let i = 6; i >= 0; i--) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];

                const totalOrders = Math.floor(Math.random() * 50) + 20;
                const profitOrders = Math.floor(totalOrders * (0.4 + Math.random() * 0.3));
                const lossOrders = totalOrders - profitOrders;

                dailyStats.push({
                    trade_date: dateStr,
                    total_orders: totalOrders,
                    profit_orders: profitOrders,
                    loss_orders: lossOrders,
                    total_pnl: (Math.random() - 0.4) * 1000,
                    total_profit: Math.random() * 800 + 200,
                    total_loss: -(Math.random() * 600 + 100),
                    win_rate_percent: (profitOrders / totalOrders * 100).toFixed(2),
                    profit_loss_ratio: (1 + Math.random() * 2).toFixed(2),
                    total_volume: Math.random() * 10000 + 5000,
                    total_fees: Math.random() * 100 + 50,
                    buy_orders: Math.floor(totalOrders * (0.4 + Math.random() * 0.2)),
                    sell_orders: Math.floor(totalOrders * (0.4 + Math.random() * 0.2)),
                    market_orders: Math.floor(totalOrders * 0.7),
                    limit_orders: Math.floor(totalOrders * 0.3)
                });
            }

            // 生成24小时交易分布数据
            for (let i = 0; i < 7; i++) {
                const date = new Date();
                date.setDate(date.getDate() - i);
                const dateStr = date.toISOString().split('T')[0];

                const hourlyData = { date: dateStr };
                for (let h = 0; h < 24; h++) {
                    hourlyData[`${h}h`] = Math.floor(Math.random() * 10);
                }
                hourlyStats.push(hourlyData);
            }

            return { dailyStats, hourlyStats };
        }

        // 更新统计概览
        function updateStatsOverview(data) {
            const today = data.dailyStats[0] || {};
            const statsGrid = document.getElementById('statsGrid');

            const stats = [
                { label: '盈利', value: today.profit_orders || 0, color: '#27ae60' },
                { label: '亏损', value: today.loss_orders || 0, color: '#e74c3c' },
                { label: '总盈亏', value: `${(today.total_pnl || 0).toFixed(2)}`, color: today.total_pnl >= 0 ? '#27ae60' : '#e74c3c' },
                { label: '胜率', value: `${today.win_rate_percent || 0}%`, color: '#f39c12' },
                { label: '盈亏比', value: today.profit_loss_ratio || 0, color: '#9b59b6' },
                { label: '交易量', value: (today.total_volume || 0).toFixed(0), color: '#34495e' },
            ];

            statsGrid.innerHTML = stats.map(stat => `
                <div class="stat-item">
                    <div class="stat-value" style="color: ${stat.color}">${stat.value}</div>
                    <div class="stat-label">${stat.label}</div>
                </div>
            `).join('');
        }
        // 存储图表数据
        let chartData = null;

        // 更新盈亏趋势图
        function updatePnlChart(data) {
            // 存储数据供后续使用
            chartData = data;

            // 按日期排序数据，确保时间顺序正确
            const sortedData = [...data.dailyStats].sort((a, b) => new Date(a.trade_date) - new Date(b.trade_date));

            const dates = sortedData.map(item => item.trade_date);
            const pnlData = sortedData.map(item => item.total_pnl);
            const profitData = sortedData.map(item => item.total_profit);
            const lossData = sortedData.map(item => item.total_loss);

            // 根据复选框状态决定显示哪些系列
            const series = [];
            const legendData = [];

            if (document.getElementById('totalPnlCheck').checked) {
                series.push({
                    name: '总盈亏',
                    type: 'line',
                    data: pnlData,
                    smooth: true,
                    itemStyle: { color: '#3498db' },
                    lineStyle: { width: 3 },
                    areaStyle: {
                        opacity: 0.3,
                        color: {
                            type: 'linear',
                            x: 0, y: 0, x2: 0, y2: 1,
                            colorStops: [
                                { offset: 0, color: 'rgba(52, 152, 219, 0.8)' },
                                { offset: 1, color: 'rgba(52, 152, 219, 0.1)' }
                            ]
                        }
                    },
                    symbol: 'circle',
                    symbolSize: 6
                });
                legendData.push('总盈亏');
            }

            if (document.getElementById('totalProfitCheck').checked) {
                series.push({
                    name: '总盈利',
                    type: 'line',
                    data: profitData,
                    smooth: true,
                    itemStyle: { color: '#27ae60' },
                    lineStyle: { width: 2 },
                    symbol: 'circle',
                    symbolSize: 4
                });
                legendData.push('总盈利');
            }

            if (document.getElementById('totalLossCheck').checked) {
                series.push({
                    name: '总亏损',
                    type: 'line',
                    data: lossData,
                    smooth: true,
                    itemStyle: { color: '#e74c3c' },
                    lineStyle: { width: 2 },
                    symbol: 'circle',
                    symbolSize: 4
                });
                legendData.push('总亏损');
            }

            const option = {
                tooltip: {
                    trigger: 'axis',
                    axisPointer: { type: 'cross' },
                    formatter: function (params) {
                        if (params.length === 0) return '';
                        let result = params[0].axisValue + '<br/>';
                        params.forEach(param => {
                            result += param.marker + param.seriesName + ': ' + param.value + '<br/>';
                        });
                        return result;
                    }
                },
                legend: {
                    data: legendData,
                    show: true
                },
                xAxis: {
                    type: 'category',
                    data: dates,
                    show: true
                },
                yAxis: {
                    type: 'value',
                    axisLabel: {
                        formatter: '{value}'
                    },
                    show: true
                },
                series: series,
                // 当没有数据系列时显示空状态
                graphic: series.length === 0 ? [{
                    type: 'text',
                    left: 'center',
                    top: 'middle',
                    style: {
                        text: '请选择要显示的数据系列',
                        fontSize: 16,
                        fill: '#999'
                    }
                }] : []
            };

            // 使用 setOption 的第二个参数确保完全替换配置
            pnlChart.setOption(option, true);
        }

        // 更新图表可见性
        function updatePnlChartVisibility() {
            if (chartData) {
                updatePnlChart(chartData);
            }
        }

        // 更新24小时热力图
        function updateHourlyHeatmapChart(data) {
            const hours = Array.from({ length: 24 }, (_, i) => `${i}:00`);
            // 按日期排序数据，确保时间顺序正确
            const sortedHourlyData = [...data.hourlyStats].sort((a, b) => new Date(a.date) - new Date(b.date));
            const days = sortedHourlyData.map(item => item.date);

            const heatmapData = [];
            let maxValue = 0;
            sortedHourlyData.forEach((dayData, dayIndex) => {
                for (let hour = 0; hour < 24; hour++) {
                    const value = dayData[`${hour}h`] || 0;
                    heatmapData.push([hour, dayIndex, value]);
                    maxValue = Math.max(maxValue, value);
                }
            });

            // 根据数据量动态调整图表高度和Y轴标签
            const dataCount = days.length;
            let chartHeight, yAxisLabelInterval, gridTop, gridBottom;

            if (dataCount <= 7) {
                chartHeight = '400px';
                yAxisLabelInterval = 0; // 显示所有标签
                gridTop = '10%';
                gridBottom = '20%';
            } else if (dataCount <= 15) {
                chartHeight = '600px';
                yAxisLabelInterval = 0;
                gridTop = '8%';
                gridBottom = '15%';
            } else {
                chartHeight = '800px';
                yAxisLabelInterval = Math.ceil(dataCount / 15); // 间隔显示标签
                gridTop = '5%';
                gridBottom = '10%';
            }

            // 动态调整图表容器高度
            document.getElementById('hourlyHeatmapChart').style.height = chartHeight;
            hourlyHeatmapChart.resize();

            const option = {
                tooltip: {
                    position: 'top',
                    formatter: function (params) {
                        return `${days[params.data[1]]} ${hours[params.data[0]]}<br/>交易次数: ${params.data[2]}`;
                    }
                },
                grid: {
                    height: `${100 - parseInt(gridTop) - parseInt(gridBottom)}%`,
                    top: gridTop,
                    bottom: gridBottom,
                    left: '10%',
                    right: '15%'
                },
                xAxis: {
                    type: 'category',
                    data: hours,
                    splitArea: {
                        show: true,
                        areaStyle: {
                            color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
                        }
                    },
                    axisLabel: {
                        fontSize: 11,
                        rotate: dataCount > 15 ? 45 : 0
                    }
                },
                yAxis: {
                    type: 'category',
                    data: days,
                    splitArea: {
                        show: true,
                        areaStyle: {
                            color: ['rgba(250,250,250,0.3)', 'rgba(200,200,200,0.3)']
                        }
                    },
                    axisLabel: {
                        fontSize: 11,
                        interval: yAxisLabelInterval,
                        formatter: function (value) {
                            // 对于长时间范围，只显示月-日
                            if (dataCount > 15) {
                                return value.substring(5); // 显示 MM-DD
                            }
                            return value;
                        }
                    },
                    inverse: true // 最新日期在顶部
                },
                visualMap: {
                    min: 0,
                    max: Math.max(maxValue, 10),
                    calculable: true,
                    orient: 'horizontal',
                    left: 'center',
                    bottom: '2%',
                    textStyle: {
                        fontSize: 11
                    },
                    inRange: {
                        color: ['#313695', '#4575b4', '#74add1', '#abd9e9', '#e0f3f8', '#ffffcc', '#fee090', '#fdae61', '#f46d43', '#d73027', '#a50026']
                    }
                },
                series: [{
                    name: '交易次数',
                    type: 'heatmap',
                    data: heatmapData,
                    label: {
                        show: dataCount <= 15, // 数据太多时隐藏标签
                        fontSize: 10
                    },
                    emphasis: {
                        itemStyle: {
                            shadowBlur: 10,
                            shadowColor: 'rgba(0, 0, 0, 0.5)'
                        }
                    }
                }]
            };

            hourlyHeatmapChart.setOption(option);
        }

        // 刷新所有数据
        async function refreshData() {
            const refreshBtn = document.getElementById('refreshBtn');
            refreshBtn.textContent = '刷新中...';
            refreshBtn.disabled = true;

            try {
                const data = await fetchDataFromAPI();

                updateStatsOverview(data);
                updateHourlyHeatmapChart(data);
                updatePnlChart(data);

            } catch (error) {
                console.error('刷新数据失败:', error);
            } finally {
                refreshBtn.textContent = '刷新数据';
                refreshBtn.disabled = false;
            }
        }

        // 切换自动刷新
        function toggleAutoRefresh() {
            const btn = document.getElementById('autoRefreshBtn');

            if (isAutoRefresh) {
                clearInterval(autoRefreshInterval);
                isAutoRefresh = false;
                btn.textContent = '自动刷新: 关闭';
                btn.classList.remove('active');
            } else {
                autoRefreshInterval = setInterval(refreshData, 30000); // 30秒刷新一次
                isAutoRefresh = true;
                btn.textContent = '自动刷新: 开启';
                btn.classList.add('active');
            }
        }

        // 切换日期范围
        function changeDateRange(days) {
            currentDateRange = days;

            // 更新按钮状态
            document.querySelectorAll('.range-btn').forEach(btn => btn.classList.remove('active'));
            document.getElementById(`range${days}`).classList.add('active');

            // 刷新数据
            refreshData();
        }

        // 响应式处理
        window.addEventListener('resize', function () {
            hourlyHeatmapChart.resize();
            pnlChart.resize();
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function () {
            refreshData();
        });
    </script>
</body>

</html>